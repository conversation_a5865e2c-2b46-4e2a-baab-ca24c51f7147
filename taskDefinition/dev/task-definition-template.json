{"family": "web-booking", "executionRoleArn": "arn:aws:iam::205700164111:role/ecs_task_execution_role", "memory": "512", "networkMode": "awsvpc", "placementConstraints": [], "cpu": "512", "volumes": [], "requiresCompatibilities": ["EC2"], "containerDefinitions": [{"name": "web-booking", "image": "205700164111.dkr.ecr.ap-northeast-1.amazonaws.com/${IMAGE_NAME}", "cpu": 512, "memoryReservation": 512, "portMappings": [{"protocol": "tcp", "containerPort": 3000, "hostPort": 3000}], "logConfiguration": {"logDriver": "fluentd", "options": {"fluentd-address": "fluentd.hogugu.com:24224", "tag": "web-booking"}}, "linuxParameters": {"initProcessEnabled": true}, "essential": true, "volumesFrom": []}]}