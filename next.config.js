const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig = {
  output: 'standalone',
  eslint: {
    dirs: ['.'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  swcMinify: true,
  poweredByHeader: false,
  reactStrictMode: true,
  pageExtensions: ['page.tsx', 'page.ts'],
  experimental: {
    scrollRestoration: true,
  },
  modularizeImports: {
    lodash: {
      transform: 'lodash/{{member}}',
    },
    hooks: {
      transform: 'hooks/{{member}}',
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: '**.com',
      },
    ],
    minimumCacheTTL: 600,
  },
  async rewrites() {
    return [
      {
        source: '/health',
        destination: '/api',
      },
      {
        source: '/server/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_SERVER_BASE_URL}/:path*`, 
      }
    ]
  },
  env: {
    NEXT_PUBLIC_API_SERVER_BASE_URL:
      process.env.NEXT_PUBLIC_API_SERVER_BASE_URL,
    NEXT_PUBLIC_DEPLOY_ENV: process.env.NEXT_PUBLIC_DEPLOY_ENV,
    NEXT_PUBLIC_DOMAIN: process.env.NEXT_PUBLIC_DOMAIN,
    NEXT_PUBLIC_LP_URL: process.env.NEXT_PUBLIC_LP_URL,
    NEXT_PUBLIC_LP_DOMAIN: process.env.NEXT_PUBLIC_LP_DOMAIN,
    NEXT_PUBLIC_DEEP_LINKING_URL: process.env.NEXT_PUBLIC_DEEP_LINKING_URL,
    GTM_ID: process.env.GTM_ID,
    GA_MEASUREMENT_ID: process.env.GA_MEASUREMENT_ID,
    GTM_AUTH: process.env.GTM_AUTH,
    GTM_PREVIEW: process.env.GTM_PREVIEW,
    GTM_COOKIES_WIN: process.env.GTM_COOKIES_WIN,
    NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
    FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
    FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
    FIREBASE_DATABASE_URL: process.env.FIREBASE_DATABASE_URL,
    FIREBASE_SENDER_ID: process.env.FIREBASE_SENDER_ID,
    FIREBASE_MEASUREMENT_ID: process.env.FIREBASE_MEASUREMENT_ID,
    GUEST_TOKEN: process.env.GUEST_TOKEN,
    NEXT_PUBLIC_GMO_BASE_URL: process.env.NEXT_PUBLIC_GMO_BASE_URL,
    NEXT_PUBLIC_GMO_SHOP_ID: process.env.NEXT_PUBLIC_GMO_SHOP_ID,
    NEXT_PUBLIC_ENABLE_ANNIVERSARY: process.env.NEXT_PUBLIC_ENABLE_ANNIVERSARY,
    NEXT_PUBLIC_DURATION_LIMIT_CHAT: process.env.NEXT_PUBLIC_DURATION_LIMIT_CHAT,
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/i,
      issuer: { and: [/\.(js|ts|md)x?$/] },
      use: [
        {
          loader: '@svgr/webpack',
          options: {
            prettier: false,
            svgo: true,
            svgoConfig: {
              plugins: [
                {
                  name: 'preset-default',
                  params: {
                    overrides: { removeViewBox: false, cleanupIds: false  },
                  },
                },
              ],
            },
            titleProp: true,
          },
        },
      ],
    });
    return config;
  }
}

module.exports = withBundleAnalyzer(nextConfig);
