/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000/',
  generateRobotsTxt: true,
  changefreq: 'daily',
  priority: 1,
  sitemapSize: 50000,
  exclude: [
    '/login',
    '/logout',
    '/register',
    '/register/**',
    '/my-page',
    '/my-page/**',
    '/booking',
    '/booking/**',
  ],
  robotsTxtOptions: {
    // Point to our custom sitemap API routes for each page
    additionalSitemaps: [
      ...Array.from(
        { length: process.env.NEXT_PUBLIC_DYNAMIC_SITEMAP_PAGES },
        (_, i) => {
          return `${process.env.NEXT_PUBLIC_DOMAIN}/api/sitemap.xml?page=${
            i + 1
          }`;
        }
      ),
    ],
  },
};
