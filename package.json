{"name": "hogugu-web-booking", "version": "2.1.1", "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap && node scripts/reformat-sitemap.js", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "export": "next export", "build-prod": "run-s clean build export", "clean": "rimraf .next out", "lint": "next lint --fix", "check-types": "tsc --noEmit --pretty", "prepare": "husky install", "sitemap": "next-sitemap && node scripts/reformat-sitemap.js"}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/server": "^11.10.0", "@fontsource/noto-sans-jp": "^4.5.12", "@hookform/resolvers": "^3.0.1", "@mantine/carousel": "^6.0.7", "@mantine/core": "^6.0.7", "@mantine/dates": "^6.0.7", "@mantine/hooks": "^6.0.7", "@mantine/modals": "^6.0.7", "@mantine/next": "^6.0.7", "@mantine/notifications": "^6.0.7", "@mantine/nprogress": "^6.0.7", "@tanstack/react-query": "^4.29.1", "@tanstack/react-query-devtools": "^4.29.1", "@total-typescript/ts-reset": "^0.4.2", "@twilio/voice-sdk": "^2.5.0", "axios": "^1.3.5", "card-validator": "^8.1.1", "cookies-next": "^2.1.1", "dayjs": "^1.11.7", "dotenv": "^16.5.0", "embla-carousel-react": "^7.1.0", "firebase": "^9.19.1", "framer-motion": "^10.12.16", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "next": "13.5.11", "next-seo": "^6.0.0", "next-sitemap": "^4.2.3", "pino": "^8.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-emoji-render": "^2.0.1", "react-hook-form": "^7.43.9", "react-image-crop": "^10.0.9", "react-indiana-drag-scroll": "^3.0.1-alpha", "react-intersection-observer": "^9.4.3", "react-intl": "^6.4.2", "react-number-format": "^5.1.4", "react-qr-code": "^2.0.11", "react-scroll": "^1.8.9", "react-truncate-markup": "^5.1.2", "react-zoom-pan-pinch": "^3.4.4", "sharp": "^0.32.0", "uuid": "^9.0.1", "yup": "^1.1.0", "zustand": "^4.3.7"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@next/bundle-analyzer": "^13.3.0", "@svgr/cli": "^7.0.0", "@svgr/webpack": "^7.0.0", "@types/gtag.js": "^0.0.14", "@types/lodash": "^4.14.192", "@types/node": "^18.15.11", "@types/react": "^18.0.35", "@types/react-scroll": "^1.8.6", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "eslint": "^8.38.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-next": "^13.3.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.1", "prettier": "^2.8.7", "typescript": "^5.0.4"}}