FROM node:16-alpine AS builder
WORKDIR /src
COPY package*.json ./
RUN npm pkg set scripts.prepare=":" && npm install   # Override prepare script by null command to ignore husky installation
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM node:16-alpine AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_SHARP_PATH /usr/local/lib/node_modules/sharp
RUN npm install --location=global sharp
RUN apk update && apk add --no-cache bash gettext nginx supervisor

RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

COPY package.json ./
COPY --from=builder /src/public ./public
COPY --from=builder --chown=nextjs:nodejs /src/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /src/.next/static ./.next/static
COPY config config
COPY scripts scripts

# set HOSTNAME to localhost to fix deploy issue
ENV HOSTNAME 0.0.0.0

ENV PORT 5050
EXPOSE 5000
CMD ["./scripts/entrypoint.sh"]
