<svg xmlns="http://www.w3.org/2000/svg" width="160" height="160" viewBox="0 0 160 160">
    <defs>
        <clipPath id="72baj3632a">
            <path data-name="Rectangle 37100" transform="translate(640 549)" style="fill:#fff" d="M0 0h160v160H0z"/>
        </clipPath>
    </defs>
    <g data-name="Mask Group 59" transform="translate(-640 -549)" style="clip-path:url(#72baj3632a)">
        <g data-name="Group 3558">
            <g data-name="Group 3556" transform="translate(0 -16)">
                <rect data-name="Rectangle 37097" width="130" height="50" rx="10" transform="translate(655 655)" style="fill:#e7eef3"/>
                <circle data-name="Ellipse 125" cx="10" cy="10" r="10" transform="translate(667 670)" style="fill:#bdccd3"/>
                <g data-name="Group 3554" transform="translate(-253 10)">
                    <rect data-name="Rectangle 37098" width="60" height="6" rx="3" transform="translate(951 660)" style="fill:#bdccd3"/>
                    <rect data-name="Rectangle 37099" width="40" height="6" rx="3" transform="translate(951 674)" style="fill:#bdccd3"/>
                </g>
            </g>
            <g data-name="Group 3557" transform="translate(0 4)">
                <circle data-name="Ellipse 124" cx="12" cy="12" r="12" transform="translate(652 583)" style="fill:#bdccd3"/>
                <g data-name="Rectangle 37094" transform="translate(640 565)" style="stroke:#bdccd3;stroke-width:4px;fill:none">
                    <rect width="160" height="60" rx="16" style="stroke:none"/>
                    <rect x="2" y="2" width="156" height="56" rx="14" style="fill:none"/>
                </g>
                <g data-name="Group 3555" transform="translate(-253 -22)">
                    <rect data-name="Rectangle 37095" width="83" height="6" rx="3" transform="translate(941 606)" style="fill:#bdccd3"/>
                    <rect data-name="Rectangle 37096" width="57" height="6" rx="3" transform="translate(941 623)" style="fill:#bdccd3"/>
                </g>
            </g>
        </g>
    </g>
</svg>
