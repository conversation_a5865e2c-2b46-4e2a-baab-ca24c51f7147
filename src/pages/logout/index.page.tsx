import Layout from 'components/Layout';
import { useLogout, useUser } from 'hooks';
import { NextSeo } from 'next-seo';
import React, { useEffect } from 'react';

const LogoutPage = () => {
  const { data: user } = useUser();
  const { logout } = useLogout();

  useEffect(() => {
    if (user) {
      logout();
    }
  }, [logout, user]);

  return (
    <>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: 'ログアウト | HOGUGU（ホググ）',
        }}
        title="ログアウト | HOGUGU（ホググ）"
      />
    </>
  );
};

LogoutPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default LogoutPage;
