import IconChevronRight from '@icons/icon-chevron-up.svg';
import {
  Box,
  Button,
  Collapse,
  Container,
  Flex,
  Group,
  Text,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { OtpVerifyForm, PhoneForm } from 'components/Auth';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';
import Layout from 'components/Layout';
import { useAuthSignInWithPhoneNumber, useMutate, useUser } from 'hooks';
import { get } from 'lodash';
import type {
  CheckPhonePayload,
  ICheckPhone,
  SignInPayload,
} from 'models/auth';
import authQuery from 'models/auth/query';
import type { GetServerSideProps, NextApiRequest, NextApiResponse } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { useState } from 'react';
import { FIREBASE_AUTH_ERRORS } from 'utils/constants';
import { auth, generateRecaptcha } from 'utils/firebase';
import helpers, { eventLog } from 'utils/helpers';
import notification from 'utils/notification';
import { suffixTitle } from 'utils/seoConfig';
import type { UserTokenCookie } from 'utils/type';

import { styles, sx } from './styles';

const LoginPage = () => {
  const router = useRouter();
  const [opened, { toggle }] = useDisclosure(true);
  const [verifyBy, setVerifyBy] = useState<'sms' | 'call'>('sms');
  const [step, setStep] = useState<Number>(1);
  const [isVerifying, setIsVerifying] = useState(false);
  const { refetch, isFetching } = useUser();

  const { mutateAsync: login, isLoading: isLogin } = useMutate<
    SignInPayload,
    UserTokenCookie
  >(authQuery.login);

  const { mutateAsync: sendCallOtp } = useMutate<
    CheckPhonePayload,
    ICheckPhone
  >(authQuery.checkPhone);

  const {
    isLoading: isSendingOtp,
    mutateAsync: sendOtp,
    data: confirmationResult,
    variables: sendOtpData,
  } = useAuthSignInWithPhoneNumber(auth);

  const verifyAccount = async () => {
    const { data: userData } = await refetch();
    if (userData?.isCompletedProfile) {
      eventLog('login');
    } else {
      eventLog('sign_up');
    }
    router.push(
      router.query.referer && typeof router.query.referer === 'string'
        ? router.query.referer
        : '/',
    );
  };

  const sendSmsOtp = async (phone: string) => {
    try {
      generateRecaptcha();
      await sendOtp({
        phoneNumber: phone,
        appVerifier: window.recaptchaVerifier,
      });
      setStep(2);
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] ||
        'システムエラーです。申し訳ございません。1つ前の画面に戻り、電話番号を再度送信してください。';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
      throw e;
    }
  };

  const handleStep1 = async (data: ICheckPhone) => {
    // Special case dev phone
    if (data.token && data.total) {
      helpers.setToken({ token: data.token });
      await verifyAccount();
      return;
    }
    // Phone isn't registered
    if (!data.total) {
      notification.show({
        type: 'error',
        message:
          'アカウントが見つかりませんでした。電話番号をお確かめください。',
      });
      return;
    }
    if (data.total && data.phone) {
      await sendSmsOtp(data.phone);
    }
  };

  const handleStep2 = async (values: OtpVerifyFormValues) => {
    try {
      if (!sendOtpData?.phoneNumber) return;
      setIsVerifying(true);
      let code: string;
      if (verifyBy === 'call') {
        code = values.code;
      } else {
        const result = await confirmationResult?.confirm(values.code);
        code = result?.user.uid || '';
      }
      const data = await login({
        code,
        type: verifyBy,
        phone: sendOtpData.phoneNumber,
      });
      helpers.setToken(data);
      await verifyAccount();
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] || 'Something went wrong...';
      if (String(errorCode).includes('auth'))
        notification.show({
          type: 'error',
          message: errorMessage,
        });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendOtp = async (type: 'sms' | 'call') => {
    if (!sendOtpData?.phoneNumber) return;
    setVerifyBy(type);
    if (type === 'call') {
      // Resend OTP via call
      const oPhone = helpers.transfromPhone(sendOtpData.phoneNumber);
      await sendCallOtp({
        ...oPhone,
        type: 'call',
      });
      return;
    }
    // Resend OTP via sms
    await sendSmsOtp(sendOtpData.phoneNumber);
  };

  const renderContent = () => {
    if (step === 2) {
      return (
        <OtpVerifyForm
          initialValues={{ code: '' }}
          isLoading={isLogin || isVerifying || isFetching}
          isLogin
          onResendOtp={handleResendOtp}
          onSubmit={handleStep2}
        />
      );
    }
    return (
      <PhoneForm
        isLogin
        label="ログイン"
        loading={isSendingOtp || isFetching}
        onSubmit={handleStep1}
        placeholder="携帯電話番号"
      />
    );
  };

  return (
    <Container
      size={1180}
      sx={{
        padding: '80px 20px',
        '@media (max-width: 768px)': {
          padding: '50px 20px 10px',
        },
      }}
    >
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `ログイン | ${suffixTitle}`,
        }}
        title={`ログイン | ${suffixTitle}`}
      />
      {renderContent()}
      <Flex direction={step === 1 ? 'column' : 'column-reverse'}>
        <Flex
          gap={20}
          justify={step === 1 ? 'center' : 'space-between'}
          mb={step === 1 ? 0 : 30}
          mt={step === 1 ? 20 : 0}
          sx={sx.btnGroup}
        >
          {step === 2 && (
            <Button
              className="cancel-btn"
              color="grey"
              onClick={() => setStep(1)}
              size="lg"
              variant="outline"
            >
              戻って再送信
            </Button>
          )}
          <Button
            className="register-btn"
            color="marigold"
            component={Link}
            href="/register"
            size="lg"
          >
            新規会員登録
          </Button>
        </Flex>
        <Box sx={sx.moreInfo}>
          <Group>
            <Button
              className={opened ? 'opened' : ''}
              onClick={toggle}
              rightIcon={<IconChevronRight />}
              styles={styles.buttonCollapse}
              variant="variant"
            >
              ログインできない場合
            </Button>
          </Group>

          <Collapse in={opened}>
            <Text>
              <p className="collapse-content">
                ・会員登録した携帯電話番号の解約・譲渡・名義変更などによりログイン時に必要な認証コードを受け取れず
                <br />
                ログインできなくなる恐れがあります。事前に運営事務局へ「設定した携帯電話番号の変更希望」をご連絡ください。
              </p>

              <p>
                &lt;事後の場合 &gt;
                <br />
                ・古い携帯電話番号でご予約(リクエスト予約、確定予約)がある場合は、運営事務局へご連絡ください。
                <br />
                ・古い携帯電話番号でご予約中のものがない場合、今までのご利用履歴を引き継がれる場合は、運営事務局へご連絡ください。
              </p>

              <p>・引き継がれない場合は、新規会員登録よりご利用ください。</p>
            </Text>
          </Collapse>
        </Box>
      </Flex>
    </Container>
  );
};

LoginPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const webToken = helpers.getWebCookie(
    req as NextApiRequest,
    res as NextApiResponse,
  );
  if (webToken.token) {
    return {
      redirect: {
        destination: '/',
        permanent: true,
      },
    };
  }
  return {
    props: {},
  };
};

export default LoginPage;
