import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  moreInfo: (theme: MantineTheme) => ({
    marginTop: 62,
    fontSize: 16,
    color: theme.colors.blackOlive,
    '@media (max-width: 768px)': {
      marginTop: 25,
      fontSize: 14,
    },
    'p:not(last-child)': {
      marginBottom: 40,
      '@media (max-width: 768px)': {
        marginBottom: 30,
      },
    },
  }),
  btnGroup: {
    'button, a': {
      maxWidth: 300,
      width: '100%',
      '@media (max-width: 768px)': {
        maxWidth: 180,
      },
    },
  },
};

export const styles: Record<string, any> = {
  buttonCollapse: (theme: MantineTheme) => ({
    root: {
      padding: 0,
      fontSize: 16,
      marginBottom: 42,
      color: theme.colors.queenBlue[6],
      borderBottom: `1px solid ${theme.colors.queenBlue[6]}`,
      boxShadow: 'none !important',
      borderRadius: 0,
      '&:hover': {
        backgroundImage: 'none',
      },
      '@media (max-width: 768px)': {
        margin: '0 auto 10px',
        fontSize: 14,
      },
    },
    rightIcon: {
      transition: 'all 0.5s',
      width: 12,
      '.opened &': {
        transform: 'rotate(180deg)',
      },
    },
  }),
};
