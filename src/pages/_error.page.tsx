import ErrorComponent from 'components/Error';
import Layout from 'components/Layout';

function Error({ statusCode }: { statusCode: number }) {
  return <ErrorComponent statusCode={statusCode} />;
}

Error.getLayout = (page: React.ReactNode) => {
  return <Layout error>{page}</Layout>;
};

Error.getInitialProps = ({ res, err }: any) => {
  if (res?.statusCode) {
    return { statusCode: res.statusCode };
  }
  if (err?.statusCode) {
    return { statusCode: err.statusCode };
  }
  return { statusCode: 404 };
};

export default Error;
