import { Container } from '@mantine/core';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';
import Layout from 'components/Layout';
import { Step1, Step2, Step3 } from 'components/RevokeAccount';
import { useAuthSignInWithPhoneNumber, useMutate, useUser } from 'hooks';
import { get } from 'lodash';
import type { ICustomer } from 'models/auth';
import { authQuery } from 'models/auth';
import type { GetServerSideProps } from 'next';
import { NextSeo } from 'next-seo';
import { useState } from 'react';
import { FIREBASE_AUTH_ERRORS } from 'utils/constants';
import dayjs from 'utils/dayjs';
import { auth, generateRecaptcha } from 'utils/firebase';
import helpers from 'utils/helpers';
import notification from 'utils/notification';
import request from 'utils/request';

import { sx } from './styles';

const RevokeAccountPage = () => {
  const { data: user } = useUser();
  const [step, setStep] = useState(1);
  const [isVerifying, setIsVerifying] = useState(false);
  const expiration = Math.ceil(
    dayjs(user?.requestingDeleteAccount?.deletedAt).diff(dayjs(), 'd', true),
  );

  const { mutateAsync: revokeDeleteFn, isLoading } = useMutate<
    unknown,
    unknown
  >(authQuery.revokeDeleteAccount);

  const handleSubmitOtp = () => {
    revokeDeleteFn(
      {},
      {
        onSuccess: () => setStep(3),
      },
    );
  };

  const { mutateAsync: sendOtp, data: confirmationResult } =
    useAuthSignInWithPhoneNumber(auth);

  const sendOTP = async () => {
    try {
      generateRecaptcha();
      await sendOtp({
        phoneNumber: user!.phone,
        appVerifier: window.recaptchaVerifier,
      });
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] ||
        'システムエラーです。申し訳ございません。1つ前の画面に戻り、電話番号を再度送信してください。';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
      throw e;
    }
  };

  const handleSubmit = async (values: OtpVerifyFormValues) => {
    try {
      setIsVerifying(true);
      const result = await confirmationResult?.confirm(values.code);
      if (result) {
        revokeDeleteFn(
          {},
          {
            onSuccess: () => setStep(3),
          },
        );
      }
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] || 'Something went wrong...';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <Container size={1140} sx={sx.wrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: 'アカウント停止中 | HOGUGU（ホググ）',
        }}
        title="アカウント停止中 | HOGUGU（ホググ）"
      />
      {(() => {
        switch (step) {
          case 1:
            return (
              <Step1
                expiration={expiration}
                onRevokeAccount={() => {
                  if (user?.isTesting) {
                    handleSubmitOtp();
                    return;
                  }
                  setStep(2);
                  sendOTP();
                }}
              />
            );
          case 2:
            return (
              <Step2
                backFn={() => setStep(1)}
                isLoading={isLoading || isVerifying}
                onSubmit={handleSubmit}
                sendOTP={sendOTP}
              />
            );
          default:
            return <Step3 />;
        }
      })()}
    </Container>
  );
};

RevokeAccountPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  try {
    const { data } = await request<ICustomer>({
      url: '/account-api/mobile/customer/me',
      method: 'GET',
      config: helpers.getTokenConfig(req, res),
    });
    if (!data.requestingDeleteAccount) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }
    return {
      props: {},
    };
  } catch {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }
};

export default RevokeAccountPage;
