import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  wrapper: (_theme: MantineTheme) => ({
    paddingTop: 80,
    paddingBottom: 143,
    fontSize: 16,
    color: _theme.colors.blackOlive,
    h3: {
      fontSize: 30,
      marginBottom: 30,
    },
    h4: {
      fontSize: 24,
      marginBottom: 16,
    },
    '@media (max-width: 768px)': {
      paddingTop: 50,
      paddingBottom: 50,
      fontSize: 14,
      h3: {
        fontSize: 22,
        marginBottom: 34,
      },
      h4: {
        fontSize: 16,
        marginBottom: 20,
      },
    },
  }),
};
