import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Container, Group, Title } from '@mantine/core';
import type { CompleteProfileFormValues } from 'components/Auth/CompleteProfileForm/schema';
import { schema } from 'components/Auth/CompleteProfileForm/schema';
import Layout from 'components/Layout';
import { ProfileFields } from 'components/Profile';
import { useMutate, useUser } from 'hooks';
import { pick as _pick } from 'lodash';
import { authQuery } from 'models/auth';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';
import { suffixTitle } from 'utils/seoConfig';

import { sx } from './styles';

const UpdateProfile = () => {
  const router = useRouter();
  const { data: user, refetch: refetchUserData } = useUser();
  const { mutateAsync: updateProfile, isLoading } = useMutate(
    authQuery.updateProfile,
  );
  const initialValues = {
    ..._pick(user, ['name', 'email', 'phone']),
    birthday: user?.birthday ? dayjs(user?.birthday) : '',
    gender: user?.gender?.toString() || '',
    profilePicture: user?.profilePicture?.url,
  } as CompleteProfileFormValues;
  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, isValid },
  } = useForm<CompleteProfileFormValues>({
    defaultValues: initialValues,
    mode: 'onBlur',
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    reset(initialValues);
  }, [user]);

  const formValues = watch();

  const onSubmit = (values: CompleteProfileFormValues) => {
    const params = helpers.removeEmpty(values as CompleteProfileFormValues, [
      'phone',
    ]) as CompleteProfileFormValues;
    updateProfile(
      {
        ...params,
        gender: params?.gender ? Number(params.gender) : undefined,
        birthday: dayjs(params.birthday).startOf('d').toISOString(),
      },
      {
        onSuccess: async () => {
          refetchUserData();
          router.push('/my-page/profile-edit/complete');
        },
      },
    );
  };

  return (
    <Container size={1140} sx={sx.profileWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `プロフィール変更 | ${suffixTitle}`,
        }}
        title={`プロフィール変更 | ${suffixTitle}`}
      />
      <Title order={3}>プロフィールの変更</Title>
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={sx.completeProfileFormWrapper}
      >
        <ProfileFields control={control} formValues={formValues} />
        <Group position="apart">
          <Button
            color="grey"
            component={Link}
            href="/my-page"
            loading={isLoading}
            size="lg"
            sx={sx.normalBtn}
            variant="outline"
          >
            マイページへ戻る
          </Button>
          <Button
            color="marigold"
            disabled={!isValid || !isDirty}
            loading={isLoading}
            size="lg"
            sx={sx.normalBtn}
            type="submit"
          >
            登録する
          </Button>
        </Group>
      </Box>
    </Container>
  );
};

UpdateProfile.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default UpdateProfile;
