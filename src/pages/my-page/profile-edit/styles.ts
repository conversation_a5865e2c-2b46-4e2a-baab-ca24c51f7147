import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  profileWrapper: {
    paddingTop: 80,
    paddingBottom: 80,
    h3: {
      fontSize: 30,
      marginBottom: 58,
    },
    '@media (max-width: 768px)': {
      paddingTop: 50,
      paddingBottom: 40,
      h3: {
        fontSize: 22,
        marginBottom: 28,
      },
    },
  },

  normalBtn: {
    minWidth: 300,
    marginTop: 83,
    '@media (max-width: 768px)': {
      minWidth: 148,
      marginTop: 30,
    },
  },
};
