import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  profileWrapper: {
    paddingTop: 80,
    paddingBottom: 80,
    h3: {
      fontSize: 30,
    },
    '@media (max-width: 768px)': {
      paddingTop: 50,
      paddingBottom: 40,
      h3: {
        fontSize: 22,
      },
    },
  },

  status: {
    fontSize: 16,
    marginTop: 20,
    marginBottom: 58,
    '@media (max-width: 768px)': {
      marginTop: 12,
      marginBottom: 28,
    },
  },

  contentWrapper: {
    '@media (max-width: 768px)': {
      flexWrap: 'wrap',
      justifyContent: 'center',
    },
  },
};

export const styles: Record<string, any> = {
  avatar: {
    root: {
      minWidth: 200,
      height: 200,
      marginRight: 150,
      '@media (max-width: 768px)': {
        minWidth: 120,
        height: 120,
        marginRight: 0,
        marginBottom: 37,
      },
    },
  },

  content: (theme: MantineTheme) => ({
    root: {
      fontSize: 18,
      color: theme.colors.blackOlive,
      display: 'flex',
      flexWrap: 'wrap',
      gap: '20px 50px',
      paddingBottom: 42,
      width: '100%',
      '@media (max-width: 768px)': {
        fontSize: 14,
        gap: '10px 0',
        paddingBottom: 10,
        width: '100%',
      },
    },
    item: {
      maxWidth: 330,
      width: '100%',
      paddingBottom: 20,
      borderBottom: '2px dashed #b1b1b1',
      '&:last-child': {
        borderBottom: 'none',
      },
      '&[data-cols="2"]': {
        maxWidth: 590,
        flexGrow: 2,
      },
      b: {
        fontSize: 20,
        color: '#070203',
      },
      span: {
        display: 'block',
        wordBreak: 'break-all',
      },
      '@media (max-width: 768px)': {
        maxWidth: '50%',
        borderBottom: 'none',
        b: {
          fontSize: 16,
        },
      },
    },
  }),

  editBtn: {
    root: {
      maxWidth: 300,
      width: '100%',
      '@media (max-width: 768px)': {
        maxWidth: 148,
      },
    },
  },

  backBtn: {
    root: {
      maxWidth: 300,
      width: '100%',
      '@media (max-width: 768px)': {
        maxWidth: 148,
      },
    },
  },
};
