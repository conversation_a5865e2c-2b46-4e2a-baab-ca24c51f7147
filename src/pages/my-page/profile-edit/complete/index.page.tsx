import {
  Avatar,
  Button,
  Container,
  Flex,
  Group,
  List,
  Text,
  Title,
} from '@mantine/core';
import Layout from 'components/Layout';
import { useUser } from 'hooks';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import { GENDER } from 'utils/constants';
import dayjs from 'utils/dayjs';

import { styles, sx } from './styles';

const GENDER_AVATAR = [
  '/icons/icon-avatar-other.svg',
  '/icons/icon-avatar-male.svg',
  '/icons/icon-avatar-female.svg',
];

const ProfilePage = () => {
  const { data: user } = useUser();

  return (
    <Container size={1140} sx={sx.profileWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: 'プロフィール変更完了 | HOGUGU（ホググ）',
        }}
        title="プロフィール変更完了 | HOGUGU（ホググ）"
      />
      <Title order={3}>プロフィール変更完了</Title>
      <Text color="blackOlive" sx={sx.status}>
        変更しました。
      </Text>
      <Flex sx={sx.contentWrapper}>
        <Avatar
          alt={user?.name}
          radius="50%"
          src={
            user?.profilePicture?.url ||
            GENDER_AVATAR[Number(user?.gender) || 0]
          }
          styles={styles.avatar}
        />
        <List listStyleType="none" styles={styles.content}>
          <List.Item>
            <b>登録名</b>
            <span>{user?.name}</span>
          </List.Item>
          <List.Item>
            <b>性別</b>
            <span>{GENDER[user?.gender || 0]}</span>
          </List.Item>
          <List.Item>
            <b>生年月日</b>
            <span>
              {user?.birthday
                ? dayjs(user?.birthday).format('YYYY/MM/DD')
                : '-'}
            </span>
          </List.Item>
          <List.Item>
            <b>電話番号</b>
            <span>{user?.phone}</span>
          </List.Item>
          <List.Item data-cols={2}>
            <b>メールアドレス</b>
            <span>{user?.email || '-'}</span>
          </List.Item>
        </List>
      </Flex>
      <Group position="apart">
        <Button
          color="grey"
          component={Link}
          href="/my-page/profile-edit"
          size="lg"
          styles={styles.editBtn}
          variant="outline"
        >
          変更入力に戻る
        </Button>
        <Button
          component={Link}
          href="/my-page"
          size="lg"
          styles={styles.backBtn}
        >
          マイページ
        </Button>
      </Group>
    </Container>
  );
};

ProfilePage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ProfilePage;
