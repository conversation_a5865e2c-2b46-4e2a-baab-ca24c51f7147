import IconAccountSetting from '@icons/icon-account-setting.svg';
import IconAvatar from '@icons/icon-avatar.svg';
import IconCard from '@icons/icon-card.svg';
import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconClock from '@icons/icon-clock.svg';
import IconLogout from '@icons/icon-logout.svg';
import IconMenu from '@icons/icon-menu.svg';
import IconPointArrowSidebar from '@icons/icon-point-arrow-sidebar.svg';
import {
  Box,
  Container,
  Flex,
  Group,
  NavLink,
  Skeleton,
  Text,
  Title,
} from '@mantine/core';
import Layout from 'components/Layout';
import { NotificationList } from 'components/Notifications';
// import ReferralCouponLinkButton from 'components/ReferralCouponLinkButton';
import { useFetchData, useGlobalState, useLogout } from 'hooks';
import type { ITotalPoints } from 'models/booking';
import { bookingQuery } from 'models/booking';
import type { IConfigurationItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import Image from 'next/image';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import { IntlProvider } from 'react-intl';
import helpers from 'utils/helpers';
import { suffixTitle } from 'utils/seoConfig';

import { styles, sx } from './styles';

const MyPage = () => {
  const { logout: logoutFn } = useLogout();
  const { firebaseUser } = useGlobalState();
  const { data: configurations, isLoading } = useFetchData<
    IConfigurationItem[],
    {
      [key: string]: IConfigurationItem;
    }
  >({
    ...resourceQuery.getConfigs,
    select: (response) => {
      const objectKeyMap: Record<string, IConfigurationItem> = {};
      response.forEach((item) => {
        objectKeyMap[item.key] = item;
      });
      return objectKeyMap;
    },
  });
  const { data: totalPoints, isLoading: isLoadingTotalPoints } =
    useFetchData<ITotalPoints>(bookingQuery.getTotalPoints);

  return (
    <IntlProvider locale="ja" onError={() => {}}>
      <Container size={1140}>
        <NextSeo
          nofollow
          noindex
          openGraph={{
            title: `マイページ | ${suffixTitle}`,
          }}
          title={`マイページ | ${suffixTitle}`}
        />
        <Flex align="flex-start" sx={sx.mypageWrapper}>
          <Group sx={sx.sidebarWrapper}>
            <Title order={3}>マイページ</Title>
            <Title order={4}>会員情報</Title>
            {/* <PointLinkButton /> */}
            <Box
              component={Link}
              href="/my-page/points"
              maw={{ base: '100%', sm: 315 }}
              pos="relative"
              sx={{ borderRadius: 4 }}
              w="100%"
            >
              <Image
                alt="Sidebar point background"
                draggable={false}
                height={0}
                priority
                quality={100}
                sizes="100vw"
                src="/images/point-bg-sidebar.png"
                style={{
                  width: '100%',
                  height: 'auto',
                  borderRadius: 4,
                  position: 'absolute',
                }}
                width={0}
              />
              <Group pos="relative" position="apart" px={16} py={6} spacing={6}>
                <Flex align="center" gap={{ base: 16, sm: 12 }}>
                  <img
                    alt="Point gift sidebar"
                    draggable={false}
                    sizes="100vw"
                    src="/images/point-gift-sidebar.png"
                    style={{
                      width: 73,
                      height: 58,
                      borderRadius: 4,
                    }}
                  />
                  <Flex direction="column" gap={{ base: 4, sm: 2 }}>
                    <Text c="#070203" fz={12} lh="16px">
                      ホググポイント
                    </Text>
                    {isLoadingTotalPoints ? (
                      <Skeleton h={26} w="100%" />
                    ) : (
                      <Text c="#070203" fw={700} fz={18} lh="26px">
                        {helpers.numberFormat(totalPoints?.availablePoints)}
                      </Text>
                    )}
                  </Flex>
                </Flex>
                <IconPointArrowSidebar />
              </Group>
            </Box>
            {/* <ReferralCouponLinkButton /> */}
            {isLoading ? (
              <Skeleton
                h={77}
                sx={{ borderRadius: '4px' }}
                w={{ base: '100%', sm: '315px' }}
              />
            ) : (
              <Box
                component={Link}
                href="/my-page/coupons"
                maw={{ base: '100%', sm: '315px' }}
                pos="relative"
                sx={{ borderRadius: '4px', border: 'solid 1px #bdccd3' }}
                w="100%"
              >
                <Image
                  alt="Coupon banner"
                  draggable={false}
                  height={0}
                  priority
                  quality={100}
                  sizes="100vw"
                  src={
                    configurations?.referralCampain?.banner ||
                    '/images/coupon-banner.png'
                  }
                  style={{ width: '100%', height: 'auto', borderRadius: '4px' }}
                  width={0}
                />
              </Box>
            )}
            <Box sx={sx.sidebar}>
              <NavLink
                component={Link}
                href="/my-page/profile-edit"
                icon={<IconAvatar />}
                label="プロフィール変更"
                rightSection={<IconChevronRight />}
                styles={styles.menuItem}
              />
              <NavLink
                component={Link}
                href="/my-page/booking-history"
                icon={<IconClock />}
                label="予約履歴"
                rightSection={
                  <>
                    {!!firebaseUser?.bookingsHaveNewMessages && (
                      <Text className="red-dot" />
                    )}
                    <IconChevronRight />
                  </>
                }
                styles={styles.menuItem}
              />
              <NavLink
                component={Link}
                href="/my-page/cards"
                icon={<IconCard />}
                label="お支払い方法"
                rightSection={<IconChevronRight />}
                styles={styles.menuItem}
              />
              <NavLink
                component={Link}
                href="/my-page/delete-account"
                icon={<IconAccountSetting />}
                label="退会申請"
                rightSection={<IconChevronRight />}
                styles={styles.menuItem}
              />
              <NavLink
                component={Link}
                href={'/terms'}
                icon={<IconMenu />}
                label="各種規約"
                rightSection={<IconChevronRight />}
                styles={styles.menuItem}
              />
              <NavLink
                icon={<IconLogout />}
                label="ログアウト"
                onClick={() => logoutFn()}
                rightSection={<IconChevronRight />}
                styles={styles.menuItem}
              />
            </Box>
          </Group>
          <Box sx={sx.content}>
            <NotificationList />
          </Box>
        </Flex>
      </Container>
    </IntlProvider>
  );
};

MyPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default MyPage;
