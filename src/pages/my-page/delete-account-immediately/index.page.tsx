import { Container } from '@mantine/core';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';
import { Step1, Step2 } from 'components/DeletingAccountImmediately';
import Layout from 'components/Layout';
import { useAuthSignInWithPhoneNumber, useMutate, useUser } from 'hooks';
import { get } from 'lodash';
import type { ICustomer } from 'models/auth';
import { authQuery } from 'models/auth';
import type { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { useEffect, useState } from 'react';
import { FIREBASE_AUTH_ERRORS } from 'utils/constants';
import { auth, generateRecaptcha } from 'utils/firebase';
import helpers from 'utils/helpers';
import notification from 'utils/notification';
import request from 'utils/request';

import { sx } from './styles';

const DeleteAccountPage = () => {
  const router = useRouter();
  const { data: user } = useUser();
  const [isVerifying, setIsVerifying] = useState(false);
  const [step, setStep] = useState(user?.isTesting ? 2 : 1);

  const { mutateAsync: deleteFn, isLoading } = useMutate<unknown, unknown>(
    authQuery.deleteAccountImmediately,
  );

  const handleSubmitOtp = () => {
    deleteFn(
      {},
      {
        onSuccess: () => {
          setStep(2);
        },
      },
    );
  };

  const { mutateAsync: sendOtp, data: confirmationResult } =
    useAuthSignInWithPhoneNumber(auth);

  const sendOTP = async () => {
    try {
      generateRecaptcha();
      await sendOtp({
        phoneNumber: user!.phone,
        appVerifier: window.recaptchaVerifier,
      });
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] ||
        'システムエラーです。申し訳ございません。1つ前の画面に戻り、電話番号を再度送信してください。';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
      throw e;
    }
  };

  const handleSubmit = async (values: OtpVerifyFormValues) => {
    try {
      setIsVerifying(true);
      const result = await confirmationResult?.confirm(values.code);
      if (result) {
        handleSubmitOtp();
      }
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] || 'Something went wrong...';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    if (user?.isTesting) {
      handleSubmitOtp();
    } else if (user?.phone) {
      sendOTP();
    }
  }, [user]);

  return (
    <Container size={1140} sx={sx.wrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: 'アカウント削除 | HOGUGU（ホググ）',
        }}
        title="アカウント削除 | HOGUGU（ホググ）"
      />
      {(() => {
        switch (step) {
          case 1:
            return (
              <Step1
                backFn={() => router.push('/my-page/revoke-account')}
                isLoading={isLoading || isVerifying}
                onSubmit={handleSubmit}
                sendOTP={sendOTP}
              />
            );
          default:
            return <Step2 />;
        }
      })()}
    </Container>
  );
};

DeleteAccountPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  try {
    const { data } = await request<ICustomer>({
      url: '/account-api/mobile/customer/me',
      method: 'GET',
      config: helpers.getTokenConfig(req, res),
    });
    if (!data.requestingDeleteAccount) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }
    return {
      props: {},
    };
  } catch {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    };
  }
};

export default DeleteAccountPage;
