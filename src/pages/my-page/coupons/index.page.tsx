import IconCopy from '@icons/icon-copy.svg';
import IconCouponTicket from '@icons/icon-coupon-ticket.svg';
import {
  BackgroundImage,
  Box,
  Button,
  Container,
  CopyButton,
  Flex,
  Loader,
  Skeleton,
  Text,
  Title,
} from '@mantine/core';
import Layout from 'components/Layout';
import { useFetchData } from 'hooks';
import type { IInvitationCode } from 'models/auth';
import { authQuery } from 'models/auth';
import type { IConfigurationItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import Image from 'next/image';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import React from 'react';
import notification from 'utils/notification';
import { suffixTitle } from 'utils/seoConfig';

import useStyles from './styles';

const MyCoupons = () => {
  const { classes } = useStyles();

  const { data } = useFetchData<IInvitationCode>({
    ...authQuery.inviterGetInvitationCode,
    enabled: true,
  });
  const { data: incentiveCoupon, isLoading: isLoadingIncentiveCoupon } =
    useFetchData<{
      id: string;
      code: string;
      usageLimit: number;
    }>(authQuery.getIncentiveCoupon);
  const { data: configurations, isLoading } = useFetchData<
    IConfigurationItem[],
    {
      [key: string]: IConfigurationItem;
    }
  >({
    ...resourceQuery.getConfigs,
    select: (response) => {
      const objectKeyMap: Record<string, IConfigurationItem> = {};
      response.forEach((item) => {
        objectKeyMap[item.key] = item;
      });
      return objectKeyMap;
    },
  });

  const renderCoupon = () => {
    if (isLoadingIncentiveCoupon) {
      return (
        <Box bg="white" py={64} ta="center">
          <Loader size="lg" />
        </Box>
      );
    }
    if (incentiveCoupon) {
      return (
        <Box
          bg="white"
          className={classes.itemCard}
          px={10}
          py={{ base: 24, sm: 32 }}
        >
          <Text
            color="black"
            fw="bold"
            fz={{ base: 16, sm: 18 }}
            lh={1}
            mb={16}
          >
            プレゼントクーポン
          </Text>
          <Text color="sonicSilver" fw={500} lh={1} mb={24}>
            利用可能クーポン数:
            <Text component="span" fw="bold" ml={8}>
              {incentiveCoupon.usageLimit || 0}枚
            </Text>
          </Text>
          <Flex
            align="stretch"
            bg="#c7d5e1"
            maw={{ base: 251, sm: 350 }}
            mx="auto"
            p={2}
            sx={{ borderRadius: '3px' }}
            w="100%"
          >
            <Box
              bg="white"
              className={classes.codeInputWrapper}
              component="label"
              px={16}
              py={{ base: 15, sm: 16 }}
              sx={{
                borderRight: '2px solid #c7d5e1',
              }}
            >
              <Box
                color="richBlack"
                component="input"
                fz={{ base: 16, sm: 18 }}
                onClick={(e) => {
                  if (e.target) {
                    (e.target as any).select();
                  }
                }}
                readOnly
                type="text"
                value={incentiveCoupon.code}
              />
            </Box>
            <Box className={classes.copyBtnWrapper}>
              <CopyButton value={incentiveCoupon.code}>
                {({ copy }) => (
                  <Button
                    bg="ghostWhite"
                    color="queenBlue"
                    fullWidth
                    onClick={() => {
                      copy();
                      notification.show({
                        type: 'success',
                        message: 'コビー済み',
                      });
                    }}
                    sx={{
                      border: 0,
                      boxShadow: 'none !important',
                    }}
                    variant="outline"
                  >
                    <IconCopy />
                    コピー
                  </Button>
                )}
              </CopyButton>
            </Box>
          </Flex>
          <Text
            color="sonicSilver"
            fw={{ base: 400, sm: 500 }}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mt={16}
            sx={(theme) => ({
              'br:not(:first-of-type)': {
                display: 'none',
              },
              [theme.fn.smallerThan('sm')]: {
                'br:not(:first-of-type)': {
                  display: 'unset',
                },
              },
            })}
          >
            ※クーポンごとに有効期限が異なります。
            <br />
            プレゼントクーポンの有効期限は、
            <br />
            発行された日から3か月間です。
          </Text>
        </Box>
      );
    }
    return (
      <Box bg="white" className={classes.itemCard} px={10} py={32} ta="center">
        <Text color="black" fw="bold" fz={18} lh={1} mb={{ base: 24, sm: 32 }}>
          プレゼントクーポン
        </Text>
        <IconCouponTicket />
        <Text
          color="sonicSilver"
          fw={500}
          fz={14}
          lh={1.36}
          mt={{ base: 16, sm: 18 }}
        >
          現在は利用可能クーポンがありません。
        </Text>
      </Box>
    );
  };

  return (
    <Container
      pb={{ base: 40, sm: 70 }}
      pt={{ base: 50, sm: 80 }}
      px={30}
      size={1200}
    >
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `お友達紹介プログラム | ${suffixTitle}`,
        }}
        title={`お友達紹介プログラム | ${suffixTitle}`}
      />
      <Title
        color="blackOlive"
        fz={{ base: 22, sm: 30 }}
        lh={1}
        mb={24}
        order={1}
      >
        お友達紹介プログラム
      </Title>
      <Container p={0} size={856}>
        <Box bg="water">
          <BackgroundImage
            bgr={'no-repeat'}
            src="/images/referral-coupon-concen-line-bg.png"
          >
            <Flex
              align="center"
              className={classes.itemCard}
              direction="column"
              mb={20}
              p={{ base: '12px 10px', sm: '32px 32px 40px' }}
            >
              <Box
                h={{ base: 128, sm: 184 }}
                mb={8}
                pos="relative"
                w={{ base: 247, sm: 320 }}
              >
                <Image
                  alt="Present image"
                  fill
                  src="/icons/icon-coupon-present-big.svg"
                />
              </Box>
              <Text
                color="black"
                fw="bold"
                fz={{ base: 16, sm: 18 }}
                lh="18px"
                mb={16}
              >
                ＼紹介した人もされた人もおトクに! ／
              </Text>
              {isLoading ? (
                <Box mb={16} ta="center" w={{ base: '80%', sm: '50%' }}>
                  <Skeleton height={16} mb={8} width={'100%'} />
                  <Skeleton height={16} mb={8} width={'100%'} />
                  <Skeleton height={16} width={'100%'} />
                </Box>
              ) : (
                <Text
                  align="center"
                  color="sonicSilver"
                  fz={{ base: 12, sm: 14 }}
                  lh={1.38} // 1.43 following design YuGothic font
                  mb={16}
                >
                  {configurations?.referralCampain?.text ? (
                    configurations?.referralCampain?.text
                  ) : (
                    <>
                      2,000円クーポンをお友達にプレゼント！
                      <br />
                      さらにご紹介していただいたお友達が紹介クーポンご利用で、
                      <br />
                      あなたにも2,000円クーポンをプレゼント！
                    </>
                  )}
                </Text>
              )}
              <Flex
                align="stretch"
                bg="queenBlue"
                maw={{ base: 251, sm: 350 }}
                mx="auto"
                p={2}
                sx={{ borderRadius: '3px' }}
                w="100%"
              >
                <Box
                  bg="white"
                  className={classes.codeInputWrapper}
                  component="label"
                  pb={{ base: 9, sm: 11 }}
                  pt={11}
                  px={16}
                >
                  <Text color="black" fw={500} fz={12} lh={1} mb={6}>
                    紹介コード
                  </Text>
                  <Box
                    color="richBlack"
                    component="input"
                    disabled={!data?.code}
                    fz={{ base: 18, sm: 20 }}
                    onClick={(e) => {
                      if (e.target) {
                        (e.target as any).select();
                      }
                    }}
                    readOnly
                    type="text"
                    value={data?.code}
                  />
                </Box>
                <Box className={classes.copyBtnWrapper}>
                  <CopyButton value={data?.code || ''}>
                    {({ copy }) => (
                      <Button
                        disabled={!data?.code}
                        fullWidth
                        onClick={() => {
                          copy();
                          notification.show({
                            type: 'success',
                            message: 'コビー済み',
                          });
                        }}
                        sx={{
                          boxShadow: 'none !important',
                        }}
                      >
                        <IconCopy />
                        コピー
                      </Button>
                    )}
                  </CopyButton>
                </Box>
              </Flex>
            </Flex>
          </BackgroundImage>
        </Box>
        {renderCoupon()}
      </Container>
      <Box mt={{ base: 74, sm: 120 }}>
        <Button
          color="grey"
          component={Link}
          fullWidth
          href="/my-page"
          maw={{ base: 148, sm: 300 }}
          mx={{ base: 'auto', sm: 0 }}
          size="lg"
          variant="outline"
        >
          マイページへ戻る
        </Button>
      </Box>
    </Container>
  );
};

MyCoupons.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default MyCoupons;
