import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  itemCard: {
    borderRadius: '6px',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    textAlign: 'center',
  },
  codeInputWrapper: {
    flex: 1,
    borderTopLeftRadius: '3px',
    borderBottomLeftRadius: '3px',
    textAlign: 'left',
    height: '100%',
    input: {
      width: '100%',
      outline: 'none',
      border: 'none',
      padding: 0,
      fontWeight: 'bold',
      lineHeight: 1.3,
    },
  },
  copyBtnWrapper: {
    maxWidth: 119,
    width: '100%',
    flexShrink: 0,
    [theme.fn.smallerThan('sm')]: {
      maxWidth: 92,
    },
    button: {
      height: '100%',
      borderRadius: 0,
      borderTopRightRadius: '3px',
      borderBottomRightRadius: '3px',
      '.mantine-Button-label': {
        fontSize: 16,
        gap: 8,
        svg: {
          flexShrink: 0,
        },
      },
      [theme.fn.smallerThan('sm')]: {
        padding: 8,
        '.mantine-Button-label': {
          fontSize: 14,
          gap: 5,
          svg: {
            width: 12,
          },
        },
      },
    },
  },
}));

export default useStyles;
