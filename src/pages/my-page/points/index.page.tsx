import ArrowRightIcon from '@icons/icon-narrow-thin-right.svg';
import IconPointGift from '@icons/icon-point-gift.svg';
import IconPointGiftCircle from '@icons/icon-point-gift-circle.svg';
import IconWarningCircle2 from '@icons/icon-warning-circle-2.svg';
import {
  BackgroundImage,
  Badge,
  Box,
  Button,
  Container,
  Flex,
  Group,
  List,
  Skeleton,
  Stack,
  Title,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import { useInfiniteQuery } from '@tanstack/react-query';
import Layout from 'components/Layout';
import dayjs from 'dayjs';
import { useFetchData } from 'hooks';
import type { IListResult } from 'hooks/types';
import type { IPointHistoryItem, ITotalPoints } from 'models/booking';
import { bookingQuery } from 'models/booking';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import {
  POINT_HISTORY_BOOKING_TYPE,
  POINT_HISTORY_REASON_TEXT,
} from 'utils/constants';
import helpers from 'utils/helpers';
import request from 'utils/request';

import { styles, sx } from '../styles';

const POINT_HISTORY_LIMIT = 20;

const MyPoints = () => {
  const router = useRouter();
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const { data: totalPoints, isLoading } = useFetchData<ITotalPoints>(
    bookingQuery.getTotalPoints,
  );
  const { ref, inView } = useInView();

  const {
    fetchNextPage,
    isFetching: isFetchingPointHistory,
    data: pointHistoryData,
    hasNextPage,
  } = useInfiniteQuery<IListResult<IPointHistoryItem>>({
    queryKey: bookingQuery.getPointHistory.queryKey,
    queryFn: async ({ pageParam = 1 }) => {
      const { data } = await request({
        url: bookingQuery.getPointHistory.apiUrl,
        data: {
          page: pageParam,
          limit: POINT_HISTORY_LIMIT,
        },
        method: bookingQuery.getPointHistory.method,
      });
      return data;
    },
    getNextPageParam: (lastPage, allPage) => {
      if ((lastPage?.data?.length || 0) >= POINT_HISTORY_LIMIT) {
        return allPage.length + 1;
      }
      return undefined;
    },
  });
  const pointHistoryPages = pointHistoryData?.pages || [];

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingPointHistory) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, inView, isFetchingPointHistory]);

  const renderPointHistory = () => {
    const hasPointHistory = pointHistoryPages[0]?.data?.length !== 0;

    return (
      <Flex
        bg="white"
        direction="column"
        gap={{ base: hasPointHistory ? 16 : 24, sm: hasPointHistory ? 8 : 32 }}
        pb={{ base: hasPointHistory ? 30 : 16, sm: hasPointHistory ? 30 : 32 }}
        pt={{ base: 16, sm: 32 }}
        px={{ base: 16, sm: 50 }}
        sx={{
          borderRadius: 6,
          boxShadow: '0px 2px 2px 0px rgba(0, 0, 0, 0.16)',
        }}
      >
        <Title
          c="richBlack"
          fz={{ base: 16, sm: 18 }}
          lh={{ base: '24px', sm: '26px' }}
        >
          ポイント履歴
        </Title>

        {hasPointHistory && (
          <Stack spacing={0}>
            {pointHistoryPages?.map((pointHistory) => {
              return pointHistory?.data?.map((item, index) => {
                const { point, bookingId } = item;
                const isAwardPoint = point > 0;
                const isNowBooking =
                  item.bookingType === POINT_HISTORY_BOOKING_TYPE.NOW;

                return (
                  <Stack
                    key={item.createdAt + index}
                    onClick={() =>
                      bookingId &&
                      !isNowBooking &&
                      router.push(`/booking/${bookingId}`)
                    }
                    py={{ base: 12, sm: 20 }}
                    spacing={12}
                    sx={{
                      ':not(:last-child)': {
                        borderBottom: '1px solid #F2F2F2',
                      },
                      cursor:
                        bookingId && !isNowBooking ? 'pointer' : 'default',
                    }}
                  >
                    <Stack c="quickSilver" fz={12} lh="16px">
                      {dayjs(item.createdAt).format('YYYY/MM/DD HH:mm')}
                    </Stack>

                    <Group noWrap position="apart" spacing={12}>
                      <Group noWrap spacing={12}>
                        <Stack c={isAwardPoint ? 'marigold' : 'maximumRed'}>
                          <IconPointGiftCircle
                            height={mobileScreen ? 32 : 40}
                            width={mobileScreen ? 32 : 40}
                          />
                        </Stack>

                        <Flex direction="column" gap={{ base: 8, sm: 6 }}>
                          <Stack
                            c="richBlack"
                            fw={600}
                            fz={{ base: 12, sm: 14 }}
                            lh={{ base: '16px', sm: '20px' }}
                          >
                            {POINT_HISTORY_REASON_TEXT[item.reason]}
                          </Stack>
                          {item.bookingId && (
                            <Flex gap={{ base: 6, sm: 8 }}>
                              <Stack
                                c="sonicSilver"
                                fz={12}
                                lh="16px"
                              >{`予約ID: ${item.bookingId}`}</Stack>
                              {isNowBooking && (
                                <Badge
                                  c="white"
                                  color="marigoldActive"
                                  fw={400}
                                  fz={10}
                                  h={16}
                                  p={0}
                                  radius={4}
                                  w={36}
                                >
                                  NOW
                                </Badge>
                              )}
                            </Flex>
                          )}
                        </Flex>
                      </Group>

                      <Title
                        c={isAwardPoint ? 'richBlack' : 'maximumRed'}
                        fz={{ base: 14, sm: 16 }}
                        lh={{ base: '20px', sm: 1.5 }}
                        sx={{ flexShrink: 0 }}
                      >
                        {isAwardPoint
                          ? `+ ${helpers.numberFormat(point)}`
                          : `- ${helpers.numberFormat(point * -1)}`}
                      </Title>
                    </Group>
                  </Stack>
                );
              });
            })}
          </Stack>
        )}

        {!hasPointHistory && !isFetchingPointHistory && (
          <Stack align="center" py={{ base: 64, sm: 0 }}>
            <Stack p={3}>
              <IconPointGift />
            </Stack>
            <Stack c="sonicSilver" lh="20px">
              表示するデータがありません
            </Stack>
          </Stack>
        )}

        {isFetchingPointHistory && (
          <Group
            pt={{ base: 26, sm: 20 }}
            spacing={12}
            sx={{ borderTop: `${hasPointHistory ? 1 : 0}px solid #F2F2F2` }}
            w="100%"
          >
            <Skeleton
              h={{ base: 32, sm: 40 }}
              sx={{ borderRadius: '50%', flexShrink: 0 }}
              w={{ base: 32, sm: 40 }}
            />
            <Stack
              spacing={12}
              w={{
                base: 'calc(100% - 12px - 32px)',
                sm: 'calc(100% - 12px - 40px)',
              }}
            >
              <Skeleton h={14} maw={300} w="100%" />
              <Skeleton h={14} w={150} />
            </Stack>
          </Group>
        )}

        {hasNextPage && !isFetchingPointHistory && <div ref={ref} />}
      </Flex>
    );
  };

  return (
    <Container
      pb={{ base: 44, sm: 70 }}
      pt={{ base: 44, sm: 64 }}
      px={{ base: 20, sm: 30 }}
      size={1200}
    >
      <Title
        color="blackOlive"
        fz={{ base: 22, sm: 30 }}
        lh={{ base: '32px', sm: '40px' }}
        mb={{ base: 16, sm: 54 }}
      >
        ホググポイント
      </Title>

      <Container p={0} size={860}>
        <BackgroundImage
          bgr="no-repeat"
          mb={{ base: 16, sm: 24 }}
          pb={{ base: 16, sm: 22 }}
          pt={{ base: 16, sm: 56 }}
          src={`/images/point-banner${mobileScreen ? '-mobile' : ''}.png`}
        >
          <Stack align="center" display={{ sm: 'none' }} mb={8}>
            <Image
              alt="Point gift mobile"
              draggable={false}
              height={50}
              priority
              quality={100}
              sizes="100vw"
              src="/images/point-gift-mobile.png"
              width={50}
            />
          </Stack>
          <Title
            c="richBlack"
            fz={{ base: 16, sm: 20 }}
            lh={{ base: 1.5, sm: 1.6 }}
            mb={16}
            ta="center"
          >
            ＼保有ポイント／
          </Title>

          <Stack
            bg="white"
            m="auto"
            maw={500}
            spacing={0}
            sx={{
              border: '1px solid #DAAC59',
              borderRadius: 6,
              overflow: 'hidden',
            }}
            w="calc(100% - 32px)"
          >
            <Flex
              align="center"
              direction="column"
              gap={{ base: 10, sm: 4 }}
              py={16}
            >
              {isLoading ? (
                <Skeleton h={{ base: 40, sm: 46 }} w={115} />
              ) : (
                <Title
                  c="richBlack"
                  fz={{ base: 28, sm: 32 }}
                  lh={{ base: '40px', sm: '46px' }}
                >
                  {helpers.numberFormat(totalPoints?.availablePoints)}
                </Title>
              )}
              {isLoading ? (
                <Skeleton h={16} w={115} />
              ) : (
                <Stack c="grey" fz={12} lh="16px">
                  獲得日から{totalPoints?.expirationInYears}年間有効
                </Stack>
              )}
            </Flex>

            <Group
              bg="rgba(255, 234, 194, 0.5)"
              fz={12}
              grow
              lh="16px"
              py={{ base: 12, sm: 16 }}
              spacing={0}
            >
              <Stack
                align="center"
                spacing={4}
                sx={{
                  borderRight: `1px solid rgba(218, 172, 89, 0.${
                    mobileScreen ? 3 : 4
                  })`,
                }}
              >
                <Stack c="grey">ポイント付与率</Stack>
                {isLoading ? (
                  <Skeleton h={16} w={88} />
                ) : (
                  <Stack c="richBlack" fw={600}>
                    決済金額の{(totalPoints?.exchangeRate.granting || 0) * 100}%
                  </Stack>
                )}
              </Stack>

              <Stack align="center" spacing={4}>
                <Stack c="grey">ポイント利用</Stack>
                {isLoading ? (
                  <Skeleton h={16} w={122} />
                ) : (
                  <Stack c="richBlack" fw={600}>
                    {totalPoints?.exchangeRate.using.point}ポイント＝
                    {totalPoints?.exchangeRate.using.amount}円相当
                  </Stack>
                )}
              </Stack>
            </Group>
          </Stack>

          <Flex
            gap={{ base: 4, sm: 6 }}
            m="auto"
            mt={8}
            sx={{
              // 500 + 16 * 2 + 20 * 2
              '@media (max-width: 572px)': {
                paddingLeft: 16,
                width: '100%',
              },
            }}
            w={500}
          >
            <IconWarningCircle2 />
            {isLoading ? (
              <Skeleton h={16} maw={297} w="calc(100% - 36px)" /> // 36 = 16 * 2 + 4
            ) : (
              <Stack c="#A36A00" fz={12} lh="16px">
                {totalPoints?.minPoints}
                単位から利用できます。最低決済金額は¥
                {totalPoints?.minBookingAmount}です。
              </Stack>
            )}
          </Flex>
          {/* point explanation modal */}
          <Flex justify="center" mt={16}>
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                openContextModal({
                  modal: 'AlertModal',
                  centered: true,
                  fullScreen: mobileScreen,
                  size: 630,
                  innerProps: {
                    icon: null,
                    content: (
                      <Box>
                        <Title
                          align="center"
                          color="black"
                          mb={16}
                          order={3}
                          size={30}
                          sx={sx.pointExplanationTitle}
                        >
                          ホググポイントの仕組み
                        </Title>
                        <List sx={sx.pointExplanationContent} type="ordered">
                          {[
                            '施術決済完了後に、決済金額の0.5％がホググポイントとして付与されます。',
                            '端数ポイントが出た場合は切り捨てとなります。',
                            'ポイントの有効期限は、獲得した月から起算して1年後の月末までとなります。',
                            '有効期限までにポイントの利用がない場合、当該ポイントは失効となります。',
                            '予約リクエスト時にポイントとクーポンを併用する場合は、ポイント利用が優先され、ポイント利用金額の値引き後の金額に対してクーポンが適応されます。',
                            'ポイント利用の限度額はありませんが、最低でも100円の決済が必要となります。',
                            '複数のアカウントがあった場合でも、ポイント の統合はできません。',
                          ].map((text, index) => (
                            <List.Item key={index}>{text}</List.Item>
                          ))}
                        </List>
                      </Box>
                    ),
                  },
                  styles: styles.pointExplanationModal,
                });
              }}
              rightIcon={<ArrowRightIcon />}
              styles={styles.pointExplanationBtn}
              variant="subtle"
            >
              ポイント詳細はこちら
            </Button>
          </Flex>
        </BackgroundImage>

        {renderPointHistory()}
      </Container>

      <Button
        color="grey"
        component={Link}
        fullWidth
        href="/my-page"
        maw={{ base: 148, sm: 300 }}
        mt={{ base: 16, sm: 64 }}
        mx={{ base: 'auto', sm: 0 }}
        size="lg"
        variant="outline"
      >
        マイページへ戻る
      </Button>
    </Container>
  );
};

MyPoints.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default MyPoints;
