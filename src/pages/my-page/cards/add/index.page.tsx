import { Button, Container, Flex, Text } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import Layout from 'components/Layout';
import { AddCardForm } from 'components/PaymentCard';
import { useFetchData, useMutate } from 'hooks';
import { get } from 'lodash';
import type { AddPaymentCardPayload, CardListResponse } from 'models/payment';
import { paymentQuery } from 'models/payment';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import React from 'react';
import { eventLog } from 'utils/helpers';
import notification from 'utils/notification';

import { sx } from './styles';

const AddCardPage = () => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const router = useRouter();
  const { data, refetch } = useFetchData<CardListResponse>({
    ...paymentQuery.getCardList,
    staleTime: 1000 * 60 * 2,
  });
  const { mutateAsync: addPayment, isLoading: isAddingCard } =
    useMutate<AddPaymentCardPayload>({
      ...paymentQuery.addPaymentCard,
      successMessage: 'カードを追加登録しました。',
    });

  const handleAddCard = async (cardToken: string) => {
    try {
      await addPayment({ token: cardToken });
      if (data?.cards.length === 0) {
        eventLog('add_payment_info', {
          page: 'My page',
        });
      }
      await refetch();
      router.push('/my-page/cards');
    } catch (e) {
      notification.show({
        type: 'error',
        message: get(e, 'error', "Can't add card at the moment."),
      });
    }
  };

  /* Content visible in 1140px + 80px (padding left and right) */
  return (
    <Container px={40} py={80} size={1220} sx={sx.addCardPage}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: 'クレジットカードの登録 | HOGUGU（ホググ）',
        }}
        title="クレジットカードの登録 | HOGUGU（ホググ）"
      />
      <AddCardForm
        footerContent={(formLoading) => (
          <Flex gap={20} justify="space-between" sx={sx.btnGroup}>
            <Button
              className="cancel-btn"
              color="grey"
              component={Link}
              href="/my-page/cards"
              size="lg"
              variant="outline"
            >
              {mobileScreen ? 'お支払い方法\nへ戻る' : 'お支払い方法へ戻る'}
            </Button>
            <Button
              color="marigold"
              loading={isAddingCard || formLoading}
              size="lg"
              type="submit"
            >
              登録する
            </Button>
          </Flex>
        )}
        headerContent={
          <>
            <Text color="blackOlive" size={30} sx={sx.title} weight="bold">
              クレジットカードの登録・追加登録
            </Text>
            <Text color="blackOlive" size={16} sx={sx.subTitle}>
              予約時にご利用いただくクレジットカードをご登録ください。
            </Text>
          </>
        }
        onSubmit={handleAddCard}
      />
    </Container>
  );
};

AddCardPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default AddCardPage;
