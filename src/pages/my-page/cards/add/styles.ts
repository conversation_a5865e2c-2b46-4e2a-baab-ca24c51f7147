import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  addCardPage: {
    display: 'flex',
    flexDirection: 'column',
    '@media (max-width: 768px)': {
      padding: '50px 20px 40px',
    },
  },
  title: {
    marginBottom: 16,
    '@media (max-width: 768px)': {
      marginBottom: 12,
      fontSize: 22,
    },
  },
  subTitle: {
    marginBottom: 40,
    '@media (max-width: 768px)': {
      fontSize: 14,
      marginBottom: 24,
    },
  },
  btnGroup: {
    marginTop: 60,
    '@media (max-width: 768px)': {
      marginTop: 30,
    },
    'button, a': {
      maxWidth: 300,
      width: '100%',
      '@media (max-width: 768px)': {
        maxWidth: 148,
      },
      '&.cancel-btn': {
        padding: 0,
        span: {
          lineHeight: 1.2,
          textAlign: 'center',
          whiteSpace: 'pre-line',
        },
      },
    },
  },
};
