import { Container } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import Layout from 'components/Layout';
import { CardList } from 'components/PaymentCard';
import { useFetchData, useMutate } from 'hooks';
import { get } from 'lodash';
import type { CardListResponse } from 'models/payment';
import { paymentQuery } from 'models/payment';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import React from 'react';
import notification from 'utils/notification';
import { suffixTitle } from 'utils/seoConfig';

const PaymentCardPage = () => {
  const router = useRouter();
  const {
    data,
    isLoading,
    refetch: refetchList,
  } = useFetchData<CardListResponse>({
    ...paymentQuery.getCardList,
    staleTime: 1000 * 60 * 2,
  });

  const { mutateAsync: removeCard, isLoading: isDeletingCard } = useMutate<{
    cardId: string;
  }>(paymentQuery.removeCard);

  const { mutateAsync: setDefaultCard, isLoading: isSettingDefaultCard } =
    useMutate<{ cardId: string }>(paymentQuery.setDefaultCard);

  const handleRemoveCard = async (id: string) => {
    try {
      openContextModal({
        modal: 'AlertModal',
        size: 630,
        innerProps: {
          title: 'ご確認',
          content: 'このカードを削除しますか？',
          onConfirm: () => {
            removeCard(
              { cardId: id },
              {
                onSuccess: () => {
                  refetchList();
                },
              },
            );
          },
          haveActionBtn: true,
        },
        withCloseButton: false,
        centered: true,
      });
    } catch (e) {
      notification.show({
        type: 'error',
        message: get(e, 'error', ''),
      });
    }
  };

  const handleSelectDefaultCard = async (id: string) => {
    try {
      await setDefaultCard({ cardId: id });
      refetchList();
      notification.show({
        type: 'success',
        message: '予約リクエストに表示するカードを変更しました。',
      });
    } catch (e) {
      notification.show({
        type: 'error',
        message: get(e, 'error', ''),
      });
    }
  };

  return (
    <Container
      px={40}
      py={80}
      size={1220}
      sx={{
        '@media (max-width: 768px)': {
          padding: '50px 20px',
        },
      }}
    >
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `お支払い方法 | ${suffixTitle}`,
        }}
        title={`お支払い方法 | ${suffixTitle}`}
      />
      <CardList
        defaultCard={data?.defaultCard}
        isDeletingCard={isDeletingCard}
        isSettingDefaultCard={isSettingDefaultCard}
        list={data?.cards || []}
        loading={isLoading}
        onAdd={() => router.push('/my-page/cards/add')}
        onCancel={() => router.push('/my-page')}
        onRemoveCard={handleRemoveCard}
        onSelectDefault={handleSelectDefaultCard}
      />
    </Container>
  );
};

PaymentCardPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default PaymentCardPage;
