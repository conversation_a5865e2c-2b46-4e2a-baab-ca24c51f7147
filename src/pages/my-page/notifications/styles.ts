import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  wrapper: (theme: MantineTheme) => ({
    paddingTop: 80,
    paddingBottom: 130,
    h3: {
      display: 'none',
    },
    h4: {
      fontSize: 24,
    },
    small: {
      fontSize: 16,
      color: theme.colors.sonicSilver,
    },
    '@media (max-width: 768px)': {
      paddingTop: 40,
      paddingBottom: 40,
      background: 'white',
      minHeight: 'inherit',
      h4: {
        fontSize: 16,
      },
      small: {
        fontSize: 12,
        marginTop: 14,
      },
    },
  }),
};
