import { Container } from '@mantine/core';
import Layout from 'components/Layout';
import { NotificationList } from 'components/Notifications';
import { NextSeo } from 'next-seo';
import { IntlProvider } from 'react-intl';

import { sx } from './styles';

const NotificationsPage = () => {
  return (
    <IntlProvider locale="ja" onError={() => {}}>
      <Container size={1140} sx={sx.wrapper}>
        <NextSeo
          nofollow
          noindex
          openGraph={{
            title: '通知一覧 | HOGUGU（ホググ）',
          }}
          title="通知一覧 | HOGUGU（ホググ）"
        />
        <NotificationList />
      </Container>
    </IntlProvider>
  );
};

NotificationsPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default NotificationsPage;
