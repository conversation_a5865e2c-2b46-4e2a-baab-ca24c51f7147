import { Container } from '@mantine/core';
import { dehydrate } from '@tanstack/react-query';
import { Step1, Step2, Step3, Step4 } from 'components/DeletingAccount';
import Layout from 'components/Layout';
import { useMutate, useUser } from 'hooks';
import { get } from 'lodash';
import type { RequestDeleteAccountPayload } from 'models/auth';
import { authQuery } from 'models/auth';
import type { GetServerSideProps } from 'next';
import { NextSeo } from 'next-seo';
import { useState } from 'react';
import helpers from 'utils/helpers';
import getQueryClient, { fetchData } from 'utils/queryClient';
import { suffixTitle } from 'utils/seoConfig';

import { sx } from './styles';

const DeleteAccountPage = ({ enabled }: { enabled: boolean }) => {
  const { data: user, refetch } = useUser();
  const [step, setStep] = useState(1);
  const [answer, setAnswer] = useState<RequestDeleteAccountPayload>({
    reasons: [],
  });

  const { mutateAsync: requestDeleteFn, isLoading: isRequesting } = useMutate<
    RequestDeleteAccountPayload,
    unknown
  >(authQuery.requestDeleteAccount);

  const handleSubmitOtp = (values = answer) => {
    requestDeleteFn(values, {
      onSuccess: async () => {
        setStep(4);
        await refetch();
      },
    });
  };

  return (
    <Container size={1140} sx={sx.wrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `退会申請 | ${suffixTitle}`,
        }}
        title={`退会申請 | ${suffixTitle}`}
      />
      {(() => {
        switch (step) {
          case 1:
            return <Step1 enabled={enabled} onClick={() => setStep(2)} />;
          case 2:
            return (
              <Step2
                backFn={() => setStep(1)}
                initialValues={answer}
                onSubmit={(values) => {
                  setAnswer(values);
                  if (user?.isTesting) {
                    return handleSubmitOtp(values);
                  }
                  return setStep(3);
                }}
              />
            );
          case 3:
            return (
              <Step3
                backFn={() => setStep(2)}
                isLoading={isRequesting}
                onSubmit={handleSubmitOtp}
              />
            );
          default:
            return <Step4 />;
        }
      })()}
    </Container>
  );
};

DeleteAccountPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  try {
    if (req.url?.startsWith('/_next')) {
      return {
        props: {
          enabled: true,
        },
      };
    }
    const queryClient = getQueryClient();
    await fetchData({
      queryClient,
      ...authQuery.checkCanDeleteAccount,
      axiosConfig: helpers.getTokenConfig(req, res),
    });
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: false,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }
};

export default DeleteAccountPage;
