import type { CSSObject, MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  wrapper: (_theme: MantineTheme) => ({
    paddingTop: 80,
    paddingBottom: 130,
    fontSize: 16,
    color: _theme.colors.blackOlive,
    '@media (max-width: 768px)': {
      paddingTop: 50,
      paddingBottom: 40,
      fontSize: 14,
    },
  }),
};

export const styles: Record<string, Record<string, CSSObject>> = {};
