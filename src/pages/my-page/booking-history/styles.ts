import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  bookingHistoryWrapper: {
    paddingTop: 80,
    paddingBottom: 54,
    '@media (max-width: 768px)': {
      paddingTop: 50,
      paddingBottom: 50,
    },
  },

  title: (theme: MantineTheme) => ({
    fontSize: 30,
    color: theme.colors.blackOlive,
    '@media (max-width: 768px)': {
      fontSize: 22,
    },
  }),

  noBookingWrapper: (theme: MantineTheme) => ({
    fontSize: 16,
    paddingTop: 32,
    paddingBottom: 25,
    color: theme.colors.sonicSilver,
    '@media (max-width: 768px)': {
      fontSize: 14,
      paddingTop: 15,
      paddingBottom: 90,
      svg: {
        width: 120,
        height: 'auto',
      },
    },
  }),
};

export const styles: Record<string, any> = {
  backBtn: {
    root: {
      display: 'block',
      maxWidth: 300,
      width: '100%',
      margin: '55px auto 0',
      '@media (max-width: 768px)': {
        maxWidth: 180,
        marginTop: 30,
      },
    },
  },
};
