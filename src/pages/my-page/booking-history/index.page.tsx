import IconNoBooking from '@icons/icon-no-booking.svg';
import { Button, Container, Flex, Tabs, Text, Title } from '@mantine/core';
import { BookingHistory } from 'components/Bookings';
import Layout from 'components/Layout';
import { useFetchList } from 'hooks';
import { get as _get } from 'lodash';
import type { IBookingItem } from 'models/booking';
import { bookingQuery } from 'models/booking';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { suffixTitle } from 'utils/seoConfig';

import { styles, sx } from './styles';

const bookingTabs = {
  REQUEST: 'REQUEST',
  CONFIRMED: 'CONFIRMED',
  COMPLETED: 'COMPLETED',
  CANCELED: 'CANCELED',
};

const seoTitle = {
  [bookingTabs.REQUEST]: 'リクエスト中',
  [bookingTabs.CONFIRMED]: '確定',
  [bookingTabs.COMPLETED]: 'お会計完了',
  [bookingTabs.CANCELED]: 'キャンセル済み',
};

const ProfilePage = () => {
  const router = useRouter();
  const { page: qPage } = router.query;
  const activeTab = _get(
    bookingTabs,
    router.query.category as string,
    bookingTabs.REQUEST,
  );
  const {
    page,
    lastPage,
    list: bookings = [],
    isFetching: isLoadingBookings,
  } = useFetchList<IBookingItem>({
    ...bookingQuery.getBookingHistory,
    customParams: {
      sort: [bookingTabs.REQUEST, bookingTabs.CONFIRMED].includes(activeTab)
        ? 'asc'
        : 'desc',
      page: typeof qPage === 'string' ? Number(qPage) : 1,
      limit: 20,
      category: activeTab,
    },
  });

  return (
    <Container size={1140} sx={sx.bookingHistoryWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `${
            seoTitle[activeTab] || 'リクエスト中'
          } | 予約履歴 | ${suffixTitle}`,
        }}
        title={`${
          seoTitle[activeTab] || 'リクエスト中'
        } | 予約履歴 | ${suffixTitle}`}
      />
      <Title order={3} sx={sx.title}>
        予約履歴 一覧
      </Title>
      <Tabs
        defaultValue={bookingTabs.REQUEST}
        onTabChange={async (category: string) => {
          router.push(`/my-page/booking-history?category=${category}`);
        }}
        unstyled
        value={activeTab}
      >
        <Tabs.List>
          <Tabs.Tab value={bookingTabs.REQUEST}>リクエスト中</Tabs.Tab>
          <Tabs.Tab value={bookingTabs.CONFIRMED}>確定</Tabs.Tab>
          <Tabs.Tab value={bookingTabs.COMPLETED}>お会計完了</Tabs.Tab>
          <Tabs.Tab value={bookingTabs.CANCELED}>キャンセル済み</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel pt="xs" value={bookingTabs.REQUEST}>
          <Title order={4}>リクエスト中</Title>
          {isLoadingBookings || bookings.length ? (
            <BookingHistory
              bookings={bookings}
              lastPage={lastPage}
              loading={isLoadingBookings}
              page={page}
            />
          ) : (
            <Flex align="center" direction="column" sx={sx.noBookingWrapper}>
              <IconNoBooking />
              <Text>リクエスト中の予約はありません。</Text>
            </Flex>
          )}
        </Tabs.Panel>

        <Tabs.Panel pt="xs" value={bookingTabs.CONFIRMED}>
          <Title order={4}>確定</Title>
          {isLoadingBookings || bookings.length ? (
            <BookingHistory
              bookings={bookings}
              lastPage={lastPage}
              loading={isLoadingBookings}
              page={page}
            />
          ) : (
            <Flex align="center" direction="column" sx={sx.noBookingWrapper}>
              <IconNoBooking />
              <Text>確定した予約はありません。</Text>
            </Flex>
          )}
        </Tabs.Panel>

        <Tabs.Panel pt="xs" value={bookingTabs.COMPLETED}>
          <Title order={4}>お会計完了</Title>
          {isLoadingBookings || bookings.length ? (
            <BookingHistory
              bookings={bookings}
              lastPage={lastPage}
              loading={isLoadingBookings}
              page={page}
            />
          ) : (
            <Flex align="center" direction="column" sx={sx.noBookingWrapper}>
              <IconNoBooking />
              <Text>お会計完了の予約はありません。</Text>
            </Flex>
          )}
        </Tabs.Panel>

        <Tabs.Panel pt="xs" value={bookingTabs.CANCELED}>
          <Title order={4}>キャンセル済み</Title>
          {isLoadingBookings || bookings.length ? (
            <BookingHistory
              bookings={bookings}
              lastPage={lastPage}
              loading={isLoadingBookings}
              page={page}
            />
          ) : (
            <Flex align="center" direction="column" sx={sx.noBookingWrapper}>
              <IconNoBooking />
              <Text>キャンセル済みの予約はありません。</Text>
            </Flex>
          )}
        </Tabs.Panel>
      </Tabs>
      <Button
        color="grey"
        component={Link}
        href="/my-page"
        size="lg"
        styles={styles.backBtn}
        variant="outline"
      >
        マイページへ戻る
      </Button>
    </Container>
  );
};

ProfilePage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default ProfilePage;
