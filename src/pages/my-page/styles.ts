import { type MantineTheme, type Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  mypageWrapper: {
    paddingTop: 80,
    paddingBottom: 80,
    '@media (max-width: 768px)': {
      flexFlow: 'column-reverse wrap',
      paddingTop: 30,
      paddingBottom: 30,
      h4: {
        fontSize: 16,
      },
    },
  },

  sidebarWrapper: {
    alignItems: 'flex-start',
    flexDirection: 'column',
    h3: {
      fontSize: 30,
      marginBottom: 8,
    },
    '@media (max-width: 768px)': {
      width: '100%',
      h3: {
        display: 'none',
      },
    },
    '@media (min-width: 769px)': {
      h4: {
        display: 'none',
      },
    },
  },

  sidebar: {
    width: 315,
    marginRight: 100,
    alignItems: 'flex-start',
    '@media (max-width: 768px)': {
      marginRight: 0,
      width: '100%',
      paddingLeft: 16,
      paddingRight: 16,
      backgroundColor: 'white',
      borderRadius: 4,
      border: 'solid 1px #bdccd3',
    },
  },

  content: (theme: MantineTheme) => ({
    width: '100%',
    h4: {
      fontSize: 24,
    },
    small: {
      fontSize: 16,
      color: theme.colors.sonicSilver,
    },
    '@media (max-width: 768px)': {
      marginBottom: 30,
      h3: {
        fontSize: 20,
        marginBottom: 25,
      },
      h4: {
        fontSize: 16,
      },
      small: {
        display: 'none',
      },
    },
    '@media (min-width: 769px)': {
      h3: {
        display: 'none',
      },
    },
  }),

  pointExplanationContent: {
    color: '#6B6B6B',
    textAlign: 'left',
    marginTop: 16,
    fontSize: 14,
    paddingRight: 10,
    '@media(max-width: 768px)': {
      paddingTop: 20,
    },
    '& > *::marker': {
      color: '#3C3C3C',
      fontWeight: 700,
    },
    '& > * > *': {
      marginLeft: 4,
    },
    '& > *': {
      marginBottom: 20,
      '@media (max-width: 768px)': {
        marginBottom: 8,
      },
    },
  },
  pointExplanationTitle: {
    '@media(max-width: 768px)': {
      fontSize: 18,
      textAlign: 'left',
      '&::after': {
        content: '""',
        display: 'block',
        position: 'absolute',
        width: '100%',
        height: 1,
        top: 56,
        left: 0,
        right: 0,
        boxShadow: '0px 2px 2px 0px rgba(0, 0, 0, 0.1608)',
      },
    },
  },
};

export const styles: Record<string, any> = {
  menuItem: (theme: MantineTheme) => ({
    root: {
      padding: '10px 16px 10px 10px',
      height: 70,
      borderRadius: 4,
      width: '100%',
      border: 'solid 1px #bdccd3',
      backgroundColor: 'white',
      '&:hover': {
        backgroundColor: 'white',
      },
      '& + * + &': {
        marginTop: 10,
      },
      '@media (max-width: 768px)': {
        border: 'none',
        height: 60,
        padding: 0,
        fontSize: 14,
        '& + * + &': {
          marginTop: 0,
          borderRadius: 0,
          borderTop: 'solid 1px #bdccd3',
        },
        '.red-dot': {
          marginRight: 16,
        },
      },
    },
    icon: {
      width: 40,
      height: 40,
      marginRight: 14,
      borderRadius: '50%',
      backgroundColor: theme.colors.queenBlue[6],
      svg: {
        color: 'white',
        fill: 'white',
        width: 18,
        height: 'auto',
        path: {
          fill: 'white !important',
        },
      },
      '@media (max-width: 768px)': {
        width: 36,
        height: 36,
        marginRight: 12,
        svg: {
          width: 15,
        },
      },
    },
    rightSection: {
      '@media (min-width: 769px)': {
        'svg:last-child': {
          display: 'none',
        },
      },
    },
  }),

  markReadAllBtn: {
    root: {
      fontSize: 16,
      '&:hover': {
        backgroundColor: 'transparent',
      },
      '@media (max-width: 768px)': {
        display: 'none',
      },
    },
  },

  pointExplanationBtn: {
    root: {
      fontSize: 12,
      padding: 0,
      minHeight: 20,
      height: 20,
      fontWeight: 'normal',
      color: '#327EB9',
      boxShadow: 'none',
      backgroundColor: 'transparent',
      borderBottom: 'solid 1px #327EB9',
      '&:hover': {
        backgroundColor: 'transparent',
      },
      '&:active': {
        backgroundColor: 'transparent',
      },
      '&:focus': {
        outLine: 'none',
        boxShadow: 'none',
      },
    },
    rightIcon: {
      width: 12,
      marginLeft: 4,
    },
  },

  pointExplanationModal: {
    root: {
      '& .modal-container': {
        padding: '56px 30px 30px',
        '@media (max-width: 768px)': {
          padding: 20,
          borderRadius: 0,
        },
      },
    },
    body: {
      '@media (max-width: 768px)': {
        height: '100%',
      },
    },
    content: {
      '@media (max-width: 768px)': {
        height: '100dvh',
        '& > *': {
          maxHeight: '100% !important',
          height: '100%',
          backgroundColor: 'white',
        },
      },
    },
  },
};
