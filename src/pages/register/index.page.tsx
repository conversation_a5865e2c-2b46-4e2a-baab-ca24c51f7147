import { Box, Button, Flex, Text } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import type { CompleteProfileFormValues } from 'components/Auth/CompleteProfileForm/schema';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';
import Layout from 'components/Layout';
import { AddCardForm } from 'components/PaymentCard';
import { useAuthSignInWithPhoneNumber, useMutate, useUser } from 'hooks';
import { get } from 'lodash';
import type {
  CheckPhonePayload,
  ICheckPhone,
  ICustomer,
  SignInPayload,
} from 'models/auth';
import authQuery from 'models/auth/query';
import type { AddPaymentCardPayload } from 'models/payment';
import { paymentQuery } from 'models/payment';
import type { GetServerSideProps, NextApiRequest, NextApiResponse } from 'next';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import React, { useEffect, useState } from 'react';
import { FIREBASE_AUTH_ERRORS } from 'utils/constants';
import dayjs from 'utils/dayjs';
import { auth, generateRecaptcha } from 'utils/firebase';
import helpers, { eventLog } from 'utils/helpers';
import notification from 'utils/notification';
import { suffixTitle } from 'utils/seoConfig';
import type { UserTokenCookie } from 'utils/type';

import { sx } from './styles';

const CompleteProfileForm = dynamic(
  () => import('components/Auth/CompleteProfileForm'),
);
const OtpVerifyForm = dynamic(() => import('components/Auth/OtpVerifyForm'), {
  ssr: false,
});
const PhoneForm = dynamic(() => import('components/Auth/PhoneForm'), {
  ssr: false,
});

const STEPPER_CONTENTS = [
  { value: 1, label: 'Step1' },
  { value: 2, label: 'Step2' },
  { value: 3, label: 'Step3' },
  { value: 4, label: 'Step4' },
];

const RegisterPage = () => {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verifyBy, setVerifyBy] = useState<'sms' | 'call'>('sms');
  const { data: currentUserData, refetch: refetchUserData } = useUser();
  const { refetch, isFetching } = useUser();

  const { mutateAsync: signIn, isLoading: isRegistering } = useMutate<
    SignInPayload,
    UserTokenCookie
  >(authQuery.login);

  const { mutateAsync: sendCallOtp } = useMutate<
    CheckPhonePayload,
    ICheckPhone
  >(authQuery.checkPhone);

  const { mutateAsync: updateProfile, isLoading: isUpdatingProfile } =
    useMutate<unknown, ICustomer>(authQuery.updateProfile);

  const { mutateAsync: addPayment, isLoading: isAddingCard } =
    useMutate<AddPaymentCardPayload>(paymentQuery.addPaymentCard);

  const {
    isLoading: isSendingOtp,
    mutateAsync: sendOtp,
    data: confirmationResult,
    variables: sendOtpData,
  } = useAuthSignInWithPhoneNumber(auth);

  useEffect(() => {
    if (currentUserData?.isCompletedProfile === false) {
      setStep(3);
    }
  }, [currentUserData?.isCompletedProfile, router.asPath]);

  const verifyAccount = async () => {
    const { data: userData } = await refetch();
    if (userData?.isCompletedProfile) {
      eventLog('login');
    } else {
      eventLog('sign_up');
    }
    router.push(
      router.query.referer && typeof router.query.referer === 'string'
        ? router.query.referer
        : '/',
    );
  };

  const handleSendSmsOtp = async (phone: string) => {
    try {
      generateRecaptcha();
      await sendOtp({
        phoneNumber: phone,
        appVerifier: window.recaptchaVerifier,
      });
      setStep(2);
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] ||
        'システムエラーです。申し訳ございません。1つ前の画面に戻り、電話番号を再度送信してください。';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
      throw e;
    }
  };
  const handleSubmitStep1 = async (values: ICheckPhone) => {
    eventLog('start_entry');
    const data = values as ICheckPhone;
    // Special case dev phone
    if (data.token && data.total) {
      helpers.setToken({ token: data.token });
      await verifyAccount();
      return;
    }
    if (data.phone) {
      handleSendSmsOtp(data.phone);
    }
  };

  const handleStep2 = async (values: OtpVerifyFormValues) => {
    try {
      if (!sendOtpData?.phoneNumber) return;
      setIsVerifying(true);
      let code: string;
      if (verifyBy === 'call') {
        code = values.code;
      } else {
        const result = await confirmationResult?.confirm(values.code);
        code = result?.user.uid || '';
      }
      const data = await signIn({
        code,
        type: verifyBy,
        phone: sendOtpData.phoneNumber,
      });
      helpers.setToken(data);
      await verifyAccount();
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] || 'Something went wrong...';
      if (String(errorCode).includes('auth'))
        notification.show({
          type: 'error',
          message: errorMessage,
        });
    } finally {
      setIsVerifying(false);
    }
  };
  const handleResendOtp = async (type: 'sms' | 'call') => {
    if (!sendOtpData?.phoneNumber) return;
    setVerifyBy(type);
    if (type === 'call') {
      // Resend OTP via call
      const oPhone = helpers.transfromPhone(sendOtpData.phoneNumber);
      await sendCallOtp({
        ...oPhone,
        type: 'call',
      });
      return;
    }
    // Resend OTP via sms
    await handleSendSmsOtp(sendOtpData.phoneNumber);
  };

  const hanldeSubmitStep3 = async (values: CompleteProfileFormValues) => {
    eventLog('profile_input');
    const params = helpers.removeEmpty(values, [
      'phone',
    ]) as CompleteProfileFormValues;
    const response = await updateProfile({
      ...params,
      gender: params?.gender ? Number(params.gender) : undefined,
      birthday: dayjs(params.birthday).startOf('d').toISOString(),
    });
    if (response.invitationCoupon) {
      openContextModal({
        modal: 'ReferralCouponModal',
        size: 630,
        withCloseButton: false,
        centered: true,
        innerProps: {
          code: response.invitationCoupon,
          onConfirm: async () => {
            eventLog('complete_profile');
            setStep(4);
            await refetchUserData();
            window.scrollTo(0, 0);
          },
        },
      });
      return;
    }
    eventLog('complete_profile');
    setStep(4);
    await refetchUserData();
    window.scrollTo(0, 0);
  };

  const handleSkipAddCard = () => {
    eventLog('payment_input_skip');
    router.push('/register/complete');
  };
  const handleAddCard = async (cardToken: string) => {
    try {
      await addPayment({ token: cardToken });
      eventLog('add_payment_info', {
        page: 'Sign up',
      });
      router.push('/register/complete');
    } catch (e) {
      notification.show({
        type: 'error',
        message: get(e, 'error', "Can't add card at the moment."),
      });
    }
  };

  const renderStepContent = () => {
    if (step === 4) {
      return (
        <AddCardForm
          footerContent={(formLoading) => (
            <Flex
              align="center"
              direction="column"
              gap={20}
              sx={sx.addCardBtnGroup}
            >
              <Button
                color="marigold"
                loading={isAddingCard || formLoading}
                size="lg"
                type="submit"
              >
                登録する
              </Button>
              <Button
                className="skip-btn"
                color="grey"
                onClick={handleSkipAddCard}
                size="lg"
                variant="outline"
              >
                あとで登録する
              </Button>
            </Flex>
          )}
          headerContent={
            <>
              <Text
                color="blackOlive"
                mb={20}
                size={24}
                sx={sx.addCardTitle}
                weight="bold"
              >
                クレジットカードの登録
              </Text>
              <Text
                color="blackOlive"
                mb={38}
                size={16}
                sx={sx.addCardSubTitle}
              >
                {
                  '予約時にご利用いただくクレジットカードをご登録ください。\n予約時に改めてご登録される方は、「あとで登録」を選択ください。'
                }
              </Text>
            </>
          }
          onSubmit={handleAddCard}
        />
      );
    }
    if (step === 3) {
      return (
        <CompleteProfileForm
          initialValues={{
            name: '',
            profilePicture: '',
            gender: '',
            birthday: '',
            email: '',
            invitationCode: null,
            phone: currentUserData?.phone,
          }}
          isCompleteProfile
          loading={isUpdatingProfile}
          onSubmit={hanldeSubmitStep3}
        />
      );
    }
    if (step === 2) {
      return (
        <OtpVerifyForm
          initialValues={{ code: '' }}
          isLoading={isRegistering || isVerifying || isFetching}
          onCancel={() => setStep(1)}
          onResendOtp={handleResendOtp}
          onSubmit={handleStep2}
        />
      );
    }
    return (
      <PhoneForm
        description={
          <>
            すでに会員登録がお済みの方は<Link href="/login">ログイン</Link>
          </>
        }
        label="携帯電話番号で会員登録"
        loading={isSendingOtp || isFetching}
        onSubmit={handleSubmitStep1}
        placeholder="携帯電話番号"
      />
    );
  };

  return (
    <Box sx={sx.registerPageWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `会員登録 | ${suffixTitle}`,
        }}
        title={`会員登録 | ${suffixTitle}`}
      />
      <Flex sx={sx.stepperWrapper}>
        {STEPPER_CONTENTS.map((content) => {
          return (
            <Box
              data-active={step === content.value}
              key={content.value}
              sx={sx.stepBtn}
            >
              {content.label}
            </Box>
          );
        })}
      </Flex>
      {renderStepContent()}
    </Box>
  );
};

RegisterPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const webToken = helpers.getWebCookie(
    req as NextApiRequest,
    res as NextApiResponse,
  );
  const userData = helpers.getUserDataCookie(
    req as NextApiRequest,
    res as NextApiResponse,
  );
  if (webToken.token && userData.isCompletedProfile) {
    return {
      redirect: {
        destination: '/',
        permanent: true,
      },
    };
  }
  return {
    props: {},
  };
};

export default RegisterPage;
