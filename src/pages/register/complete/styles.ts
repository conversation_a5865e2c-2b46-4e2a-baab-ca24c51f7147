import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  registerCompletedWrapper: {
    maxWidth: 1180,
    margin: 'auto',
    padding: '80px 20px',
    '@media (max-width: 768px)': {
      padding: '50px 20px',
    },
  },
  title: {
    '@media (max-width: 768px)': {
      fontSize: 22,
      marginBottom: 14,
    },
  },
  description: {
    '@media (max-width: 768px)': {
      fontSize: 14,
    },
  },
  btnGroup: {
    marginTop: 62,
    a: {
      maxWidth: 300,
      width: '100%',
    },
    '@media (max-width: 768px)': {
      marginTop: 30,
      a: {
        maxWidth: 180,
      },
    },
  },
};
