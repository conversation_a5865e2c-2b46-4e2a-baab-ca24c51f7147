import { Box, Button, Flex, Text } from '@mantine/core';
import Layout from 'components/Layout';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import React from 'react';

import { sx } from './styles';

const RegisterCompleted = () => {
  return (
    <Box sx={sx.registerCompletedWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: '会員登録完了 | HOGUGU（ホググ）',
        }}
        title="会員登録完了 | HOGUGU（ホググ）"
      />
      <Text color="blackOlive" mb={20} size={30} sx={sx.title} weight="bold">
        会員登録完了
      </Text>
      <Text color="blackOlive" size={16} sx={sx.description}>
        会員登録いただきありがとうございます。
        <br />
        ご入力いただいたメールアドレスへ登録完了メールを送信いたしました。
        <br />
        Hoguguのサービスをぜひご活用ください。
      </Text>
      <Flex align="center" direction="column" gap={20} sx={sx.btnGroup}>
        <Button component={Link} href="/my-page" replace size="lg">
          マイページへ
        </Button>
        <Button
          component={Link}
          href={process.env.NEXT_PUBLIC_LP_DOMAIN || '/'}
          replace
          size="lg"
        >
          ホームへ戻る
        </Button>
      </Flex>
    </Box>
  );
};

RegisterCompleted.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default RegisterCompleted;
