import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  registerPageWrapper: {
    maxWidth: 1180,
    margin: 'auto',
    padding: '80px 20px 130px',
    '@media (max-width: 768px)': {
      padding: '50px 20px 47px',
    },
  },
  stepperWrapper: {
    marginBottom: 100,
    '@media (max-width: 768px)': {
      marginBottom: 30,
    },
  },
  stepBtn: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flex: '1 1 25%',
    height: 80,
    borderRadius: 0,
    color: '#767676',
    fontSize: 20,
    fontWeight: 'normal',
    backgroundColor: 'white',
    position: 'relative',
    transform: 'none !important',
    '@media (max-width: 768px)': {
      fontSize: 13,
      height: 50,
    },
    '&[data-active=true]': {
      overflow: 'hidden',
      backgroundColor: '#D6EDFF',
      fontWeight: 'bold',
      color: '#225277',
      '& + *:before': {
        display: 'none',
      },
    },
    '&:first-of-type': {
      borderRadius: '6px 0 0 6px',
    },
    '&:last-of-type': {
      borderRadius: '0 6px 6px 0',
    },
    '&:not(:first-of-type):before': {
      content: '""',
      position: 'absolute',
      top: '50%',
      left: -1,
      height: '30%',
      width: 1,
      transform: 'translate(0, -50%)',
      backgroundColor: '#767676',
    },
  },
  addCardTitle: {
    '@media (max-width: 768px)': {
      marginBottom: 12,
      fontSize: 22,
    },
  },
  addCardSubTitle: {
    whiteSpace: 'normal',
    '@media (max-width: 768px)': {
      marginBottom: 24,
      fontSize: 14,
      whiteSpace: 'pre-line',
    },
  },
  addCardBtnGroup: {
    marginTop: 63,
    '@media (max-width: 768px)': {
      marginTop: 30,
    },
    button: {
      maxWidth: 300,
      width: '100%',
      '@media (max-width: 768px)': {
        maxWidth: 148,
      },
      '&.skip-btn': {
        padding: 0,
      },
    },
  },
};
