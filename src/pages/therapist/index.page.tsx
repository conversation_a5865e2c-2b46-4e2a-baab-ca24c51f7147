import IconChevronRight from '@icons/icon-chevron-right.svg';
import {
  Anchor,
  Box,
  Breadcrumbs,
  Container,
  SimpleGrid,
  Skeleton,
  Text,
  Title,
} from '@mantine/core';
import { dehydrate } from '@tanstack/react-query';
import Layout from 'components/Layout';
import { useFetchList } from 'hooks';
import { get, times } from 'lodash';
import type { IBreadcrumb } from 'models/resource';
import type { ITherapistItem } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import type { GetServerSideProps } from 'next';
import dynamic from 'next/dynamic';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import pino from 'pino';
import dayjs from 'utils/dayjs';
import helpers, { eventLog } from 'utils/helpers';
import getQueryClient, { fetchList } from 'utils/queryClient';
import seoConfig from 'utils/seoConfig';

import { sx } from './styles';

const TherapistCard = dynamic(() =>
  import('components/Therapists').then((r) => r.TherapistCard),
);
const Pagination = dynamic(() => import('components/Pagination'));

const HomePage = ({ enabled }: { enabled: boolean }) => {
  const router = useRouter();
  const { page: pageQuery } = router.query;
  const {
    list,
    total,
    page,
    lastPage,
    isFetching: isLoadingTherapist,
  } = useFetchList<ITherapistItem>({
    ...therapistQuery.getTherapistList,
    enabled,
    customParams: {
      page: typeof pageQuery === 'string' ? Number(pageQuery) : 1,
      limit: 30,
    },
    staleTime: 1000 * 60 * 2,
  });

  const renderList = () => {
    if (isLoadingTherapist) {
      return times(30).map((i) => <TherapistCard isLoading key={i} />);
    }
    return list.map((therapist) => (
      <Link href={`/therapist/${therapist._id}`} key={therapist._id}>
        <TherapistCard
          detail={therapist}
          onClickSeeMenu={(e) => {
            e.preventDefault();
            e.stopPropagation();
            eventLog('display_menus', {
              therapist_id: therapist._id,
            });
            router.push({
              pathname: `/therapist/${therapist._id}`,
              query: {
                viewMenu: true,
              },
            });
          }}
        />
      </Link>
    ));
  };

  const breadcrumbList: IBreadcrumb[] = [
    {
      name: 'HOGUGU',
      href: 'https://hogugu.com',
    },
    {
      name: 'セラピスト一覧',
      href: router.asPath,
    },
  ];

  return (
    <Container size={1340} sx={sx.therapistListContainer}>
      <Head>
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seoConfig.therapistListSchema.webPage),
          }}
          id="therapist-list-web-page"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seoConfig.breadcrumbList(breadcrumbList)),
          }}
          id="breadcrumb-list"
          type="application/ld+json"
        />
      </Head>
      <NextSeo {...seoConfig.therapistList} />
      <Breadcrumbs separator={<IconChevronRight />}>
        {breadcrumbList.map((item, index) => {
          if (item.href && index < breadcrumbList.length - 1) {
            return (
              <Anchor
                component={Link}
                href={item.href}
                key={`anchor-${index}`}
                sx={{ color: 'gray' }}
              >
                {item.name}
              </Anchor>
            );
          }
          return (
            <Text color="black" key={`text-${index}`}>
              {item.name}
            </Text>
          );
        })}
      </Breadcrumbs>

      <Container size={1140} sx={sx.mainContentWrapper}>
        <Box sx={sx.pageTitle}>
          <Title order={1}>セラピスト一覧</Title>
        </Box>
        <Text sx={sx.total}>
          {isLoadingTherapist ? <Skeleton h={28} w="10%" /> : `${total}件`}
        </Text>
        <SimpleGrid
          breakpoints={[
            { maxWidth: 992, cols: 2 },
            { maxWidth: 768, cols: 1 },
          ]}
          cols={3}
          mb={40}
          spacing={24}
          sx={sx.therapistList}
        >
          {renderList()}
        </SimpleGrid>
        {!isLoadingTherapist && <Pagination page={page} total={lastPage} />}
      </Container>
    </Container>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  if (req.url?.startsWith('/_next')) {
    return {
      props: {
        enabled: true,
      },
    };
  }

  try {
    pino().info({ page: req.url, time: dayjs().format() });
    const { page = 1 } = query;
    const queryClient = getQueryClient();
    await fetchList({
      queryClient,
      ...therapistQuery.getTherapistList,
      customParams: {
        ...query,
        page: Number(page),
        limit: 30,
      },
      axiosConfig: helpers.getTokenConfig(req, res),
    });

    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: false,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return {
      notFound: true,
    };
  }
};

HomePage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default HomePage;
