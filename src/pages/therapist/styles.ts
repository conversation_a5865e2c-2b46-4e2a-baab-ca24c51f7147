import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  therapistListContainer: {
    padding: '20px 20px 0',
    '@media (max-width: 768px)': {
      padding: '12px 20px 0',
    },
  },
  pageTitle: {
    display: 'flex',
    justifyContent: 'center',
    margin: '38px 0 20px',
    '@media (max-width: 768px)': {
      margin: '32px 0',
    },
    h1: {
      position: 'relative',
      '@media (max-width: 768px)': {
        fontSize: 24,
      },
      '&:after': {
        content: '" "',
        borderRadius: 3,
        position: 'absolute',
        width: '100%',
        height: 6,
        background:
          'linear-gradient(160deg, #43749a 0%, #43749a 50%, #e8a62d 50%, #e8a62d 100%);',
        bottom: -6,
        left: 0,
      },
    },
  },
  total: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#3C3C3C',
    '@media (max-width: 768px)': {
      marginBottom: 10,
      fontSize: 14,
    },
  },
  mainContentWrapper: {
    padding: '0 0 123px',
    '@media (max-width: 768px)': {
      border: 0,
      padding: '0 0 35px',
    },
  },
  therapistList: {
    marginBottom: 40,
    '@media (max-width: 768px)': {
      marginBottom: 30,
    },
  },
};
