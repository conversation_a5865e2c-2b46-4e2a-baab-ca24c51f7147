import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  therapistDetailHead: {
    backgroundColor: '#FFFFFF',
    padding: '20px 20px 48px',
    [theme.fn.smallerThan('sm')]: {
      padding: '20px 20px 15px',
    },
  },
  breadcrumbs: {
    maxWidth: 1340,
    margin: '0 auto',
  },
  scrollMenu: {
    borderTop: '1px solid #dddddd',
    paddingTop: 20,
    marginTop: 20,
    button: {
      padding: 0,
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.16)',
      backgroundColor: '#FFFFFF',
      color: '#7f7f7f',
      fontSize: 15,
      fontWeight: 'bold',
      border: 'none',
      transform: 'none !important',
      borderRadius: 0,
      '&.active-button': {
        backgroundColor: '#f5f9fb',
        color: '#43749a',
      },
    },
  },
  therapisSeccondaryInfoWrapper: {
    padding: '20px 20px 0',
    [theme.fn.smallerThan('sm')]: {
      padding: '20px 20px 0',
      borderBottom: 'none',
    },
    '& > div': {
      display: 'flex',
      flexDirection: 'column',
      gap: 50,
      paddingBottom: 120,
      [theme.fn.smallerThan('sm')]: {
        paddingBottom: 60,
        gap: 40,
        borderBottom: 'none',
      },
    },
  },
  secondaryInfoRow: {
    gap: 50,
    flexWrap: 'nowrap',
    [theme.fn.smallerThan('sm')]: {
      flexDirection: 'column',
      gap: 40,
    },
  },
  secondaryInfoColumn: {
    '&:nth-of-type(odd)': {
      flex: '0 1 670px',
    },
    '&:nth-of-type(even)': {
      flex: '0 1 420px',
    },
    [theme.fn.smallerThan('sm')]: {
      flex: '1 1 auto !important',
      width: '100%',
    },
  },
  columnTitle: {
    backgroundColor: '#43749a',
    fontSize: 20,
    fontWeight: 'bold',
    padding: '11px 20px',
    color: '#ffffff',
    [theme.fn.smallerThan('sm')]: {
      fontSize: 18,
      padding: '8px 20px',
    },
  },
  confirmModalWrapper: {
    padding: '60px 30px !important',
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 20px 20px !important',
    },
    '.modal-info': {
      gap: 23,
      [theme.fn.smallerThan('sm')]: {
        gap: 16,
      },
      [theme.fn.largerThan('sm')]: {
        '.modal-content br:first-of-type': {
          display: 'none',
        },
      },
    },
    '.modal-btn-group': {
      marginTop: 24,
      gap: 20,
      [theme.fn.smallerThan('sm')]: {
        gap: 16,
      },
    },
  },
}));

export default useStyles;
