import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconMessageNotify from '@icons/icon-message-notify.svg';
import {
  Anchor,
  Box,
  Breadcrumbs,
  Button,
  Container,
  Group,
  Text,
  Title,
} from '@mantine/core';
import { useLocalStorage, useMediaQuery } from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import { dehydrate } from '@tanstack/react-query';
import Layout from 'components/Layout';
import { PrimaryInfoSection } from 'components/Therapists';
import { useFetchData } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import { get } from 'lodash';
import _isEmpty from 'lodash/isEmpty';
import type { IBreadcrumb } from 'models/resource';
import type { IMenuItem, IReviewList, ITherapistItem } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import type { GetServerSideProps } from 'next';
import dynamic from 'next/dynamic';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import pino from 'pino';
import React, { useEffect, useState } from 'react';
import { Link as ScrollLink } from 'react-scroll';
import {
  LOCAL_STORAGE_KEY,
  MIDNIGHT_FEE,
  MIDNIGHT_TIMES,
} from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers, { eventLog } from 'utils/helpers';
import getQueryClient, { fetchData } from 'utils/queryClient';
import seoConfig from 'utils/seoConfig';

import useStyles from './styles';

const CustomerReviewSection = dynamic(() =>
  import('components/Therapists').then((r) => r.CustomerReviewSection),
);
const InfoSection = dynamic(() =>
  import('components/Therapists').then((r) => r.InfoSection),
);
const MenuSelectSection = dynamic(() =>
  import('components/Therapists').then((r) => r.MenuSelectSection),
);

const scrollMenu = [
  {
    to: 'info-section',
    content: 'プロフィール',
  },
  {
    to: 'menu-section',
    content: 'メニュー',
  },
  {
    to: 'review-section',
    content: '口コミ',
  },
];

const TherapistDetail = ({ enabled }: { enabled: boolean }) => {
  const { classes } = useStyles();
  const [rememberAnswer, setRememberAnswer] = useLocalStorage<boolean>({
    key: LOCAL_STORAGE_KEY.THERAPIST_LATE_RESPONSE,
    defaultValue: false,
  });
  const [booking, setBooking, removeBooking] = useLocalStorage<ICreateBooking>({
    key: LOCAL_STORAGE_KEY.BOOKING,
  });
  const router = useRouter();
  const { dateTime, areaNames, areaCodes, id } = router.query;
  const therapistId = typeof id === 'string' ? id : '';
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const [activeMenu, setActiveMenu] = useState('info-section');

  const { data: therapistDetail, isLoading: isLoadingTherapist } =
    useFetchData<ITherapistItem>({
      ...therapistQuery.getTherapistDetail({ id: therapistId }),
      enabled,
      staleTime: 1000 * 60 * 2,
    });
  const { data: therapistMenus, isLoading: isLoadingMenus } = useFetchData<
    IMenuItem[]
  >({
    ...therapistQuery.getTherapistMenus({ id: therapistId }),
    staleTime: 1000 * 60 * 2,
  });
  useEffect(() => {
    if (therapistDetail) {
      eventLog('view_therapist', {
        therapist_id: therapistDetail._id,
        therapist_username: therapistDetail.nickName,
      });
    }
  }, [therapistDetail]);

  const { data: therapistReviews, isLoading: isLoadingReviews } =
    useFetchData<IReviewList>({
      ...therapistQuery.getTherapistReviews({
        id: therapistId,
        therapistDetail: true,
      }),
      staleTime: 1000 * 60 * 2,
      customParams: {
        page: 1,
        limit: 3,
      },
    });

  const handleStartBooking = (values: Pick<ICreateBooking, 'menus'>) => {
    if (!therapistDetail) return;
    if (booking) {
      eventLog('change_therapist', {
        therapist_id: booking.therapistId,
        therapist_username: booking?.therapistName,
        therapist_id_change: therapistDetail._id,
        therapist_username_change: therapistDetail.nickName,
      });
      removeBooking();
    }
    setBooking({
      ...values,
      therapistId: therapistDetail._id,
      therapistName: therapistDetail?.nickName,
      therapistBusy: therapistDetail?.therapistCurrentTreatment?.isBusy,
      areaCodes: typeof areaCodes === 'string' ? areaCodes.split(',') : [],
      areaNames: typeof areaNames === 'string' ? areaNames : '',
      dateBooking: helpers.getValidDate(dateTime),
      cardId: '',
      address: '',
    });

    let sumDuration = 0;
    let sumPrice = 0;
    const menuIds: string[] = [];

    values.menus.forEach((menu) => {
      menuIds.push(menu._id);
      sumDuration += menu.selectedOption?.duration || 0;
      sumPrice += menu.selectedOption?.price || 0;
    });

    eventLog('begin_checkout', {
      menu_id: menuIds.join(','),
      duration: sumDuration,
      value: sumPrice,
      midnight_price:
        dateTime && MIDNIGHT_TIMES.includes(dayjs(dateTime).format('HH:mm'))
          ? MIDNIGHT_FEE
          : 0,
    });
    router.push('/booking');
  };

  const handleConfirmStartBooking = (values: Pick<ICreateBooking, 'menus'>) => {
    if (rememberAnswer || !therapistDetail?.therapistCurrentTreatment?.isBusy) {
      handleStartBooking(values);
      return;
    }
    openContextModal({
      modal: 'AlertModal',
      size: 630,
      innerProps: {
        icon: <IconMessageNotify />,
        className: classes.confirmModalWrapper,
        content: (
          <>
            選択されたセラピストは現在、
            <br />
            他のお客様を施術中のため、
            <br />
            返信に時間がかかる場合があります。
            <br />
            このまま予約リクエストを続けますか?
          </>
        ),
        rememberCheckboxText: '次回から表示しない',
        haveActionBtn: true,
        cancelText: 'キャンセル',
        confirmText: 'OK',
        onConfirm: (remember: boolean) => {
          if (remember) setRememberAnswer(true);
          handleStartBooking(values);
        },
      },
      withCloseButton: false,
      centered: true,
    });
  };

  const breadcrumbList: IBreadcrumb[] = [
    {
      name: 'HOGUGU',
      href: 'https://hogugu.com',
    },
    {
      name: 'セラピスト一覧',
      href: '/therapist',
    },
    {
      name: 'セラピスト詳細',
      href: router.asPath.split('?')[0],
    },
  ];

  const pageSchema = seoConfig.therapistDetailSchema(therapistDetail);

  return (
    <Box>
      <Head>
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema.webPageList),
          }}
          id="therapist-detail-web-page-list"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema.webPage),
          }}
          id="therapist-detail-web-page"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema.person),
          }}
          id="therapist-detail-person"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seoConfig.breadcrumbList(breadcrumbList)),
          }}
          id="breadcrumb-list"
          type="application/ld+json"
        />
      </Head>
      <NextSeo
        {...seoConfig.therapistDetail(therapistDetail)}
        nofollow={_isEmpty(therapistDetail)}
        noindex={_isEmpty(therapistDetail)}
      />
      <Box className={classes.therapistDetailHead}>
        <Breadcrumbs
          className={classes.breadcrumbs}
          separator={<IconChevronRight />}
        >
          {breadcrumbList.map((item, index) => {
            if (item.href && index < breadcrumbList.length - 1) {
              return (
                <Anchor
                  component={Link}
                  href={item.href}
                  key={`anchor-${index}`}
                  sx={{ color: 'gray' }}
                >
                  {item.name}
                </Anchor>
              );
            }
            return (
              <Text color="black" key={`text-${index}`}>
                {item.name}
              </Text>
            );
          })}
        </Breadcrumbs>
        <PrimaryInfoSection
          detail={therapistDetail}
          loading={isLoadingTherapist}
          summaryReview={therapistReviews?.summaryReview}
        />
        {mobileScreen && (
          <Group className={classes.scrollMenu} grow spacing={3}>
            {scrollMenu.map((menu) => (
              <ScrollLink
                key={menu.to}
                offset={-80}
                onClick={() => {
                  if (menu.to === 'menu-section' && therapistDetail?._id) {
                    eventLog('display_menus', {
                      therapist_id: therapistDetail._id,
                    });
                  }
                }}
                onSetActive={(to) => setActiveMenu(to)}
                spy
                to={menu.to}
              >
                <Button
                  className={activeMenu === menu.to ? 'active-button' : ''}
                  fullWidth
                  variant="outline"
                >
                  {menu.content}
                </Button>
              </ScrollLink>
            ))}
          </Group>
        )}
      </Box>
      <Box className={classes.therapisSeccondaryInfoWrapper}>
        <Container p={0} size={1140}>
          <Group align="start" className={classes.secondaryInfoRow}>
            <InfoSection
              className={classes.secondaryInfoColumn}
              detail={therapistDetail}
              loading={isLoadingTherapist}
              title={
                <Title className={classes.columnTitle} order={3}>
                  プロフィール
                </Title>
              }
            />
            <MenuSelectSection
              className={classes.secondaryInfoColumn}
              handleStartBooking={handleConfirmStartBooking}
              loading={isLoadingMenus}
              menus={therapistMenus}
              minMenu={therapistDetail?.minMenu}
              therapistId={therapistId}
              title={
                <Title className={classes.columnTitle} order={3}>
                  メニュー
                </Title>
              }
            />
          </Group>
          <Group align="start" className={classes.secondaryInfoRow}>
            <CustomerReviewSection
              className={classes.secondaryInfoColumn}
              loading={isLoadingReviews}
              reviewInfo={therapistReviews}
              therapistId={therapistDetail?._id}
              title={
                <Title className={classes.columnTitle} order={3}>
                  口コミ
                </Title>
              }
            />
          </Group>
        </Container>
      </Box>
    </Box>
  );
};

TherapistDetail.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
}) => {
  const { id } = query;
  if (req.url?.startsWith('/_next')) {
    return {
      props: {
        enabled: true,
      },
    };
  }
  if (typeof id !== 'string') return { notFound: true };
  pino().info({
    thrapistId: id,
    type: 'detail',
    time: dayjs().format(),
  });
  try {
    const queryClient = getQueryClient();
    const detail = await fetchData({
      queryClient,
      ...therapistQuery.getTherapistDetail({ id }),
    });
    if (_isEmpty(detail) || detail.status === 'LOCKED') {
      return {
        notFound: true,
      };
    }
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: false,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return {
      notFound: true,
    };
  }
};

export default TherapistDetail;
