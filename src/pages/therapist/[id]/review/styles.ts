import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  therapistReviewsContainer: {},
  therapistReviewsHead: {
    backgroundColor: '#FFFFFF',
    padding: '20px 20px',
    '@media (max-width: 768px)': {
      padding: '20px 20px 15px',
    },
  },
  breadcrumbs: {
    maxWidth: 1340,
    margin: '0 auto',
  },
  reviewListWrapper: {
    padding: '20px 20px 0',
    '@media (max-width: 768px)': {
      padding: '20px 20px 0',
    },
    '& > div': {
      paddingBottom: 98,
      '@media (max-width: 768px)': {
        paddingBottom: 35,
        borderBottom: 'none',
      },
    },
  },
  avgRating: {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 'bold',
    color: '#3c3c3c',
    gap: 8,
    fontSize: 28,
    whiteSpace: 'nowrap',
    svg: {
      width: 24,
      height: 24,
    },
  },
  totalRating: {
    display: 'flex',
    alignItems: 'center',
    color: '#4d4d4d',
    fontWeight: 'bold',
    fontSize: 12,
    gap: 7,
    svg: {
      width: 14,
      height: 14,
    },
  },
};
