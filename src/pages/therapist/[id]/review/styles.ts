import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  therapistReviewsContainer: {},
  therapistReviewsHead: {
    backgroundColor: '#FFFFFF',
    padding: '20px 20px',
    '@media (max-width: 768px)': {
      padding: '20px 20px 15px',
    },
  },
  breadcrumbs: {
    maxWidth: 1340,
    margin: '0 auto',
  },
  reviewListWrapper: {
    padding: '20px 20px 0',
    '@media (max-width: 768px)': {
      padding: '20px 20px 0',
    },
    '& > div': {
      paddingBottom: 98,
      '@media (max-width: 768px)': {
        paddingBottom: 35,
        borderBottom: 'none',
      },
    },
  },
  avgRating: {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 'bold',
    color: '#3c3c3c',
    gap: 8,
    fontSize: 28,
    whiteSpace: 'nowrap',
    svg: {
      width: 24,
      height: 24,
    },
  },
  totalRating: {
    display: 'flex',
    alignItems: 'center',
    color: '#4d4d4d',
    fontWeight: 'bold',
    fontSize: 12,
    gap: 7,
    svg: {
      width: 14,
      height: 14,
    },
  },
  ratingSectionWrapper: {
    paddingTop: 16,
    paddingBottom: 24,
    borderBottom: '1px solid #DDDDDD',
    marginBottom: 32,
    '@media (max-width: 768px)': {
      paddingTop: 8,
      paddingBottom: 0,
      marginBottom: 16,
    },
  },
  ratingSection: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: 24,
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      gap: 16,
    },
  },
  ratingSummary: {
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
    flexShrink: 0,
    '@media (max-width: 768px)': {
      width: '100%',
    },
  },
  ratingDetailWrapper: {
    padding: '8px 0px 16px 0px',
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
    '@media (min-width: 769px)': {
      padding: '8px 16px 0px 16px',
      marginTop: 0,
      flex: 1,
      maxWidth: 400,
    },
    '@media (max-width: 768px)': {
      width: '100%',
      maxWidth: 400,
    },
  },
  starRow: {
    alignItems: 'center',
    width: '100%',
    display: 'flex',
    gap: 8,
  },
  starLabel: {
    fontSize: 14,
    minWidth: 50,
    color: '#3c3c3c',
    fontWeight: 400,
    flexShrink: 0,
  },
  starBar: {
    flex: 1,
    height: 8,
    '.mantine-Progress-bar': {
      backgroundColor: '#FFA90A',
      borderRadius: 6,
    },
    '.mantine-Progress-root': {
      backgroundColor: '#E7EEF3',
      borderRadius: 6,
      height: 8,
    },
  },
  starCount: {
    fontSize: 14,
    minWidth: 35,
    textAlign: 'right',
    color: '#3c3c3c',
    fontWeight: 700,
    flexShrink: 0,
  },
};
