import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconMessage from '@icons/icon-message.svg';
import {
  Anchor,
  Box,
  Breadcrumbs,
  Container,
  Rating,
  SimpleGrid,
  Stack,
  Text,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { dehydrate } from '@tanstack/react-query';
import Layout from 'components/Layout';
import Pagination from 'components/Pagination';
import ReviewCell from 'components/ReviewCell';
import ReviewSkeleton from 'components/ReviewCell/ReviewSkeleton';
import { ReviewPrimaryInfoSection } from 'components/Therapists';
import { useFetchData } from 'hooks';
import { get, times } from 'lodash';
import _isEmpty from 'lodash/isEmpty';
import _toNumber from 'lodash/toNumber';
import type { IBreadcrumb } from 'models/resource';
import type { IReviewList, ITherapistItem } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import type { GetServerSideProps } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import pino from 'pino';
import React from 'react';
import dayjs from 'utils/dayjs';
import getQueryClient, { fetchData } from 'utils/queryClient';
import seoConfig from 'utils/seoConfig';

import { sx } from './styles';

const TherapistReviews = ({ enabled }: { enabled: boolean }) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)', false, {
    getInitialValueInEffect: false,
  });
  const router = useRouter();
  const page = typeof router.query.page === 'string' ? router.query.page : '1';
  const therapistId =
    typeof router.query.id === 'string' ? router.query.id : '';

  const { data: therapistDetail, isLoading: isLoadingTherapist } =
    useFetchData<ITherapistItem>({
      ...therapistQuery.getTherapistDetail({ id: therapistId }),
      enabled,
    });
  const { data: therapistReviews, isLoading: isLoadingReviews } =
    useFetchData<IReviewList>({
      ...therapistQuery.getTherapistReviews({
        id: therapistId,
        page,
      }),
      customParams: {
        page,
      },
    });

  const reviewList = therapistReviews?.reviews.data || [];
  const lastPage = therapistReviews?.reviews.lastPage || 1;

  const rating =
    (therapistReviews?.summaryReview?.sumRating || 0) /
      (therapistReviews?.summaryReview?.sumReviewer || 0) || 0;

  const breadcrumbList: IBreadcrumb[] = [
    {
      name: 'HOGUGU',
      href: 'https://hogugu.com',
    },
    {
      name: 'セラピスト一覧',
      href: '/therapist',
    },
    {
      name: 'セラピスト詳細',
      href: `/therapist/${therapistId}`,
    },
    {
      name: '口コミ',
      href: router.asPath,
    },
  ];

  const pageSchema = seoConfig.therapistReviewListSchema(therapistDetail);

  const renderReviews = () => {
    if (isLoadingReviews) {
      return (
        <Container p={0} size={1140}>
          <Head>
            <script
              dangerouslySetInnerHTML={{
                __html: JSON.stringify(pageSchema.webPage),
              }}
              id="therapist-review-list-web-page"
              type="application/ld+json"
            />
            <script
              dangerouslySetInnerHTML={{
                __html: JSON.stringify(
                  seoConfig.breadcrumbList(breadcrumbList),
                ),
              }}
              id="breadcrumb-list"
              type="application/ld+json"
            />
          </Head>
          <SimpleGrid
            breakpoints={[
              {
                maxWidth: 768,
                cols: 1,
                spacing: 10,
                verticalSpacing: 10,
              },
            ]}
            cols={2}
            mb={40}
            spacing={20}
          >
            {times(4).map((index) => (
              <ReviewSkeleton key={index} />
            ))}
          </SimpleGrid>
        </Container>
      );
    }
    return (
      <Container p={0} size={1140}>
        <Head>
          <script
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(pageSchema.webPage),
            }}
            id="therapist-review-list-web-page"
            type="application/ld+json"
          />
          <script
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(seoConfig.breadcrumbList(breadcrumbList)),
            }}
            id="breadcrumb-list"
            type="application/ld+json"
          />
        </Head>
        {mobileScreen && (
          <Stack mb={15} px={20} spacing={0}>
            <Text sx={sx.avgRating}>
              {rating.toFixed(1)}
              <Rating
                fractions={5}
                readOnly
                value={_toNumber(rating.toFixed(1))}
              />
            </Text>
            <Text sx={sx.totalRating}>
              <IconMessage />
              {therapistReviews?.summaryReview?.sumReviewer || 0}件
            </Text>
          </Stack>
        )}
        <SimpleGrid
          breakpoints={[
            {
              maxWidth: 768,
              cols: 1,
              spacing: 10,
              verticalSpacing: 10,
            },
          ]}
          cols={2}
          mb={40}
          spacing={20}
        >
          {reviewList.map((review) => (
            <ReviewCell info={review} key={review._id} />
          ))}
        </SimpleGrid>
        <Pagination page={Number(page)} perPage={20} total={lastPage} />
      </Container>
    );
  };

  return (
    <Box sx={sx.therapistReviewsContainer}>
      <NextSeo
        {...seoConfig.therapistReview(therapistDetail)}
        nofollow={_isEmpty(therapistDetail)}
        noindex={_isEmpty(therapistDetail)}
      />
      <Box sx={sx.therapistReviewsHead}>
        <Breadcrumbs separator={<IconChevronRight />} sx={sx.breadcrumbs}>
          {breadcrumbList.map((item, index) => {
            if (item.href && index < breadcrumbList.length - 1) {
              return (
                <Anchor
                  component={Link}
                  href={item.href}
                  key={`anchor-${index}`}
                  sx={{ color: 'gray' }}
                >
                  {item.name}
                </Anchor>
              );
            }
            return (
              <Text color="black" key={`text-${index}`}>
                {item.name}
              </Text>
            );
          })}
        </Breadcrumbs>
        <ReviewPrimaryInfoSection
          detail={therapistDetail}
          loading={isLoadingReviews || isLoadingTherapist}
          summaryReview={therapistReviews?.summaryReview}
        />
      </Box>
      <Box sx={sx.reviewListWrapper}>{renderReviews()}</Box>
    </Box>
  );
};

TherapistReviews.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
}) => {
  const { id } = query;
  if (req.url?.startsWith('/_next')) {
    return {
      props: {
        enabled: true,
      },
    };
  }
  if (typeof id !== 'string') return { notFound: true };
  try {
    pino().info({
      thrapistId: id,
      type: 'review',
      time: dayjs().format(),
    });
    const queryClient = getQueryClient();
    const detail = await fetchData({
      queryClient,
      ...therapistQuery.getTherapistDetail({ id }),
    });
    if (_isEmpty(detail) || detail.status === 'LOCKED') {
      return {
        notFound: true,
      };
    }
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: false,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return {
      notFound: true,
    };
  }
};

export default TherapistReviews;
