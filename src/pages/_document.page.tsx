import { createGetInitialProps } from '@mantine/next';
import Document, { Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';

const getInitialProps = createGetInitialProps();

class MyDocument extends Document {
  static getInitialProps = getInitialProps;

  // eslint-disable-next-line class-methods-use-this
  render() {
    return (
      <Html lang="ja">
        <Head>
          <link
            href="https://hogugu-prod-webapp.s3.ap-northeast-1.amazonaws.com/favicon_9a9853ef97_f9d4f6da55_f1d1261795.ico"
            rel="shortcut icon"
          />
          <Script
            dangerouslySetInnerHTML={{
              __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl+'&gtm_auth=${process.env.GTM_AUTH}&gtm_preview=${process.env.GTM_PREVIEW}&gtm_cookies_win=${process.env.GTM_COOKIES_WIN}';f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','${process.env.GTM_ID}');`,
            }}
            id="google-tag-manager"
            strategy="afterInteractive"
          />
          <Script
            async
            id="google-tag-src"
            src={`https://www.googletagmanager.com/gtag/js?id=${process.env.GA_MEASUREMENT_ID}`}
            strategy="afterInteractive"
          />
          <Script
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${process.env.GA_MEASUREMENT_ID}');
              `,
            }}
            id="google-tag-func"
            strategy="afterInteractive"
          />
          <script
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'http://schema.org',
                '@type': 'Organization',
                '@id': 'https://hogugu.com/about.html#organization',
                name: '株式会社HOGUGUテクノロジーズ',
                url: 'https://hogugu.com/about.html',
                sameAs: [
                  'https://media.hogugu.com/about',
                  'https://initial.inc/companies/A-34409',
                  'https://www.nikkei.com/telecom/company/jZ5PUrKjkVDt7PBzzVJMCL',
                  'https://prtimes.jp/main/html/searchrlp/company_id/47992',
                ],
                founder: {
                  '@type': 'Person',
                  name: '花岡賢一',
                  sameAs: [
                    'https://www.beautopia.jp/44664/',
                    'https://c2c-platform.com/archives/1189/',
                  ],
                },
                foundingDate: '2018-12-20T00:00:00+09:00',
                ownershipFundingInfo:
                  'https://prtimes.jp/main/html/rd/p/*********.*********.html',
              }),
            }}
            id="organization-data"
            type="application/ld+json"
          />
          <script
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'http://schema.org',
                '@type': 'WebSite',
                '@id': 'https://hogugu.com#website',
                url: 'https://hogugu.com',
                name: 'HOGUGU（ホググ）',
                publisher: [
                  {
                    '@id': 'https://hogugu.com/about.html#organization',
                  },
                ],
                description:
                  'HOGUGU（ホググ）はセラピストを選べる出張リラクゼーションサービスです。好きな時間や場所でアプリから予約してリラクゼーションを受けることが出来ます。面談・施術チェックをクリアした厳選されたセラピストに直接オファーできます。',
              }),
            }}
            id="website-data"
            type="application/ld+json"
          />
        </Head>
        <body>
          <noscript
            dangerouslySetInnerHTML={{
              __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.GTM_ID}&gtm_auth=${process.env.GTM_AUTH}&gtm_preview=${process.env.GTM_PREVIEW}&gtm_cookies_win=${process.env.GTM_COOKIES_WIN}"
              height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
            }}
          />
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
