import type { <PERSON><PERSON>ust<PERSON> } from 'models/auth';
import type { NextApiRequest, NextApiResponse } from 'next';
import helpers from 'utils/helpers';
import request from 'utils/request';
import type { IError } from 'utils/type';

async function getUserProfile(req: NextApiRequest, res: NextApiResponse) {
  try {
    const response = await request<ICustomer>({
      method: 'get',
      url: '/account-api/mobile/customer/me',
      config: {
        ...helpers.getTokenConfig(req, res),
      },
    });

    helpers.setUserData(
      {
        displayName: response.data.name || '',
        isCompletedProfile: !!response.data.isCompletedProfile,
        requestingDeleteAccount: !!response.data.requestingDeleteAccount,
      },
      {
        req,
        res,
      },
    );

    res.send(response);
  } catch (e) {
    const { code, ...rest } = e as IError;
    res.status(code || 404).send({ code, ...rest });
  }
}
export default getUserProfile;
