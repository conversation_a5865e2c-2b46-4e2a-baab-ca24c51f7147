import type { ICityItem, IPrefectureItem } from 'models/resource';
import type { IMenuItem } from 'models/therapist';
import type { NextApiRequest, NextApiResponse } from 'next';
import { GENDER, SORT_ORDER_TEXT } from 'utils/constants';
import dayjs from 'utils/dayjs';
import request from 'utils/request';

interface DynamicUrl {
  loc: string;
  priority: number;
}

const Sitemap = async (req: NextApiRequest, res: NextApiResponse) => {
  const currentDate = dayjs().tz('Asia/Tokyo').format('YYYY年M月D日 h:mm A');
  const page = parseInt(req.query.page as string, 10) || 1; // Get the page number from the query parameter
  const areasPerPage = 1; // Number of level 1 areas to process per page

  const [prefectures, menus] = await Promise.all([
    request({
      method: 'get',
      url: '/account-api/web/customer/group-areas',
    }),
    request({
      method: 'get',
      url: '/booking-api/mobile/customer/menu',
    }),
  ]);

  // Generate dynamic URLs
  const dynamicUrls: DynamicUrl[] = [];
  const genders = Object.keys(GENDER);
  const sorts = Object.keys(SORT_ORDER_TEXT);

  const generateUrl = (area: IPrefectureItem | ICityItem, prefix: String) => {
    menus.data.forEach((menu: IMenuItem) => {
      if (!menu.titleEn) return;
      genders.forEach((gender) => {
        sorts.forEach((sortOrder) => {
          dynamicUrls.push({
            loc: `${prefix}/${area.nameEn}?selectMenu=${menu.titleEn}&gender=${gender}&sort=${sortOrder}`,
            priority: 1,
          });
        });
      });
    });
    if (area.level < 2) {
      area.children?.forEach((child) => generateUrl(child, `/${area.nameEn}`));
    }
  };

  // Paginate the level 1 areas (5 per page)
  const startIndex = (page - 1) * areasPerPage;
  const endIndex = startIndex + areasPerPage;
  const paginatedAreas = prefectures.data.slice(startIndex, endIndex);

  // Generate URLs for the paginated areas and their children
  paginatedAreas.forEach((item: IPrefectureItem) => {
    generateUrl(item, '');
  });

  // Generate XML content
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="/default.xsl"?>
<urlset
  xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
  xmlns:xhtml="http://www.w3.org/1999/xhtml"
  xmlns:image="https://www.google.com/schemas/sitemap-image/1.1">
  <meta>
    <now><![CDATA[${currentDate}]]></now>
    <domain><![CDATA[${process.env.NEXT_PUBLIC_DOMAIN}]]></domain>
  </meta>
  ${dynamicUrls
    .map(
      (url) => `
    <url>
      <loc><![CDATA[${process.env.NEXT_PUBLIC_DOMAIN}${url.loc}]]></loc>
      <lastmod><![CDATA[${dayjs().toISOString()}]]></lastmod>
      <changefreq><![CDATA[daily]]></changefreq>
      <priority><![CDATA[${url.priority}]]></priority>
    </url>
  `,
    )
    .join('')}
</urlset>`;

  // Set response headers and send XML content
  res.setHeader('Content-Type', 'application/xml');
  res.status(200).send(sitemap);
};

export default Sitemap;
