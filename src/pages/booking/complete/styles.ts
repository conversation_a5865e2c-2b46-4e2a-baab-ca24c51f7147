import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  bookingCompleteWrapper: {
    maxWidth: 1180,
    margin: 'auto',
    padding: '60px 20px',
    [theme.fn.smallerThan('sm')]: {
      padding: '32px 20px',
    },
  },
  title: {
    fontSize: 30,
    fontWeight: 700,
    marginBottom: 24,
    lineHeight: '42px',
    [theme.fn.smallerThan('sm')]: {
      fontSize: 20,
      marginBottom: 8,
      lineHeight: '34px',
    },
  },
  description: {
    fontSize: 16,
    fontWeight: 400,
    lineHeight: '24px',
    [theme.fn.smallerThan('sm')]: {
      fontSize: 14,
      lineHeight: '20px',
    },
  },
  btnGroup: {
    marginTop: 60,
    a: {
      maxWidth: 300,
      width: '100%',
    },
    [theme.fn.smallerThan('sm')]: {
      marginTop: 24,
      a: {
        maxWidth: 180,
      },
    },
  },
}));

export default useStyles;
