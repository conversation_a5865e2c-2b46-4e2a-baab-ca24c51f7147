import { Box, Button, Flex, Text } from '@mantine/core';
import { useLocalStorage, useWindowEvent } from '@mantine/hooks';
import Layout from 'components/Layout';
import type { ICreateBooking } from 'hooks/types';
import get from 'lodash/get';
import type { GetServerSideProps } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import React, { useEffect } from 'react';
import { LOCAL_STORAGE_KEY } from 'utils/constants';
import { eventLog } from 'utils/helpers';

import useStyles from './styles';

const RegisterCompleted = () => {
  const { classes } = useStyles();
  const router = useRouter();

  const [booking, , removeBooking] = useLocalStorage<ICreateBooking>({
    key: LOCAL_STORAGE_KEY.BOOKING,
  });

  useEffect(() => {
    const handleRouteChange = () => {
      removeBooking();
    };
    router.events.on('routeChangeComplete', handleRouteChange);
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [removeBooking, router]);

  useWindowEvent('beforeunload', () => {
    removeBooking();
  });

  return (
    <Box className={classes.bookingCompleteWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: '予約リクエスト送信完了 | HOGUGU（ホググ）',
        }}
        title="予約リクエスト送信完了 | HOGUGU（ホググ）"
      />
      <Text className={classes.title} color="blackOlive">
        予約リクエスト送信完了
      </Text>
      <Text
        className={classes.description}
        color="blackOlive"
        mb={16}
        size={16}
      >
        リクエストを受け付けし、予約リクエスト完了メールを送信しました。
      </Text>
      <Text
        className={classes.description}
        color={booking?.therapistBusy ? 'marigold' : 'blackOlive'}
        size={16}
      >
        {booking?.therapistBusy
          ? '予約はまだ完了しておりません。\nセラピストからのリクエスト承認をお待ちください。\n現在、他のお客様を施術中のため、返信に時間がかかる場合があります。'
          : '予約はまだ完了しておりません。\nセラピストからのリクエスト承認をお待ちください。'}
      </Text>
      <Flex
        align="center"
        className={classes.btnGroup}
        direction="column"
        gap={{ base: 16, sm: 20 }}
      >
        <Button
          component={Link}
          href="/my-page/booking-history"
          replace
          size="lg"
        >
          予約履歴一覧へ
        </Button>
        <Button
          component={Link}
          href={process.env.NEXT_PUBLIC_LP_DOMAIN || '/'}
          replace
          size="lg"
        >
          ホームへ戻る
        </Button>
      </Flex>
    </Box>
  );
};

RegisterCompleted.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  try {
    const { action, bookingId, code } = query;
    if (action === 'createBooking' && bookingId && code === '200') {
      eventLog('request', {
        booking_id: bookingId,
      });
      return {
        redirect: {
          destination: '/booking/complete',
          permanent: false,
        },
      };
    }
    return {
      props: {
        enabled: true,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return { notFound: true };
  }
};

export default RegisterCompleted;
