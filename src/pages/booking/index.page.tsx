import IconTherapistBusy from '@icons/icon-therapist-busy.svg';
import {
  Box,
  Button,
  Checkbox,
  Container,
  Flex,
  Text,
  Title,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import {
  MemoNote,
  MenuCoupon,
  PaymentMethod,
  TherapistInfo,
  TreatmentPlace,
} from 'components/Bookings';
import Layout from 'components/Layout';
import { useFetchData, useMutate, useUser } from 'hooks';
import { get } from 'lodash';
import type { IBookingDetail, ICheckPointAmount } from 'models/booking';
import { bookingQuery } from 'models/booking';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import React, { useEffect, useState } from 'react';
import { eventLog } from 'utils/helpers';
import notification from 'utils/notification';

import Error from './Error';
import useStyles from './styles';
import useBooking from './useBooking';

const BookingPage = () => {
  const router = useRouter();
  const { query } = router;
  const { classes } = useStyles();
  const mobileScreen = useMediaQuery('(max-width: 768px)', true, {
    getInitialValueInEffect: false,
  });

  const [checked, setChecked] = useState(false);
  const { data: currentUser } = useUser();
  const {
    booking,
    handleChange,
    therapistDetail,
    therapistReviews,
    isMidnight,
    isSlotError,
    isPlaceError,
    isPlaceEmptyError,
    isMenuError,
    isPaymentError,
    sumPrice,
    promotionPrice,
    sumDuration,
    getBookingData,
    totalPoints,
    sumPriceWithPoint,
    isInitMenu,
  } = useBooking();

  const { data: checkPointAmount, refetch: refetchCheckPoint } =
    useFetchData<ICheckPointAmount>({
      ...bookingQuery.checkPoints,
      enabled: false,
      customParams: {
        totalPrice: sumPrice,
        point: 1,
      },
    });

  useEffect(() => {
    if (sumPrice && isInitMenu) {
      refetchCheckPoint();
    }
  }, [isInitMenu, refetchCheckPoint, sumPrice]);

  const { mutateAsync: createBooking, isLoading } = useMutate<
    unknown,
    IBookingDetail
  >({
    ...bookingQuery.createBooking,
  });

  useEffect(() => {
    const handleRouteChange = (url: string) => {
      if (url !== '/booking/complete') {
        eventLog('remove_cart', {
          menu_id: booking?.menus.map((menu) => menu._id),
          duration: sumDuration,
          price: sumPrice,
        });
      }
    };
    router.events.on('routeChangeStart', handleRouteChange);
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [booking?.menus, router, sumDuration, sumPrice]);

  useEffect(() => {
    if (query.error) {
      notification.show({ type: 'error', message: query.error });
    }
  }, [query.error]);

  const handleCreateBooking = async () => {
    try {
      const bookingData = getBookingData();
      const bookingDetail = await createBooking({
        customer: {
          id: currentUser?._id,
          name: currentUser?.name,
          phone: currentUser?.phone,
          gender: currentUser?.gender,
        },
        ...bookingData,
        redirectUrl: {
          fail: `${process.env.NEXT_PUBLIC_DOMAIN}/booking`,
          success: `${process.env.NEXT_PUBLIC_DOMAIN}/booking/complete`,
        },
      });
      window.location.href = bookingDetail.RedirectUrl;
    } catch (e) {
      eventLog('booking_error', {
        error_message: get(e, 'error'),
      });
    }
  };

  return (
    <Container p={{ base: '24px 20px', sm: '60px 20px' }} size={1180}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: '予約リクエスト入力・確認 | HOGUGU（ホググ）',
        }}
        title="予約リクエスト入力・確認 | HOGUGU（ホググ）"
      />
      {(therapistDetail?.therapistCurrentTreatment?.isBusy ||
        booking?.therapistBusy) && (
        <Text className={classes.therapistBusyBadge}>
          <IconTherapistBusy />
          <span>
            現在、他のお客様を施術中のため、返信に時間がかかる場合があります。
          </span>
        </Text>
      )}
      <Flex justify="space-between">
        <Box>
          <Title color="blackOlive" mb={22} order={1} size={30}>
            予約リクエスト入力・確認
          </Title>
          <Text color="blackOlive" mb={32} size={16}>
            予約内容を入力し、内容をご確認の上送信してください。
          </Text>
        </Box>
        {/* <Text size={18}>#123456789</Text> */}
      </Flex>
      <Flex className={classes.sectionRow} gap={20}>
        {/* Left column */}
        <Flex direction="column" gap={32}>
          <Box className={classes.sectionWrapper}>
            <Title className="title" order={3}>
              セラピスト
            </Title>
            <TherapistInfo
              detail={therapistDetail}
              summaryReview={therapistReviews?.summaryReview}
            />
          </Box>
          <Box className={classes.sectionWrapper}>
            <Title className="title" order={3}>
              日時・場所
            </Title>
            <TreatmentPlace
              booking={booking}
              isMidnight={isMidnight}
              onChange={handleChange}
              sumDuration={sumDuration}
            />
            {isSlotError && (
              <Error
                content={
                  <span>
                    ご指定のセラピストでは、選択できない日時です。
                    <br />
                    日時を変更するか、別のセラピストを選択してください。
                  </span>
                }
              />
            )}
            {(isPlaceError || isPlaceEmptyError) && (
              <Error
                content={
                  <span>
                    {isPlaceEmptyError
                      ? 'セラピストが訪問する住所を入力してください。'
                      : '申し訳ございません。現在、この住所はご利用できないエリアです。'}
                  </span>
                }
              />
            )}
          </Box>
          {mobileScreen && (
            <Box className={classes.sectionWrapper} display={{ sm: 'none' }}>
              <Title className="title" order={3}>
                メニュー
              </Title>
              <MenuCoupon
                booking={booking}
                checkPointAmount={checkPointAmount}
                onChange={handleChange}
                promotionPrice={promotionPrice}
                sumDuration={sumDuration}
                sumPrice={sumPrice}
                sumPriceWithPoint={sumPriceWithPoint}
                totalPoints={totalPoints}
              />
              {isMenuError && (
                <Error
                  content={
                    <span>
                      合計{therapistDetail?.minMenu?.duration}
                      分以上の予約からご利用いただけます。
                    </span>
                  }
                />
              )}
              {booking?.couponError && (
                <Error content={<span>{booking.couponError}</span>} />
              )}
            </Box>
          )}
          <Box className={classes.sectionWrapper}>
            <Title className="title" order={3}>
              お支払い方法
            </Title>
            <PaymentMethod booking={booking} onChange={handleChange} />
            {isPaymentError && (
              <Error content={<span>お支払い方法を登録してください。</span>} />
            )}
          </Box>
          <Box className={classes.sectionWrapper}>
            <Title className="title" order={3}>
              追記事項（任意）
            </Title>
            <MemoNote booking={booking} onChange={handleChange} />
          </Box>
        </Flex>
        {/* Right column */}
        {!mobileScreen && (
          <Box className={classes.sectionWrapper}>
            <Title className="title" order={3}>
              メニュー
            </Title>
            <MenuCoupon
              booking={booking}
              checkPointAmount={checkPointAmount}
              onChange={handleChange}
              promotionPrice={promotionPrice}
              sumDuration={sumDuration}
              sumPrice={sumPrice}
              sumPriceWithPoint={sumPriceWithPoint}
              totalPoints={totalPoints}
            />
            {isMenuError && (
              <Error
                content={
                  <span>
                    合計{therapistDetail?.minMenu?.duration}
                    分以上の予約からご利用いただけます。
                  </span>
                }
              />
            )}
            {booking?.couponError && (
              <Error content={<span>{booking.couponError}</span>} />
            )}
          </Box>
        )}
      </Flex>
      <Box className={classes.sectionWrapper}>
        <Title className="title" order={3}>
          各種規約・キャンセルポリシー
        </Title>
        <Text className={classes.termsNote} color="blackOlive" size={16}>
          「予約リクエストを送信する」ボタンを押していただくことで、お客様は当サイトの
          <Link href="/terms" rel="noreferrer" target="_blank">
            各種規約
          </Link>
          、
          <Link
            href={`${process.env.NEXT_PUBLIC_LP_URL}/terms-of-use/cancelation-policy.html`}
            rel="noreferrer"
            target="_blank"
          >
            キャンセルポリシー
          </Link>
          に同意の上、施術のご予約をリクエストされたことになります。必ず内容をご確認ください。
        </Text>
        <Checkbox
          checked={checked}
          className={classes.termsCheck}
          label="各種規約、キャンセルポリシーに同意します"
          onChange={(event) => setChecked(event.currentTarget.checked)}
        />
      </Box>
      <Flex className={classes.btnGroup} justify="space-between">
        <Button
          className="cancel-btn"
          color="grey"
          component={Link}
          href={`/therapist/${therapistDetail?._id}`}
          onClick={() => {
            router.push(`/therapist/${therapistDetail?._id}`);
          }}
          variant="outline"
        >
          セラピスト詳細に戻る
        </Button>
        <Button
          color="marigold"
          disabled={
            isMenuError ||
            isSlotError ||
            isPlaceError ||
            isPlaceEmptyError ||
            isPaymentError ||
            !checked
          }
          loading={isLoading}
          onClick={handleCreateBooking}
          type="submit"
        >
          予約リクエストを送信する
        </Button>
      </Flex>
    </Container>
  );
};

BookingPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default BookingPage;
