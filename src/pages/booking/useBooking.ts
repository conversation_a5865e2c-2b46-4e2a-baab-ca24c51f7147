import { useLocalStorage, usePrevious } from '@mantine/hooks';
import { useFetchData } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import { isEmpty, keyBy } from 'lodash';
import type { AddressHistoryItem } from 'models/address';
import type { ITotalPoints } from 'models/booking';
import { bookingQuery } from 'models/booking';
import type { IMenuItem, IReviewList, ITherapistItem } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  LOCAL_STORAGE_KEY,
  MIDNIGHT_FEE,
  MIDNIGHT_TIMES,
} from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers, { exchangePointToPrice } from 'utils/helpers';

// eslint-disable-next-line no-promise-executor-return
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
const useBooking = () => {
  const router = useRouter();
  const [booking, setBooking] = useLocalStorage<ICreateBooking>({
    key: LOCAL_STORAGE_KEY.BOOKING,
  });
  const [addressHistories, setAddressHistories] = useLocalStorage<
    AddressHistoryItem[]
  >({
    key: LOCAL_STORAGE_KEY.ADDRESS_HISTORIES,
    defaultValue: [],
  });

  const [start, setStart] = useState(false);

  const handleChange = useCallback(
    (values: Partial<ICreateBooking>) => {
      setBooking({
        ...booking,
        ...values,
      });
    },
    [booking, setBooking],
  );

  useEffect(() => {
    const delayStart = async () => {
      await delay(1000);
      setStart(true);
    };
    delayStart();
  }, []);

  useEffect(() => {
    const validDateBooking = helpers.getValidDate();
    if (
      booking?.dateBooking &&
      dayjs(validDateBooking).isAfter(booking.dateBooking)
    ) {
      handleChange({
        dateBooking: validDateBooking,
      });
    }
  }, [booking?.dateBooking, handleChange]);

  const { data: therapistDetail } = useFetchData<ITherapistItem>({
    ...therapistQuery.getTherapistDetail({ id: booking?.therapistId }),
    staleTime: 1000 * 60 * 2,
    // delay 1 second to initial client fetching
    enabled: router.isReady && start,
    onError: () => {
      if (router.isReady) {
        router.push('/');
      }
    },
  });
  const { data: therapistReviews } = useFetchData<IReviewList>({
    ...therapistQuery.getTherapistReviews({
      id: booking?.therapistId,
      therapistDetail: true,
    }),
    staleTime: 1000 * 60 * 2,
    customParams: {
      page: 1,
      limit: 3,
    },
    enabled: !!booking?.therapistId,
  });

  const previousDateBooking = usePrevious(booking?.dateBooking);
  const isMidnight = booking?.dateBooking
    ? MIDNIGHT_TIMES.includes(dayjs(booking?.dateBooking).format('HH:mm'))
    : false;

  // Calculate price, duration based on menus
  const { sumPrice, sumPriceWithCoupon, sumDuration } = useMemo(() => {
    let price = 0;
    let duration = 0;
    booking?.menus?.forEach((menu) => {
      price += menu.selectedOption?.price || 0;
      duration += menu.selectedOption?.duration || 0;
    });
    if (isMidnight) price += MIDNIGHT_FEE;
    return {
      sumPrice: price,
      sumPriceWithCoupon:
        price - (booking?.coupon?.amount || 0) < 0
          ? 0
          : price - (booking?.coupon?.amount || 0),
      sumDuration: duration,
    };
  }, [booking?.menus, booking?.coupon?.amount, isMidnight]);

  const { data: checkAvailableTimeData } = useFetchData<{ status: boolean }>({
    ...bookingQuery.checkAvailableTimeslot({
      therapistId: booking?.therapistId,
      dateBooking: booking?.dateBooking,
      duration: sumDuration,
      areaCodes: booking?.areaCodes,
    }),
    enabled:
      !!booking?.therapistId &&
      !!booking?.dateBooking &&
      !isEmpty(booking?.areaCodes) &&
      booking.dateBooking !== previousDateBooking,
  });
  const { data: checkAvailableAreaData } = useFetchData<{ status: boolean }>({
    ...bookingQuery.checkAvailableArea({
      therapistId: booking?.therapistId,
      areaCodes: booking?.areaCodes,
    }),
    enabled: !!booking?.therapistId && !isEmpty(booking?.areaCodes),
  });

  const { data: therapistMenus, isLoading: isLoadingMenus } = useFetchData<
    IMenuItem[],
    Record<string, IMenuItem>
  >({
    ...therapistQuery.getTherapistMenus({ id: booking?.therapistId }),
    staleTime: 1000 * 60 * 2,
    select: (menus) => {
      return keyBy(menus, '_id');
    },
    enabled: !!booking?.therapistId,
  });
  const initMenuRef = useRef(false);
  useEffect(() => {
    // Update selected menu incase of operator change Booking menu for bookings charged
    if (
      !initMenuRef.current &&
      !isLoadingMenus &&
      therapistMenus &&
      Object.keys(therapistMenus || {}).length &&
      booking?.menus.length
    ) {
      const menus = booking.menus.map((currMenu) => {
        const newestMenu = therapistMenus[currMenu._id];
        if (!newestMenu)
          return {
            ...currMenu,
          };
        const newSelectedOption = newestMenu.options.find(
          (option) => option.duration === currMenu.selectedOption?.duration,
        );
        return {
          ...currMenu,
          ...newestMenu,
          selectedOption: newSelectedOption,
        };
      });
      handleChange({
        menus,
      });
      initMenuRef.current = true;
    }
  }, [booking?.menus, handleChange, isLoadingMenus, therapistMenus]);

  const { data: totalPoints } = useFetchData<ITotalPoints>(
    bookingQuery.getTotalPoints,
  );

  const getBookingData = () => {
    const filterAddress = addressHistories.find(
      (item) => item.address === booking.address,
    );
    // if the address isn't exist on history list add it
    if (!filterAddress && booking.address && booking.areaCodes) {
      setAddressHistories(
        [
          {
            address: booking.address,
            buildingType: booking.buildingType,
            nameplate: booking.nameplate,
            buildingDetails: booking.buildingDetails,
            accessMethod: booking.accessMethod,
            areaCodes: booking.areaCodes,
            isGoogle: booking.isGoogle,
          },
          ...addressHistories,
        ].slice(0, 3),
      );
    }

    const bookingAddress = {
      areaCodes: booking.areaCodes,
      address: booking.address,
      buildingType: booking.buildingType,
      nameplate: booking.nameplate,
      buildingDetails: booking.buildingDetails,
      accessMethod: booking.accessMethod,
      googleAddress: '',
    };

    if (booking.isGoogle) {
      bookingAddress.googleAddress = booking.address;
      bookingAddress.address = '';
    }

    return {
      therapist: {
        _id: therapistDetail?._id,
        phone: therapistDetail?.phone,
        fullName: therapistDetail?.fullName || '-',
        nickName: therapistDetail?.nickName,
        gender: therapistDetail?.gender,
      },
      customerNote: booking.customerNote?.trim() || '',
      parkingNote: booking.parkingNote?.trim() || '',
      dateBooking: booking.dateBooking,
      payment: {
        type: 'gcard3d',
        extra: {
          cardId: booking?.cardId,
        },
      },
      menus: booking?.menus?.map((menu) => ({
        id: menu._id,
        duration: menu.selectedOption?.duration,
      })),
      therapistGender: therapistDetail?.gender,
      coupon: booking?.coupon?.code ? { code: booking.coupon.code } : undefined,
      point: booking?.point,
      ...bookingAddress,
      ...(isMidnight ? { midnightFee: MIDNIGHT_FEE } : {}),
    };
  };

  const pointDiscount = exchangePointToPrice(
    booking?.point || 0,
    totalPoints?.exchangeRate.using.amount,
    totalPoints?.exchangeRate.using.point,
  );
  const sumPriceWithPoint = sumPrice - pointDiscount;
  const promotionPrice = Math.max(
    0,
    sumPriceWithPoint - (booking?.coupon?.amount || 0),
  );
  return {
    booking,
    handleChange,
    therapistDetail,
    therapistReviews,
    isMidnight,
    isSlotError: checkAvailableTimeData?.status === false,
    isPlaceError: checkAvailableAreaData?.status === false,
    isPlaceEmptyError:
      !booking?.address || !booking?.buildingType || !booking?.areaCodes.length,
    isMenuError: sumDuration < (therapistDetail?.minMenu?.duration || 0),
    isPaymentError: !booking?.cardId,
    sumPrice,
    sumPriceWithCoupon,
    sumDuration,
    getBookingData,
    pointDiscount,
    sumPriceWithPoint,
    totalPoints,
    promotionPrice,
    isInitMenu: initMenuRef.current,
  };
};

export default useBooking;
