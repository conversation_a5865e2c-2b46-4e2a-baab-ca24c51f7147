import type { InferType } from 'yup';
import { number, object, string } from 'yup';

export const schema = object({
  rating: number().required().min(1).max(5),
  comment: object({
    therapist: string()
      .trim()
      .nullable()
      .test('len', '口コミは3文字以上で入力してください。', (val) => {
        if (!val) return true;
        return val.length > 2 && val.length <= 200;
      }),
  }),
});
export type ReviewFormValues = InferType<typeof schema>;
