import type { InferType } from 'yup';
import { number, object, string } from 'yup';

export const schema = object({
  rating: number().required().min(1).max(5),
  categories: object({
    technique: string()
      .required('技術の評価を選択してください')
      .oneOf(['GOOD', 'BAD'], 'カテゴリーを選択してください'),
    service: string()
      .required('接客の評価を選択してください')
      .oneOf(['GOOD', 'BAD'], 'カテゴリーを選択してください'),
    cost: string()
      .required('コスパの評価を選択してください')
      .oneOf(['GOOD', 'BAD'], 'カテゴリーを選択してください'),
  }),
  comment: object({
    overall: string()
      .trim()
      .nullable()
      .test('len', '口コミは3文字以上で入力してください。', (val) => {
        if (!val) return true;
        return val.length > 2 && val.length <= 500;
      }),
  }),
});
export type ReviewFormValues = InferType<typeof schema>;
