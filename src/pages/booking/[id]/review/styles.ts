import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  wrapper: {
    '@media (max-width: 768px)': {
      padding: '50px 20px 40px',
      '.title': {
        fontSize: 22,
        marginBottom: 20,
      },
    },
  },

  profileWrapper: {
    padding: '24px 30px',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    background: 'white',
    borderRadius: 6,
    '@media (max-width: 768px)': {
      padding: '15px 10px',
      gap: 10,
      flexWrap: 'wrap',
    },
  },

  contentWrapper: {
    '@media (max-width: 1024px)': {
      gap: 24,
      gridTemplateColumns: 'repeat(auto-fill, minmax(100%, 1fr))',
    },
  },

  profileContent: {
    width: 'calc(100% - 120px)',
    b: {
      width: 48,
      marginRight: 30,
      display: 'inline-flex',
    },
    '@media (max-width: 768px)': {
      width: '100%',
      b: {
        width: 'auto',
        marginRight: 5,
      },
    },
  },

  username: {
    fontSize: 20,
    fontWeight: 'bold',
    '@media (max-width: 768px)': {
      fontSize: 16,
      display: 'none',
    },
  },

  spUsername: {
    fontSize: 16,
    fontWeight: 'bold',
    minWidth: 'calc(100% - 65px)',
    '@media (min-width: 769px)': {
      display: 'none',
    },
  },

  ratingLabel: {
    textAlign: 'center',
  },

  reviewForm: {
    color: '#070203',
    fontSize: 18,
    marginBottom: 62,
    width: '100%',
    maxWidth: 500,
    margin: '0 auto 62px auto',
    '@media (max-width: 768px)': {
      fontSize: 14,
      marginBottom: 25,
      maxWidth: '100%',
    },
  },

  btnGroup: {
    'a, button': {
      width: 300,
    },
  },

  categoriesSection: {
    maxWidth: 600,
    marginBottom: 16,
    marginLeft: -32,
    display: 'flex',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    gap: 16,
    '@media (max-width: 768px)': {
      marginBottom: 24,
      marginLeft: -24,
      gap: 12,
    },
  },

  overallRatingContainer: {
    position: 'relative',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },

  overallRatingWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: '8px 0',
    width: '100%',
    marginBottom: 24,
    '@media (max-width: 425x)': {
      justifyContent: 'flex-start',
      marginBottom: 24,
    },
  },

  categoryLabel: {
    fontSize: 14,
    color: '#3C3C3C',
    width: 56,
    textAlign: 'center',
    flexShrink: 0,
  },

  feedbackSection: {
    width: 216,
    backgroundColor: '#3C3C3C',
    borderRadius: 8,
    padding: '8px 12px',
    opacity: 1,
    maxHeight: 200,
    overflow: 'hidden',
    transition: 'all 0.3s ease-in-out',
    transform: 'translateY(0)',
    position: 'absolute',
    top: -90,
    right: -8,
    zIndex: 1500,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    pointerEvents: 'none', // This prevents the feedback section from interfering with clicks
    '& *': {
      pointerEvents: 'auto', // But allows text selection within the feedback
    },
    '@media (max-width: 768px)': {
      width: 200,
      padding: '8px 10px',
      top: -116,
      right: -10,
    },
  },

  triangleIcon: {
    position: 'absolute',
    bottom: 14,
    right: 20,
    width: 14,
    height: 8,
    zIndex: 1501,
    pointerEvents: 'none',
    '@media (max-width: 768px)': {
      bottom: 14,
      right: 15,
    },
  },

  feedbackContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    alignItems: 'flex-start',
    textAlign: 'left',
    width: '100%',
  },

  feedbackTitle: {
    fontFamily: '"Noto Sans JP", sans-serif',
    fontWeight: 500,
    fontSize: 14,
    lineHeight: 1.43,
    color: '#FFFFFF',
    margin: 0,
    textAlign: 'left',
    alignSelf: 'stretch',
  },

  feedbackDescription: {
    fontFamily: '"Noto Sans JP", sans-serif',
    fontWeight: 400,
    fontSize: 14,
    lineHeight: 1.55,
    color: '#FFFFFF',
    margin: 0,
    textAlign: 'left',
    alignSelf: 'stretch',
  },

  feedbackNote: {
    fontFamily: '"Noto Sans JP", sans-serif',
    fontWeight: 400,
    fontSize: 12,
    lineHeight: 1.33,
    color: '#DDDDDD',
    margin: 0,
    textAlign: 'left',
    alignSelf: 'stretch',
  },
};

export const styles: Record<string, any> = {
  avatar: {
    root: {
      '@media (max-width: 768px)': {
        width: 55,
        minWidth: 55,
        height: 55,
      },
    },
  },
  listInfo: (theme: MantineTheme) => ({
    root: {
      display: 'flex',
      flexDirection: 'column',
      gap: 10,
      '@media (max-width: 768px)': {
        flexDirection: 'row',
        marginTop: 0,
      },
    },
    item: {
      '&:not(:first-of-type)': {
        marginTop: 0,
      },
      '@media (max-width: 768px)': {
        width: '50%',
      },
    },
    itemWrapper: {
      background: theme.colors.ghostWhite,
      alignItems: 'center',
      height: 40,
      fontSize: 16,
      padding: '0 20px',
      width: '100%',
      borderRadius: 4,
      '@media (max-width: 768px)': {
        height: 38,
        fontSize: 14,
        padding: '0 10px',
      },
    },
    itemIcon: {
      marginRight: 14,
      svg: {
        width: 18,
        height: 'auto',
        fill: theme.colors.blackOlive,
      },
      '@media (max-width: 768px)': {
        marginRight: 10,
        svg: {
          width: 15,
        },
      },
    },
  }),
};
