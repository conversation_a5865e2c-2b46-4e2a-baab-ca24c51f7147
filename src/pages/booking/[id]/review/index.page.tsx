import { yupResolver } from '@hookform/resolvers/yup';
import IconAvatar from '@icons/icon-avatar.svg';
import IconBackPack from '@icons/icon-backpack.svg';
import {
  Avatar,
  Box,
  Button,
  Container,
  Flex,
  List,
  SimpleGrid,
  Text,
  Title,
} from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { dehydrate } from '@tanstack/react-query';
import { EmojiRatingField, RatingField, TextArea } from 'components/Form';
import Layout from 'components/Layout';
import { useFetchData, useMutate } from 'hooks';
import _, { get } from 'lodash';
import type { IBookingDetail } from 'models/booking';
import { bookingQuery } from 'models/booking';
import type { IReview, ReviewPayload } from 'models/review';
import { reviewQuery } from 'models/review';
import type { GetServerSideProps } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { BOOKING_REASONS, BOOKING_STATUSES, GENDER } from 'utils/constants';
import helpers, { eventLog } from 'utils/helpers';
import getQueryClient, { fetchData } from 'utils/queryClient';

import type { ReviewFormValues } from './schema';
import { schema } from './schema';
import { styles, sx } from './styles';

const BookingReview = ({ enabled }: { enabled: boolean }) => {
  const router = useRouter();
  const { id, ref } = router.query;
  const bookingId = typeof id === 'string' ? id : '';
  const referencePage = typeof ref === 'string' ? ref : '';

  // State for feedback section visibility
  const [showFeedback, setShowFeedback] = useState(false);
  const [commentFocused, setCommentFocused] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const {
    data: bookingDetail,
    refetch: refetchDetail,
    remove,
  } = useFetchData<IBookingDetail>({
    ...bookingQuery.getBookingDetail({ bookingId }),
    enabled: enabled && !!bookingId,
  });

  const { mutateAsync: sendReviewFn, isLoading: isSendingReview } = useMutate<
    ReviewPayload,
    IReview
  >(reviewQuery.sendReview);

  const getDefaultRating = useCallback(() => {
    let rating =
      typeof router.query.rating === 'string'
        ? Math.round(Number(router.query.rating))
        : 0;
    if (rating > 5) rating = 5;
    if (rating < 0) rating = 0;
    return rating;
  }, [router.query.rating]);

  const { control, handleSubmit, reset, watch, formState } =
    useForm<ReviewFormValues>({
      defaultValues: {
        rating: getDefaultRating(),
        categories: {
          technique: '' as any,
          service: '' as any,
          cost: '' as any,
        },
        comment: {
          overall: '',
        },
      },
      mode: 'onBlur',
      resolver: yupResolver(schema),
      // This ensures the form is reinitialized when the router query changes
      shouldUnregister: true,
    });
  const formValues = watch();

  // Handle route changes
  useEffect(() => {
    router.events.on('routeChangeStart', remove);
    return () => {
      reset();
      router.events.off('routeChangeStart', remove);
    };
  }, [remove, reset, router.events]);

  // Reset form when router query changes
  useEffect(() => {
    if (router.isReady) {
      reset({
        rating: getDefaultRating(),
        categories: {
          technique: '' as any,
          service: '' as any,
          cost: '' as any,
        },
        comment: {
          overall: '',
        },
      });
    }
  }, [router.isReady, router.query, reset, getDefaultRating]);

  // Watch rating value and comment field to control feedback visibility
  useEffect(() => {
    const currentRating = formValues.rating || 0;
    const hasComment = formValues.comment?.overall?.trim() || '';

    // Show feedback when rating is 3 or below, and hide when comment is focused or has content
    const shouldShowFeedback =
      currentRating <= 3 && currentRating > 0 && !commentFocused && !hasComment;
    setShowFeedback(shouldShowFeedback);
  }, [
    formValues.rating,
    formValues.comment?.overall,
    commentFocused,
    submitAttempted,
  ]);

  const onSubmit = (values: ReviewFormValues) => {
    // Track submit attempt for feedback section logic
    setSubmitAttempted(true);

    sendReviewFn(
      {
        ...values,
        bookingId,
        user: {
          id: bookingDetail?.therapist._id,
          role: 'therapist',
        },
      } as ReviewPayload,
      {
        onSuccess: () => {
          if (
            [
              'BookingDetail',
              'BookingHistory',
              'PopupReminder',
              'NotificationList',
            ].includes(referencePage)
          ) {
            eventLog('submit_review_booking', {
              reference_screen: referencePage,
              booking_id: bookingId,
            });
          }
          refetchDetail();
          openContextModal({
            modal: 'AlertModal',
            size: 630,
            innerProps: {
              title: 'レビュー登録ありがとう\nございます！',
              content:
                '頂戴した評価やご意見は今後の\nサービスの参考とさせていただきます。',
              onConfirm: () => {
                router.push(`/booking/${bookingId}`);
              },
              hasOkBtn: true,
            },
            withCloseButton: true,
            centered: true,
            onClose: () => {
              router.push(`/booking/${bookingId}`);
            },
          });
        },
        onError: (error) => {
          if ((error as any).code === 410) {
            router.replace(`/booking/${bookingId}`);
          }
        },
      },
    );
  };

  return (
    <Container p="80px 20px" size={1180} sx={sx.wrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: '口コミ入力 | HOGUGU（ホググ）',
        }}
        title="口コミ入力 | HOGUGU（ホググ）"
      />
      <Title className="title" color="blackOlive" mb={40} order={2} size={30}>
        口コミ入力
      </Title>
      <Box component="form" onSubmit={handleSubmit(onSubmit)}>
        <SimpleGrid cols={2} spacing={20} sx={sx.contentWrapper}>
          <Box>
            <Flex
              align="center"
              direction="row"
              gap={30}
              sx={sx.profileWrapper}
            >
              <Avatar
                alt={bookingDetail?.therapist.nickName}
                radius="50%"
                size={120}
                src={
                  bookingDetail?.therapist.avatar ||
                  '/icons/icon-avatar-default-therapist.svg'
                }
                styles={styles.avatar}
              />
              <Text lineClamp={2} sx={sx.spUsername}>
                {bookingDetail?.therapist.nickName}
              </Text>
              <Box sx={sx.profileContent}>
                <Text lineClamp={2} sx={sx.username}>
                  {bookingDetail?.therapist.nickName}
                </Text>
                <List
                  center
                  mt={12}
                  size="100%"
                  spacing="xs"
                  styles={styles.listInfo}
                >
                  <List.Item icon={<IconAvatar />}>
                    <b>性別</b>
                    {GENDER[bookingDetail?.therapist.gender || 0]}
                  </List.Item>
                  <List.Item icon={<IconBackPack />}>
                    <b>施術歴</b>
                    {bookingDetail?.therapist.experience.name}
                  </List.Item>
                </List>
              </Box>
            </Flex>
          </Box>
          <Flex align="center" direction="column" sx={sx.reviewForm}>
            <Text align="center" sx={sx.ratingLabel}>
              セラピストはいかがでしたか？
              <br />
              口コミ投稿で獲得ポイント2倍に！
            </Text>

            {/* Review Categories Section */}
            <Box sx={sx.categoriesSection}>
              <EmojiRatingField
                control={control}
                label="技術"
                name="categories.technique"
              />
              <EmojiRatingField
                control={control}
                label="接客"
                name="categories.service"
              />
              <EmojiRatingField
                control={control}
                label="コスパ"
                name="categories.cost"
              />

              {/* Overall Rating Section with Feedback Overlay */}
              <Box sx={sx.overallRatingContainer}>
                <Flex align="center" gap={20} sx={sx.overallRatingWrapper}>
                  <Text sx={sx.categoryLabel}>総合評価</Text>
                  <RatingField
                    color="marigold"
                    control={control}
                    name="rating"
                    size="xs"
                    styles={styles.rating}
                  />
                </Flex>

                {/* Conditional Feedback Section positioned as overlay at top-right */}
                {showFeedback && (
                  <>
                    <Box sx={sx.feedbackSection}>
                      <Box sx={sx.feedbackContent}>
                        <Text sx={sx.feedbackTitle}>
                          口コミをお聞かせください
                        </Text>
                        <Text sx={sx.feedbackDescription}>
                          セラピストの施術について、さらにご満足いただくためには、どのような点の改善が必要だとお感じでしょうか？
                        </Text>
                        <Text sx={sx.feedbackNote}>
                          ※口コミは3文字以上でご入力ください。
                        </Text>
                      </Box>
                    </Box>
                    {/* Triangle icon positioned at bottom-right corner */}
                    <Box sx={sx.triangleIcon}>
                      <img
                        alt="Triangle pointer"
                        height={8}
                        src="/icons/triangle-bottom.svg"
                        width={14}
                      />
                    </Box>
                  </>
                )}
              </Box>
            </Box>

            <TextArea
              control={control}
              description={`${_.chain(formValues)
                .get('comment.overall', '')
                .size()
                .value()}/500`}
              maxLength={500}
              maxRows={4}
              minRows={2}
              name="comment.overall"
              onBlur={() => setCommentFocused(false)}
              onFocus={() => setCommentFocused(true)}
              placeholder="セラピストに対しての口コミをご記入ください"
            />
          </Flex>
        </SimpleGrid>
        <Flex gap={20} justify="space-between" sx={sx.btnGroup}>
          <Button
            className="back-btn"
            color="grey"
            component={Link}
            href={`/booking/${bookingId}`}
            size="lg"
            variant="outline"
          >
            詳細に戻る
          </Button>
          <Button
            color="marigold"
            disabled={!formState.isValid}
            loading={isSendingReview}
            size="lg"
            type="submit"
          >
            完了
          </Button>
        </Flex>
      </Box>
    </Container>
  );
};

BookingReview.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  try {
    const { id } = query;
    if (typeof id !== 'string') return { notFound: true };
    const queryClient = getQueryClient();
    const booking = await fetchData({
      queryClient,
      ...bookingQuery.getBookingDetail({ bookingId: id }),
      axiosConfig: helpers.getTokenConfig(req, res),
    });
    if (
      !!booking?.review ||
      booking?.currentStatus.status !== BOOKING_STATUSES.DONE ||
      booking?.currentStatus.reason ===
        BOOKING_REASONS.FINISH_WITHOUT_TREATMENT ||
      booking?.reviewExpired
    ) {
      return {
        redirect: {
          destination: `/booking/${booking._id}`,
          permanent: false,
        },
      };
    }
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: true, // Enable the query by default to ensure data is available
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return { notFound: true };
  }
};

export default BookingReview;
