import IconBlock from '@icons/icon-block.svg';
import IconHeadphone from '@icons/icon-headphone.svg';
import SuccessIcon from '@icons/icon-success.svg';
import {
  Anchor,
  Box,
  Button,
  Container,
  Divider,
  Flex,
  Text,
  Title,
} from '@mantine/core';
import { useLocalStorage, useMediaQuery } from '@mantine/hooks';
import { closeModal, openContextModal } from '@mantine/modals';
import { dehydrate } from '@tanstack/react-query';
import { BookingProgress, TherapistInfo } from 'components/Bookings';
import BookingStatus from 'components/Bookings/BookingStatus';
import MemoNoteDetail from 'components/Bookings/MemoNote/Detail';
import MenuCouponDetail from 'components/Bookings/MenuCoupon/Detail';
import PaymentMethodDetail from 'components/Bookings/PaymentMethod/Detail';
import TreatmentPlaceDetail from 'components/Bookings/TreatmentPlace/Detail';
import Layout from 'components/Layout';
import { BookingChatModal, PaymentMethodModal } from 'components/Modals';
import { useFetchData, useGlobalState, useMutate, useUser } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import { get } from 'lodash';
import type { AddressHistoryItem } from 'models/address';
import type { IBookingDetail } from 'models/booking';
import { bookingQuery } from 'models/booking';
import type { CardListResponse } from 'models/payment';
import { paymentQuery } from 'models/payment';
import type { GetServerSideProps } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import type { MouseEventHandler } from 'react';
import React, { useCallback, useEffect, useState } from 'react';
import {
  BOOKING_REASONS,
  BOOKING_STATUSES,
  LOCAL_STORAGE_KEY,
} from 'utils/constants';
import helpers, { eventLog } from 'utils/helpers';
import notification from 'utils/notification';
import getQueryClient, { fetchData } from 'utils/queryClient';

import Error from '../Error';
import { sx } from './styles';

const BookingDetail = ({ enabled }: { enabled: boolean }) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const [openPayment, setOpenPayment] = useState(false);
  const [booking, setBooking, removeBooking] = useLocalStorage<ICreateBooking>({
    key: LOCAL_STORAGE_KEY.BOOKING,
  });
  const router = useRouter();
  const { id, chat, error } = router.query;
  const bookingId = typeof id === 'string' ? id : '';
  const defaultOpenChat = typeof chat === 'string' && chat === 'true';
  const [openChat, setOpenChat] = useState(false);
  const { device, setCall, call } = useGlobalState();
  const { data: currentUser } = useUser();
  const browserType = helpers.detectBrowser();

  useEffect(() => {
    if (defaultOpenChat) {
      setOpenChat(true);
      eventLog('start_chat', {
        booking_id: bookingId,
        reference_screen: 'PushNotification',
      });
      router.replace(
        {
          pathname: '/booking/[id]',
          query: {
            id: bookingId,
          },
        },
        undefined,
        { shallow: true },
      );
    }
  }, [defaultOpenChat, bookingId, router]);

  const {
    data: bookingDetail,
    refetch: refetchDetail,
    isLoading: isLoadingBooking,
  } = useFetchData<IBookingDetail>({
    ...bookingQuery.getBookingDetail({ bookingId }),
    enabled: enabled && !!bookingId,
  });

  const { data } = useFetchData<CardListResponse>({
    ...paymentQuery.getCardList,
    staleTime: 1000 * 60 * 2,
    enabled: enabled && !!bookingId,
  });

  const { mutateAsync: getCancelBookingFee, isLoading: isFetchingCancelFee } =
    useMutate<{ bookingId: string }, { fee: number }>(
      bookingQuery.getCancelBookingFee,
    );

  const { mutateAsync: chargePayment, isLoading: isChargingPayment } =
    useMutate<Record<string, unknown>, { RedirectUrl: string }>(
      bookingQuery.chargePayment,
    );
  const { mutateAsync: rechargePayment, isLoading: isRechargingPayment } =
    useMutate<Record<string, unknown>, { RedirectUrl: string }>(
      bookingQuery.rechargePayment,
    );

  const isPaymentError =
    !!bookingDetail?.payment.transaction.error &&
    ![BOOKING_STATUSES.PENDING, BOOKING_STATUSES.NEW].includes(
      bookingDetail?.currentStatus.status || '',
    ) &&
    bookingDetail?.currentStatus.requestBy !== 'system';
  const isCancelBookingGotError =
    !!bookingDetail?.payment?.transaction?.error &&
    bookingDetail?.currentStatus.status === BOOKING_STATUSES.CANCELED;
  const isHaveCancelBookingFee =
    bookingDetail?.currentStatus?.status === BOOKING_STATUSES.CANCELED &&
    bookingDetail?.payment?.transaction?.status === 2;
  const isRefundPointBooking =
    (bookingDetail?.point?.used?.point || 0) > 0 &&
    bookingDetail?.currentStatus.status === BOOKING_STATUSES.CANCELED &&
    !bookingDetail?.cancellingNote?.isCharged;

  const handleStartBooking = () => {
    if (booking) removeBooking();
    if (bookingDetail) {
      const areaCodes = [
        bookingDetail.prefecture?.areaCode,
        bookingDetail.city?.areaCode,
        bookingDetail.ward?.areaCode,
        bookingDetail.district?.areaCode,
      ].filter((i) => !!i) as string[];

      setBooking({
        therapistId: bookingDetail.therapist._id,
        menus: bookingDetail?.menus,
        areaCodes,
        dateBooking: helpers.getValidDate(),
        cardId: bookingDetail?.payment.extra?.cardId || '',
        address: bookingDetail?.googleAddress || bookingDetail?.address || '',
        buildingType: bookingDetail?.buildingType || '',
        nameplate: bookingDetail?.nameplate || '',
        buildingDetails: bookingDetail?.buildingDetails || '',
        accessMethod: bookingDetail?.accessMethod || '',
        isGoogle: !!bookingDetail?.googleAddress,
        customerNote: bookingDetail?.customerNote,
        parkingNote: bookingDetail?.parkingNote,
      });
      eventLog('rebook', {
        booking_id: bookingDetail._id,
        reference_page: 'booking_detail',
      });
      router.push('/booking');
    }
  };

  const openReviewModal = useCallback(() => {
    if (
      !!bookingDetail?.review ||
      bookingDetail?.currentStatus.status !== BOOKING_STATUSES.DONE ||
      bookingDetail?.currentStatus.reason ===
        BOOKING_REASONS.FINISH_WITHOUT_TREATMENT
    )
      return;
    openContextModal({
      modalId: `BookingReviewModal-${bookingId}`,
      modal: 'BookingReviewModal',
      size: 630,
      centered: true,
      innerProps: {
        bookingId,
        therapist: bookingDetail?.therapist,
      },
    });
  }, [
    bookingDetail?.currentStatus.reason,
    bookingDetail?.currentStatus.status,
    bookingDetail?.review,
    bookingDetail?.therapist,
    bookingId,
  ]);

  useEffect(() => {
    if (
      !bookingDetail ||
      bookingDetail?.isViewed ||
      bookingDetail?.reviewExpired
    )
      return;
    openReviewModal();
  }, [bookingDetail, openReviewModal]);

  useEffect(() => {
    if (error) {
      notification.show({ type: 'error', message: error });
    }
  }, [error]);

  const handleChargePayment = async (paymentId: string) => {
    try {
      const bookingDetailUrl = `${process.env.NEXT_PUBLIC_DOMAIN}/booking/${bookingId}`;
      const redirectUrl = {
        fail: bookingDetailUrl,
        success: bookingDetailUrl,
      };
      if (isCancelBookingGotError) {
        const rechargePaymentData = await rechargePayment({
          bookingId,
          cardId: paymentId,
          redirectUrl,
        });
        window.location.href = rechargePaymentData.RedirectUrl;
      } else {
        const chargePaymentData = await chargePayment({
          bookingId,
          cardId: paymentId,
          redirectUrl,
        });
        window.location.href = chargePaymentData.RedirectUrl;
      }
      await refetchDetail();
      setOpenPayment(false);
    } catch {
      setOpenPayment(false);
    }
  };

  const handleCancelBooking = async () => {
    const cancelFee = await getCancelBookingFee({ bookingId });
    openContextModal({
      modal: 'CancelBookingModal',
      size: 630,
      innerProps: {
        fee: cancelFee.fee,
        expiryCancelingBooking: bookingDetail?.expiryCancelingBooking || 15,
        bookingStatus: bookingDetail?.currentStatus.status,
        bookingCategory: bookingDetail?.currentStatus.category,
        bookingId,
      },
      modalId: 'cancel-booking',
      closeButtonProps: {
        onClick: () => {
          eventLog('decline_cancel');
          closeModal('cancel-booking');
        },
      },
    });
  };

  const handleCallTherapist = async () => {
    const isEnabledAudio = await helpers.isEnabledAudio();
    if (isEnabledAudio) {
      const outGoingCall = await device?.connect({
        params: {
          to: bookingDetail?.therapist._id || '',
          callerName: currentUser?.name || '',
          callerAvatar: currentUser?.profilePicture?.url || '',
          receiverName: bookingDetail?.therapist.nickName || '',
          receiverAvatar: bookingDetail?.therapist.avatar || '',
        },
      });
      setCall(outGoingCall);
    } else {
      const supportLink = helpers.getSupportLink(browserType);
      openContextModal({
        modal: 'AlertModal',
        size: 630,
        innerProps: {
          content: supportLink ? (
            <Text sx={{ wordBreak: 'break-word' }}>
              通話を開始するには、マイクへのアクセスを許可する必要があります。マイクの設定を変更する方法は
              <Anchor href={supportLink} target="blank">
                こちら
              </Anchor>
              をご覧ください
            </Text>
          ) : (
            '通話を開始するには、マイクへのアクセスを許可する必要があります。'
          ),
        },
        centered: true,
      });
    }
  };

  const confirmCallTherapist: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
    eventLog('start_call');
    openContextModal({
      modal: 'AlertModal',
      size: 630,
      innerProps: {
        hasOkBtn: true,
        onConfirm: handleCallTherapist,
        title: 'セラピストに電話が繋がります',
        content: `この通話はインターネット回線を利用する為、
        通話料はかかりませんが、
        モバイルデータ通信料が発生します。`,
      },
      centered: true,
    });
  };

  return (
    <Container p="80px 20px" size={1180} sx={sx.bookingDetailWrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: '予約詳細 | HOGUGU（ホググ）',
        }}
        title="予約詳細 | HOGUGU（ホググ）"
      />
      <Flex align="end" justify="space-between" mb={40} sx={sx.headerWrapper}>
        <Title className="title" order={1} size={30}>
          予約履歴詳細
        </Title>
        <Text className="booking-id" size={18} weight="bold">
          {bookingDetail?._id ? `#${bookingDetail._id}` : ''}
        </Text>
      </Flex>
      <Box
        sx={{
          width: '100vw',
          marginLeft: 'calc(50% - 50vw)',
          '@media (max-width: 768px)': {
            marginLeft: -20,
          },
        }}
      >
        <BookingStatus
          cancellingNote={bookingDetail?.cancellingNote?.note}
          isPaymentError={isPaymentError}
          onOpenPayment={() => setOpenPayment(true)}
          onStartBooking={handleStartBooking}
          reason={bookingDetail?.currentStatus.reason}
          status={bookingDetail?.currentStatus.status}
          therapistIsBusy={bookingDetail?.therapistCurrentTreatment?.isBusy}
          therapistName={
            bookingDetail?.therapist.nickName ||
            bookingDetail?.therapist.fullName
          }
        />
      </Box>
      <Flex gap={20} sx={sx.sectionRow}>
        <Flex direction="column" gap={32}>
          <Box sx={sx.sectionWrapper}>
            <Title className="title" order={3}>
              セラピスト
            </Title>
            <TherapistInfo
              bookingId={bookingDetail?._id}
              bookingReason={bookingDetail?.currentStatus.reason}
              bookingStatus={bookingDetail?.currentStatus.status}
              bookingStatusHistory={bookingDetail?.statusHistory}
              detail={bookingDetail?.therapist}
              isOnCall={!!call}
              loading={isLoadingBooking}
              onCall={confirmCallTherapist}
              onOpenChat={() => {
                eventLog('start_chat', {
                  booking_id: bookingId,
                  reference_screen: 'BookingDetail',
                });
                setOpenChat(true);
              }}
              review={bookingDetail?.review}
              reviewExpired={bookingDetail?.reviewExpired}
              summaryReview={bookingDetail?.therapist.summaryReview}
            />
          </Box>
          {mobileScreen && (
            <Box sx={sx.sectionWrapper}>
              <Title className="title" order={3}>
                進行状況
              </Title>
              <BookingProgress
                loading={isLoadingBooking}
                reason={bookingDetail?.currentStatus.reason}
                status={bookingDetail?.currentStatus.status}
                statusHistory={bookingDetail?.statusHistory}
                timestamp={bookingDetail?.currentStatus.timestamp}
              />
            </Box>
          )}
          <Box sx={sx.sectionWrapper}>
            <Title className="title" order={3}>
              日時・場所
            </Title>
            <TreatmentPlaceDetail
              addressItem={bookingDetail as AddressHistoryItem | undefined}
              dateBooking={bookingDetail?.dateBooking}
              loading={isLoadingBooking}
            />
          </Box>
          {mobileScreen && (
            <Box sx={sx.sectionWrapper}>
              <Title className="title" order={3}>
                メニュー
              </Title>
              <MenuCouponDetail
                coupon={bookingDetail?.coupon}
                currentStatus={bookingDetail?.currentStatus}
                extensions={bookingDetail?.extensions}
                isPaymentError={isPaymentError}
                loading={isLoadingBooking}
                menus={bookingDetail?.menus}
                midnightFee={bookingDetail?.midnightFee}
                point={bookingDetail?.point}
                sumDuration={bookingDetail?.duration}
                sumPrice={bookingDetail?.payment.amount}
              />
              {isRefundPointBooking && (
                <Flex align="center" gap={8} mt={16}>
                  <SuccessIcon height={16} width={16} />
                  <Text color="avocado" fw={500} fz={{ base: 12, sm: 18 }}>
                    利用したポイントは返却します
                  </Text>
                </Flex>
              )}
            </Box>
          )}
          <Box sx={sx.sectionWrapper}>
            <Title className="title" order={3}>
              お支払い方法
            </Title>
            <PaymentMethodDetail
              brand={bookingDetail?.payment.transaction.extra?.source?.brand}
              isCancelBookingGotError={isCancelBookingGotError}
              isHaveCancelBookingFee={isHaveCancelBookingFee}
              isPaymentError={isPaymentError}
              last4={bookingDetail?.payment.transaction.extra?.source?.last4}
              loading={isLoadingBooking}
              status={bookingDetail?.currentStatus.status}
              sumPrice={bookingDetail?.payment.amount}
              type={bookingDetail?.payment.type}
            />
            <PaymentMethodModal
              activeCard={booking?.cardId || ''}
              defaultCard={data?.defaultCard || ''}
              handleChangePayment={handleChargePayment}
              isChangingPayment={isRechargingPayment || isChargingPayment}
              onClose={() => setOpenPayment(false)}
              opened={openPayment}
              selectPaymentText={`お支払い (¥${helpers.numberFormat(
                bookingDetail?.payment.amount,
              )})`}
            />
            {isPaymentError && (
              <Error
                content={
                  <span>
                    未決済額¥
                    {helpers.numberFormat(bookingDetail?.payment.amount || 0)}
                    のお支払いが必要です。
                  </span>
                }
              />
            )}
          </Box>
          <Box sx={sx.sectionWrapper}>
            <Title className="title" order={3}>
              追記事項（任意）
            </Title>
            <MemoNoteDetail
              customerNote={bookingDetail?.customerNote}
              loading={isLoadingBooking}
              parkingNote={bookingDetail?.parkingNote}
            />
          </Box>
        </Flex>
        {!mobileScreen && (
          <Flex direction="column" gap={32}>
            <Box sx={sx.sectionWrapper}>
              <Title className="title" order={3}>
                進行状況
              </Title>
              <BookingProgress
                loading={isLoadingBooking}
                reason={bookingDetail?.currentStatus.reason}
                status={bookingDetail?.currentStatus.status}
                statusHistory={bookingDetail?.statusHistory}
                timestamp={bookingDetail?.currentStatus.timestamp}
              />
            </Box>
            <Box sx={sx.sectionWrapper}>
              <Title className="title" order={3}>
                メニュー
              </Title>
              <MenuCouponDetail
                coupon={bookingDetail?.coupon}
                currentStatus={bookingDetail?.currentStatus}
                extensions={bookingDetail?.extensions}
                isPaymentError={isPaymentError}
                loading={isLoadingBooking}
                menus={bookingDetail?.menus}
                midnightFee={bookingDetail?.midnightFee}
                point={bookingDetail?.point}
                sumDuration={bookingDetail?.duration}
                sumPrice={bookingDetail?.payment.amount}
              />
              {isRefundPointBooking && (
                <Flex align="center" gap={16} mt={8}>
                  <SuccessIcon />
                  <Text color="avocado" fw={500} fz={{ base: 12, sm: 18 }}>
                    利用したポイントは返却します
                  </Text>
                </Flex>
              )}
            </Box>
          </Flex>
        )}
      </Flex>
      <Flex direction="column" gap={20} sx={sx.actionBtnGroup}>
        <Button
          color="blackOlive"
          leftIcon={<IconHeadphone />}
          sx={sx.contactBtn}
        >
          緊急のご連絡（運営に電話する）： 0120-43-8897
          <br />
          ※営業時間：10:00〜19:00（年中無休）
        </Button>
        {![
          BOOKING_STATUSES.CANCELED,
          BOOKING_STATUSES.ARRIVED,
          BOOKING_STATUSES.DONE,
        ].includes(bookingDetail?.currentStatus.status || '') && (
          <Button
            bg="white"
            className="cancel-btn"
            color="blackOlive"
            leftIcon={<IconBlock />}
            loading={isFetchingCancelFee}
            onClick={handleCancelBooking}
            variant="outline"
          >
            この予約をキャンセル
          </Button>
        )}
      </Flex>
      <Divider sx={sx.btnDivider} variant="dashed" />
      <Flex gap={20} justify="space-between" sx={sx.footerBtnGroup}>
        <Button
          className="back-btn"
          color="grey"
          component={Link}
          href="/my-page/booking-history"
          size="lg"
          variant="outline"
        >
          一覧に戻る
        </Button>
        <Button component={Link} href="/my-page" size="lg">
          マイページへ
        </Button>
      </Flex>
      <BookingChatModal
        bookingId={bookingId}
        onClose={() => setOpenChat(false)}
        opened={openChat}
      />
    </Container>
  );
};

BookingDetail.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  try {
    const { id } = query;
    if (typeof id !== 'string') return { notFound: true };
    if (req.url?.startsWith('/_next')) {
      return {
        props: {
          enabled: true,
        },
      };
    }
    const queryClient = getQueryClient();
    await fetchData({
      queryClient,
      ...bookingQuery.getBookingDetail({ bookingId: id }),
      axiosConfig: helpers.getTokenConfig(req, res),
    });
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: false,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return { notFound: true };
  }
};

export default BookingDetail;
