import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  bookingDetailWrapper: {
    overflowX: 'visible',
    '@media (max-width: 768px)': {
      padding: '50px 20px 40px',
    },
  },
  headerWrapper: {
    flexWrap: 'wrap',
    gap: 20,
    '@media (max-width: 768px)': {
      marginBottom: 30,
      '.title': {
        fontSize: 22,
      },
      '.booking-id': {
        fontSize: 15,
      },
    },
  },
  sectionRow: {
    marginBottom: 65,
    marginTop: 40,
    flexWrap: 'wrap',
    '@media (max-width: 768px)': {
      marginBottom: 30,
      marginTop: 30,
    },
    '& > *': {
      flex: '1 1 35%',
      '@media (max-width: 768px)': {
        gap: 25,
      },
    },
  },
  sectionWrapper: (theme: MantineTheme) => ({
    '.title': {
      fontSize: 24,
      color: 'white',
      backgroundColor: theme.colors.queenBlue[6],
      height: 60,
      display: 'flex',
      alignItems: 'center',
      paddingLeft: 30,
      marginBottom: 20,
      '@media (max-width: 768px)': {
        height: 40,
        fontSize: 18,
        paddingLeft: 10,
        marginBottom: 15,
      },
    },
    span: {
      '@media (max-width: 768px)': {
        whiteSpace: 'normal',
      },
    },
    '.error': {
      marginTop: 20,
      display: 'flex',
      color: '#db1e0e',
      fontWeight: 'bold',
      gap: 12,
      '@media (max-width: 768px)': {
        padding: '0 10px',
        gap: 6,
        fontSize: 12,
        marginTop: 12,
      },
      svg: {
        flexShrink: 0,
        width: 24,
        height: 24,
        '@media (max-width: 768px)': {
          width: 16,
          height: 16,
        },
      },
    },
  }),

  contactBtn: {
    pointerEvents: 'none',
    '.mantine-Button-label': {
      flexFlow: 'row wrap',
      justifyContent: 'center',
      textAlign: 'center',
      lineHeight: 1.5,
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
  },

  actionBtnGroup: {
    alignItems: 'center',
    'a, button': {
      height: 60,
      maxWidth: 600,
      width: '100%',
      fontSize: 18,
      '@media (max-width: 768px)': {
        height: 40,
        fontSize: 13,
      },
      svg: {
        width: 24,
        height: 24,
        '@media (max-width: 768px)': {
          width: 16,
          height: 16,
        },
      },
      '.mantine-Button-leftIcon': {
        marginRight: 16,
        '@media (max-width: 768px)': {
          marginRight: 10,
        },
      },
    },
  },
  btnDivider: {
    margin: '30px 0',
    borderWidth: 2,
    borderColor: 'transparent',
    '@media (max-width: 768px)': {
      borderColor: '#a3a3a3',
    },
  },
  footerBtnGroup: {
    'a, button': {
      maxWidth: 300,
      width: '100%',
      '@media (max-width: 768px)': {
        height: 50,
      },
    },
  },
};
