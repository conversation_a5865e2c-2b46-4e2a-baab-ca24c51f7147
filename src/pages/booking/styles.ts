import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  therapistBusyBadge: {
    backgroundColor: theme.colors.floralWhite[0],
    color: theme.colors.marigold[0],
    fontWeight: 500,
    fontSize: 16,
    padding: '16px 24px',
    borderRadius: '4px',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    boxShadow: '0px 1px 1px 0px rgba(0, 0, 0, 0.16)',
    marginBottom: 24,
    span: {
      marginLeft: 16,
    },
    svg: {
      flexShrink: 0,
    },
    [theme.fn.smallerThan('sm')]: {
      margin: '-24px -20px 24px',
      width: 'calc(100% + 40px)',
      borderRadius: 0,
      alignItems: 'flex-start',
      padding: '16px 20px',
      fontSize: 14,
      lineHeight: '20px',
      span: {
        marginLeft: 8,
      },
      svg: {
        width: 24,
        height: 24,
      },
    },
  },
  sectionRow: {
    marginBottom: 32,
    '& > *': {
      flex: '0 1 50%',
      [theme.fn.smallerThan('sm')]: {
        flex: '1 1 100%',
      },
    },
  },
  sectionWrapper: {
    '.title': {
      fontSize: 24,
      color: 'white',
      backgroundColor: theme.colors.queenBlue[6],
      height: 60,
      display: 'flex',
      alignItems: 'center',
      paddingLeft: 30,
      marginBottom: 20,
      [theme.fn.smallerThan('sm')]: {
        height: 40,
        fontSize: 18,
        paddingLeft: 10,
        marginBottom: 15,
      },
    },
    span: {
      '@media (max-width: 768px)': {
        whiteSpace: 'normal',
      },
    },
    '.error': {
      marginTop: 20,
      display: 'flex',
      color: '#db1e0e',
      fontWeight: 'bold',
      gap: 12,
      [theme.fn.smallerThan('sm')]: {
        padding: '0 10px',
        gap: 6,
        fontSize: 12,
        marginTop: 12,
      },
      svg: {
        flexShrink: 0,
        width: 24,
        height: 24,
        [theme.fn.smallerThan('sm')]: {
          width: 16,
          height: 16,
        },
      },
    },
  },
  termsNote: {
    padding: '0 30px',
    marginBottom: 30,
    a: {
      color: '#327eb9',
      textUnderlineOffset: 5,
      textDecoration: 'underline',
    },
    [theme.fn.smallerThan('sm')]: {
      padding: 0,
      marginBottom: 14,
      fontSize: 14,
    },
  },
  termsCheck: {
    margin: '0 30px',
    [theme.fn.smallerThan('sm')]: {
      margin: 0,
    },
    '.mantine-Checkbox-body': {
      position: 'relative',
    },
    '.mantine-Checkbox-inner': {
      position: 'absolute',
      top: 14,
      left: 20,
      width: 22,
      height: 22,
      [theme.fn.smallerThan('sm')]: {
        width: 18,
        height: 18,
        left: 10,
        top: 10,
      },
    },
    '.mantine-Checkbox-input': {
      width: 22,
      height: 22,
      borderRadius: 0,
      cursor: 'pointer',
      '&:checked': {
        backgroundColor: '#3C3C3C',
        borderColor: '#3C3C3C',
      },
      [theme.fn.smallerThan('sm')]: {
        width: 18,
        height: 18,
      },
    },
    '.mantine-Checkbox-label': {
      fontSize: 16,
      backgroundColor: 'white',
      boxShadow: '0px 1px 2px #00000029',
      padding: '15px 20px 15px 56px',
      cursor: 'pointer',
      borderRadius: '3px',
      a: {
        color: '#43749A',
        textDecoration: 'underline',
      },
      [theme.fn.smallerThan('sm')]: {
        fontSize: 12,
        padding: '10px 10px 10px 38px',
      },
    },
    '.mantine-Checkbox-labelWrapper': {
      flexGrow: 1,
    },
  },
  btnGroup: {
    marginTop: 62,
    [theme.fn.smallerThan('sm')]: {
      marginTop: 30,
      flexDirection: 'column-reverse',
      alignItems: 'center',
      gap: 20,
    },
    'a, button': {
      height: 60,
      maxWidth: 410,
      width: '100%',
      fontSize: 18,
      [theme.fn.smallerThan('sm')]: {
        height: 40,
        fontSize: 13,
      },
    },
  },
}));

export default useStyles;
