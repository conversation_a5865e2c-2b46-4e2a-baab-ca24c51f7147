import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconDropdown from '@icons/icon-dropdown.svg';
import {
  Anchor,
  Box,
  Breadcrumbs,
  Container,
  Group,
  Select,
  SimpleGrid,
  Skeleton,
  Text,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { closeAllModals } from '@mantine/modals';
import { dehydrate } from '@tanstack/react-query';
import Layout from 'components/Layout';
import type { SearchTherapistsFormValues } from 'components/Therapists/SearchForm';
import { useFetchData, useFetchList } from 'hooks';
import get from 'lodash/get';
import map from 'lodash/map';
import times from 'lodash/times';
import uniq from 'lodash/uniq';
import { bookingQuery } from 'models/booking';
import type { IBreadcrumb, IPrefectureItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import type { IMenuItem, ITherapistItem } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import type { GetServerSideProps } from 'next';
import dynamic from 'next/dynamic';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { NextSeo } from 'next-seo';
import pino from 'pino';
import { useMemo } from 'react';
import { SORT_ORDER } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers, { eventLog } from 'utils/helpers';
import getQueryClient, { fetchData, fetchList } from 'utils/queryClient';
import seoConfig, { searchMetaContent } from 'utils/seoConfig';
import { v4 as uuidv4 } from 'uuid';

import { styles, sx } from './styles';

const TherapistsSearchForm = dynamic(
  () => import('components/Therapists').then((r) => r.TherapistsSearchForm),
  { ssr: false },
);
const SearchBox = dynamic(
  () => import('components/Therapists').then((r) => r.SearchBox),
  { ssr: false },
);
const TherapistCard = dynamic(() =>
  import('components/Therapists').then((r) => r.TherapistCard),
);
const Pagination = dynamic(() => import('components/Pagination'));

const SearchTherapists = ({
  enabled,
  isExternalSite = false,
}: {
  enabled: boolean;
  isExternalSite: boolean;
}) => {
  const router = useRouter();
  const {
    slug = ['tokyo'],
    page = '1',
    dateBooking,
    gender = '0',
    sort,
    selectMenu,
    searchingId,
  } = router.query;
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const dateBookingFormat = helpers.getValidDate(
    typeof dateBooking === 'string' ? dateBooking : '',
  );

  const {
    list,
    total,
    lastPage,
    isLoading: isLoadingTherapist,
  } = useFetchList<ITherapistItem>({
    ...therapistQuery.searchTherapists,
    enabled,
    keepPreviousData: false,
    customParams: {
      dateBooking: dateBookingFormat,
      gender: typeof gender === 'string' ? Number(gender) : 0,
      areaNames: helpers.formatSlugPath(slug),
      page: typeof page === 'string' ? Number(page) : 1,
      limit: 20,
      menuNames:
        selectMenu && typeof selectMenu === 'string'
          ? [selectMenu]
          : selectMenu,
      sort: typeof sort === 'string' ? SORT_ORDER[sort] : undefined,
      searchingId: typeof searchingId === 'string' ? searchingId : undefined,
    },
    staleTime: 1000 * 60 * 2,
    omitKeys: ['selectMenu', 'slug'],
  });

  const { data: menus, isLoading: isLoadingMenu } = useFetchData<IMenuItem[]>({
    ...bookingQuery.getMenuList,
    staleTime: Infinity,
  });
  const { data: prefectureList = [], isLoading: isLoadingPrefecture } =
    useFetchData<IPrefectureItem[]>({
      ...resourceQuery.getPrefectureList,
      staleTime: 1000 * 60 * 2,
      enabled,
    });

  const areaNames: unknown[] = useMemo(() => {
    const names: unknown[] = [];
    if (slug[0]) {
      prefectureList.every((prefecture) => {
        if (prefecture.nameEn === slug[0]) {
          names.push(prefecture);
          if (slug[1]) {
            prefecture.children?.forEach((city) => {
              if (city.nameEn === slug[1]) {
                names.push(city);
                return false;
              }
              return true;
            });
          }
          return false;
        }
        return true;
      });
    }
    return names;
  }, [prefectureList, slug]);

  const areaCodes: string[] = useMemo(() => {
    const codes: string[] = [];
    areaNames.forEach((area, index) => {
      if (index === 1) {
        codes.push(get(area, 'parent', ''));
      }
      codes.push(get(area, 'areaCode', ''));
    });
    return uniq(codes);
  }, [areaNames]);

  const defaultValues: SearchTherapistsFormValues = {
    dateBooking: dateBookingFormat,
    areaNames,
    gender: typeof gender === 'string' ? gender : '0',
    selectMenu: (typeof selectMenu === 'string'
      ? [selectMenu]
      : selectMenu) || [''],
  };
  const topTherapists = useMemo(() => {
    return list.filter(
      (therappist) => (therappist.summaryReview?.sumReviewer || 0) >= 10,
    );
  }, [list]);

  const areaNameDisplay = areaNames.reduce(
    (result: string, area: any) => result.concat(area.name),
    '',
  );
  const areaNameSEO = areaNames[areaNames.length - 1] as {
    seo: { title: string; h1: string };
  };

  const searchContent = {
    ...searchMetaContent({
      area: areaNameSEO?.seo?.title,
      menu: typeof selectMenu === 'string' ? selectMenu : '',
      gender: typeof gender === 'string' ? Number(gender) : 0,
      sort: typeof sort === 'string' ? sort : '',
      total: total || 0,
      isCity: areaNames.length === 2,
    }),
    prefecture: get(areaNames[0], 'nameEn', ''),
    city: get(areaNames[1], 'nameEn', ''),
    path: router.asPath,
    noindex:
      process.env.NEXT_PUBLIC_DEPLOY_ENV !== 'production' ||
      Array.isArray(selectMenu),
  };

  const handleSearch = (values: SearchTherapistsFormValues) => {
    const params: Record<string, string | string[]> = {};
    if (values.dateBooking) {
      params.dateBooking = values.dateBooking;
    }
    if (values.gender) {
      params.gender = values.gender;
    }
    if (values.selectMenu) {
      // Need to filter out "" stand for all menus
      params.selectMenu = values.selectMenu.filter((value: string) => value);
    }
    if (sort && typeof sort === 'string') {
      params.sort = sort;
    }
    const path: string[] = [];
    values.areaNames.forEach((area) => {
      if (area && area.nameEn) {
        path.push(area.nameEn);
      }
    });
    closeAllModals();

    const uuid = `web${uuidv4()}`;
    eventLog('view_therapist_listsearch', {
      searchingId: uuid,
    });

    router.push({
      pathname: `/${path.join('/')}`,
      query: {
        ...params,
        searchingId: uuid,
      },
    });
  };

  const handleSort = (value: string) => {
    router.push({
      pathname: `/[[...slug]]`,
      query: {
        ...router.query,
        page: 1,
        sort: value,
      },
    });
  };

  const breadcrumbList = ((): IBreadcrumb[] => {
    const result = [
      {
        name: 'HOGUGU',
        href: 'https://hogugu.com',
      },
    ];
    defaultValues.areaNames.map((area, index) => {
      if (index === defaultValues.areaNames.length - 1) {
        return result.push({
          name: `${area?.name}のセラピスト`,
          href: router.pathname.replace(
            '[[...slug]]',
            Array.isArray(slug) ? slug.join('/') : slug,
          ),
        });
      }
      return result.push({
        name: area?.name,
        href: `/${area?.nameEn}`,
      });
    });
    return result;
  })();

  const renderList = () => {
    if (isLoadingTherapist) {
      return times(20).map((i) => (
        <TherapistCard isLoading key={i} searchingTherapist />
      ));
    }
    return list.map((therapist) => (
      <Link
        href={{
          pathname: `/therapist/${therapist._id}`,
          query: {
            dateTime: dateBookingFormat,
            areaCodes: areaCodes.join(','),
            areaNames: map(areaNames, 'name').join(','),
          },
        }}
        key={therapist._id}
      >
        <TherapistCard
          detail={therapist}
          onClickSeeMenu={(e) => {
            e.preventDefault();
            e.stopPropagation();
            eventLog('display_menus', {
              therapist_id: therapist._id,
            });
            router.push({
              pathname: `/therapist/${therapist._id}`,
              query: {
                dateTime: dateBookingFormat,
                areaCodes: areaCodes.join(','),
                areaNames: map(areaNames, 'name').join(','),
                viewMenu: true,
              },
            });
          }}
          searchingTherapist
        />
      </Link>
    ));
  };

  const pageSchema = seoConfig.searchingTherapistSchema({
    title: searchContent.title,
    description: searchContent.description,
    path: router.asPath,
    prefecture: get(areaNames[0], 'name', ''),
    city: get(areaNames[1], 'name', ''),
    therapists: topTherapists,
  });

  return (
    <Container size={1340} sx={sx.searchTherapistContainer}>
      <Head>
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema.webPage),
          }}
          id="search-therapist-web-page"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema.service),
          }}
          id="search-therapist-service"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageSchema.itemList),
          }}
          id="search-therapist-item-list"
          type="application/ld+json"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(seoConfig.breadcrumbList(breadcrumbList)),
          }}
          id="breadcrumb-list"
          type="application/ld+json"
        />
      </Head>
      <NextSeo {...seoConfig.searchingTherapist(searchContent)} />
      <Breadcrumbs separator={<IconChevronRight />}>
        {breadcrumbList.map((item, index) => {
          if (item.href && index < breadcrumbList.length - 1) {
            return (
              <Anchor
                component={Link}
                href={item.href || ''}
                key={`anchor-${index}`}
                sx={{ color: 'gray' }}
              >
                {item.name}
              </Anchor>
            );
          }
          return (
            <Text color="black" key={`text-${index}`}>
              {item.name}
            </Text>
          );
        })}
      </Breadcrumbs>

      <Container size={1140} sx={sx.mainContentWrapper}>
        <Box sx={sx.pageTitle}>
          <Box component="h2">セラピストを探す</Box>
        </Box>
        {isExternalSite && (
          <Text sx={sx.externalSiteNote}>
            今いる場所にセラピストをすぐ呼べる出張リラクゼーション。
            <br />
            <b>{areaNameDisplay}</b>
            に出張できるセラピストはこちら。出張費・指名料無料。
          </Text>
        )}
        <Box sx={sx.searchContentWrapper}>
          <Box sx={sx.searchFilter}>
            {mobileScreen ? (
              <SearchBox
                defaultValues={defaultValues}
                isLoading={isLoadingMenu || isLoadingPrefecture}
                menus={menus}
                onSubmit={handleSearch}
              />
            ) : (
              <TherapistsSearchForm
                defaultValues={defaultValues}
                isLoading={isLoadingMenu || isLoadingPrefecture}
                menus={menus}
                onSubmit={handleSearch}
              />
            )}
          </Box>
          <Box sx={sx.searchList}>
            <Group position="apart" sx={sx.searchListHead}>
              {!isLoadingPrefecture && !isLoadingTherapist ? (
                <Text sx={sx.total}>
                  {areaNameSEO && (
                    <h1>
                      {areaNameSEO?.seo?.h1}
                      のセラピスト
                    </h1>
                  )}
                  <span>{total}件</span>
                </Text>
              ) : (
                <Skeleton h={28} w="40%" />
              )}
              <Select
                data={[
                  { value: 'recommend', label: 'おすすめ順' },
                  { value: 'reasonable', label: '安い順' },
                  { value: 'expensive', label: '高い順' },
                  { value: 'reputation', label: '口コミが多い順' },
                  // { value: 'ranking', label: 'ランキング' },
                ]}
                defaultValue="recommend"
                onChange={handleSort}
                placeholder="おすすめ順"
                rightSection={<IconDropdown />}
                styles={styles.selectStyles}
                value={sort && typeof sort === 'string' ? sort : 'recommend'}
              />
            </Group>
            {isLoadingTherapist || list.length !== 0 ? (
              <>
                <SimpleGrid
                  breakpoints={[{ maxWidth: 992, cols: 1 }]}
                  cols={2}
                  spacing={24}
                  sx={sx.therapistList}
                >
                  {renderList()}
                </SimpleGrid>
                {!isLoadingTherapist && (
                  <Pagination page={Number(page)} total={lastPage} />
                )}
              </>
            ) : (
              <Text size={20} sx={sx.noTherapists} weight="bold">
                {`ご指定の条件に合うセラピストは\n見つかりませんでした。`}
                <br />
                検索条件を変更して、再度検索してください。
              </Text>
            )}
          </Box>
        </Box>
      </Container>
    </Container>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  query,
  req,
  res,
}) => {
  const {
    page = '1',
    slug = ['tokyo'],
    dateBooking,
    gender = '0',
    sort,
    selectMenu,
    searchingId,
  } = query;

  const { referer } = req.headers;
  const isExternalSite =
    !!referer &&
    !referer.includes(process.env.NEXT_PUBLIC_DOMAIN || '') &&
    !referer.includes(process.env.NEXT_PUBLIC_LP_URL || '') &&
    !referer.includes(process.env.NEXT_PUBLIC_LP_DOMAIN || '');

  if (req.url?.startsWith('/_next')) {
    return {
      props: {
        enabled: true,
        isExternalSite,
      },
    };
  }

  try {
    pino().info({
      page: req.url,
      reqTime: dayjs().format(),
    });
    const queryClient = getQueryClient();
    await Promise.all([
      fetchList({
        queryClient,
        ...therapistQuery.searchTherapists,
        customParams: {
          ...query,
          dateBooking: helpers.getValidDate(
            typeof dateBooking === 'string' ? dateBooking : '',
          ),
          gender: typeof gender === 'string' ? Number(gender) : 0,
          areaNames: helpers.formatSlugPath(slug),
          page: typeof page === 'string' ? Number(page) : 1,
          limit: 20,
          menuNames:
            selectMenu && typeof selectMenu === 'string'
              ? [selectMenu]
              : selectMenu,
          sort: typeof sort === 'string' ? SORT_ORDER[sort] : undefined,
          searchingId:
            typeof searchingId === 'string' ? searchingId : undefined,
        },
        omitKeys: ['selectMenu', 'slug'],
        axiosConfig: helpers.getTokenConfig(req, res),
      }),
      fetchData({
        queryClient,
        ...resourceQuery.getPrefectureList,
      }),
    ]);
    return {
      props: {
        dehydratedState: dehydrate(queryClient),
        enabled: false,
        isExternalSite,
      },
    };
  } catch (e) {
    if (get(e, 'code') === 503)
      return {
        props: {
          enabled: true,
        },
      };
    return {
      notFound: true,
    };
  }
};

SearchTherapists.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default SearchTherapists;
