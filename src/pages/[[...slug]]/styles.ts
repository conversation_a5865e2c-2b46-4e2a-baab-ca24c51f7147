import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  searchTherapistContainer: {
    padding: '20px 20px 0',
    '@media (max-width: 768px)': {
      padding: '12px 20px 0',
    },
  },
  pageTitle: {
    display: 'flex',
    justifyContent: 'center',
    margin: '38px 0 50px',
    '@media (max-width: 768px)': {
      margin: '32px 0',
    },
    h2: {
      fontSize: 40,
      margin: 0,
      position: 'relative',
      '@media (max-width: 768px)': {
        fontSize: 24,
      },
      '&:after': {
        content: '" "',
        borderRadius: 3,
        position: 'absolute',
        width: '100%',
        height: 6,
        background:
          'linear-gradient(160deg, #43749a 0%, #43749a 50%, #e8a62d 50%, #e8a62d 100%);',
        bottom: -6,
        left: 0,
      },
    },
  },
  externalSiteNote: {
    fontSize: 16,
    lineHeight: '24px',
    fontWeight: 400,
    textAlign: 'center',
    margin: '-12px auto 32px',
    b: {
      fontWeight: 700,
    },
    '@media (max-width: 768px)': {
      fontSize: 14,
      lineHeight: '20px',
      br: {
        display: 'none',
      },
    },
  },
  mainContentWrapper: {
    padding: '0 0 123px',
    '@media (max-width: 768px)': {
      border: 0,
      padding: '0 0 35px',
    },
  },
  searchContentWrapper: {
    display: 'flex',
    flexWrap: 'nowrap',
    alignItems: 'flex-start',
    gap: 30,
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      alignItems: 'center',
    },
  },
  searchFilter: {
    maxWidth: 360,
    width: '100%',
    flexShrink: 0,
    '@media (max-width: 768px)': {
      maxWidth: 335,
    },
  },
  searchList: {
    width: '100%',
    minWidth: 0,
  },
  searchListHead: {
    marginTop: 20,
    marginBottom: 30,
    position: 'relative',
    '@media (max-width: 768px)': {
      marginTop: 0,
      marginBottom: 24,
      alignItems: 'flex-end',
    },
  },
  total: {
    fontWeight: 'bold',
    fontSize: 20,
    minWidth: 250,
    h1: {
      display: 'inline',
      fontSize: 20,
      marginRight: 30,
      '@media (max-width: 768px)': {
        display: 'block',
        margin: 0,
        marginBottom: 24,
      },
    },
    span: {
      color: '#3c3c3c',
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
    },
  },
  therapistList: {
    marginBottom: 40,
    '@media (max-width: 768px)': {
      marginBottom: 30,
    },
  },
  noTherapists: {
    whiteSpace: 'normal',
    '@media (max-width: 768px)': {
      fontSize: 15,
      whiteSpace: 'pre-line',
      borderBottom: '1px solid #e0e0e0',
      paddingBottom: 32,
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  selectStyles: {
    root: {
      '@media (max-width: 768px)': {
        position: 'absolute',
        right: 0,
        bottom: -4,
      },
    },
    input: {
      color: '#3c3c3c',
      height: 40,
      fontSize: 14,
      maxWidth: 190,
      '@media (max-width: 768px)': {
        maxWidth: 150,
      },
    },
    item: {
      fontSize: 14,
    },
    rightSection: {
      pointerEvents: 'none',
    },
  },
};
