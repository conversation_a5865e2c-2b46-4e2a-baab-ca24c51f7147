import IconMenu from '@icons/icon-menu.svg';
import { Button, Container, Group, NavLink, Title } from '@mantine/core';
import Layout from 'components/Layout';
import { useUser } from 'hooks';
import Link from 'next/link';
import { NextSeo } from 'next-seo';
import { suffixTitle } from 'utils/seoConfig';

import { styles, sx } from './styles';

const TermsPage = () => {
  const { data: currentUserData } = useUser();

  return (
    <Container size={1140} sx={sx.wrapper}>
      <NextSeo
        nofollow
        noindex
        openGraph={{
          title: `各種規約 | ${suffixTitle}`,
        }}
        title={`各種規約 | ${suffixTitle}`}
      />
      <Title order={3}>各種規約</Title>
      <Group position="center" sx={sx.groupLink}>
        <NavLink
          component={Link}
          href={`${process.env.NEXT_PUBLIC_LP_URL}/terms-of-use/index.html`}
          icon={<IconMenu />}
          label="利用規約"
          styles={styles.menuItem}
          target="_blank"
        />
        <NavLink
          component={Link}
          href={`${process.env.NEXT_PUBLIC_LP_URL}/act-on-specified-commercial-transaction.html`}
          icon={<IconMenu />}
          label="特定商取引法に基づく表記"
          styles={styles.menuItem}
          target="_blank"
        />
        <NavLink
          component={Link}
          href={`${process.env.NEXT_PUBLIC_LP_URL}/privacy.html`}
          icon={<IconMenu />}
          label="プライバシーポリシー"
          styles={styles.menuItem}
          target="_blank"
        />
        <NavLink
          component={Link}
          href={`${process.env.NEXT_PUBLIC_LP_URL}/attention-for-customer.html `}
          icon={<IconMenu />}
          label="禁止事項と疾患の対応"
          styles={styles.menuItem}
          target="_blank"
        />
      </Group>
      <Group position="center">
        <Button
          component={Link}
          href={currentUserData ? '/my-page' : '/login'}
          size="lg"
          sx={sx.backBtn}
        >
          マイページへ
        </Button>
      </Group>
    </Container>
  );
};

TermsPage.getLayout = (page: React.ReactNode) => {
  return <Layout>{page}</Layout>;
};

export default TermsPage;
