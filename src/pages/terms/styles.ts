import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  wrapper: {
    paddingTop: 80,
    paddingBottom: 60,
    h3: {
      fontSize: 30,
      marginBottom: 22,
    },
    '@media (max-width: 768px)': {
      paddingTop: 50,
      paddingBottom: 40,
      h3: {
        fontSize: 22,
        marginBottom: 12,
      },
    },
  },

  groupLink: {
    gap: 30,
    maxWidth: 768,
    margin: 'auto',
    '@media (max-width: 768px)': {
      gap: 10,
    },
  },

  backBtn: {
    width: 300,
    marginTop: 83,
    '@media (max-width: 768px)': {
      width: 180,
      marginTop: 24,
    },
  },
};

export const styles: Record<string, any> = {
  menuItem: (theme: MantineTheme) => ({
    root: {
      padding: '10px 16px 10px 10px',
      height: 70,
      borderRadius: 4,
      width: 315,
      border: 'solid 1px #bdccd3',
      backgroundColor: 'white',
      fontSize: 16,
      '&:hover': {
        color: 'white',
        backgroundColor: theme.colors.queenBlue[6],
      },
      '@media (max-width: 768px)': {
        width: '100%',
      },
    },
    icon: {
      width: 40,
      height: 40,
      marginRight: 14,
      borderRadius: '50%',
      backgroundColor: theme.colors.queenBlue[6],
      '*:hover > &': {
        backgroundColor: 'white',
        svg: {
          path: {
            fill: `${theme.colors.queenBlue[6]} !important`,
          },
        },
      },
      svg: {
        color: 'white',
        fill: 'white',
        width: 18,
        height: 'auto',
        path: {
          fill: 'white !important',
        },
      },
      '@media (max-width: 768px)': {
        width: 36,
        height: 36,
        marginRight: 12,
        svg: {
          width: 15,
        },
      },
    },
  }),
};
