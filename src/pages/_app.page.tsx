import '@fontsource/noto-sans-jp';
import '@fontsource/noto-sans-jp/700.css';
import 'utils/yupConfig';

import { MantineProvider, ScrollArea } from '@mantine/core';
import { DatesProvider } from '@mantine/dates';
import { useViewportSize } from '@mantine/hooks';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';
import type { DehydratedState } from '@tanstack/react-query';
import { Hydrate, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import FirebaseProvider from 'components/FirebaseProvider';
import RouteProvider from 'components/RouteProvider';
import RouterProgress from 'components/RouterProgress';
import { merge } from 'lodash';
import type { NextPage } from 'next';
import type { AppProps } from 'next/app';
import dynamic from 'next/dynamic';
import Head from 'next/head';
import { DefaultSeo } from 'next-seo';
import type { ReactElement, ReactNode } from 'react';
import React from 'react';
import theme from 'theme';
import getQueryClient from 'utils/queryClient';
import seoConfig from 'utils/seoConfig';

const DateTimePickerModal = dynamic(
  () => import('components/Modals').then((r) => r.DateTimePickerModal),
  { ssr: false },
);
const LocationPickerModal = dynamic(
  import('components/Modals').then((r) => r.LocationPickerModal),
  { ssr: false },
);
const SearchTherapistsFormModal = dynamic(
  import('components/Modals').then((r) => r.SearchTherapistsFormModal),
  { ssr: false },
);
const MenuSelectionModal = dynamic(
  import('components/Modals').then((r) => r.MenuSelectionModal),
  { ssr: false },
);
const MenuPriceSelectModal = dynamic(
  import('components/Modals').then((r) => r.MenuPriceSelectModal),
  { ssr: false },
);
const MenuInfoModal = dynamic(
  import('components/Modals').then((r) => r.MenuInfoModal),
  { ssr: false },
);
const QRBookingModal = dynamic(
  import('components/Modals').then((r) => r.QRBookingModal),
  { ssr: false },
);
const AlertModal = dynamic(
  import('components/Modals').then((r) => r.AlertModal),
  { ssr: false },
);
const BookingProgressModal = dynamic(
  import('components/Modals').then((r) => r.BookingProgressModal),
  { ssr: false },
);
const UntreatedBookingModal = dynamic(
  import('components/Modals').then((r) => r.UntreatedBookingModal),
  { ssr: false },
);
const BookingReviewModal = dynamic(
  import('components/Modals').then((r) => r.BookingReviewModal),
  { ssr: false },
);
const CancelBookingModal = dynamic(
  import('components/Modals').then((r) => r.CancelBookingModal),
  { ssr: false },
);
const ReferralCouponModal = dynamic(
  import('components/Modals').then((r) => r.ReferralCouponModal),
  {
    ssr: false,
  },
);

const ServiceDown = dynamic(import('components/ServiceDown'), { ssr: false });

type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout<P = {}> = {
  Component: NextPageWithLayout<P>;
} & AppProps<P>;

// export const reportWebVitals = (metric: NextWebVitalsMetric) => {
//   console.log(metric);
// };

const MyApp = ({
  Component,
  pageProps,
}: AppPropsWithLayout<{ dehydratedState?: DehydratedState }>) => {
  const getLayout = Component.getLayout ?? ((page) => page);
  const { height } = useViewportSize();
  const queryClient = getQueryClient();
  return (
    <MantineProvider
      theme={merge({}, theme, {
        other: {
          viewHeight: height,
        },
      })}
      withGlobalStyles
      withNormalizeCSS
    >
      <Head>
        <meta
          content="width=device-width, initial-scale=1, maximum-scale=1"
          name="viewport"
        />
      </Head>
      <DefaultSeo
        dangerouslySetAllPagesToNoFollow={
          process.env.NEXT_PUBLIC_DEPLOY_ENV !== 'production'
        }
        dangerouslySetAllPagesToNoIndex={
          process.env.NEXT_PUBLIC_DEPLOY_ENV !== 'production'
        }
        {...seoConfig.default}
      />
      <QueryClientProvider client={queryClient}>
        <Hydrate state={pageProps.dehydratedState}>
          <RouterProgress />
          <ReactQueryDevtools initialIsOpen={false} />
          <FirebaseProvider />
          <RouteProvider />
          <ModalsProvider
            modalProps={{
              scrollAreaComponent: ScrollArea.Autosize,
            }}
            modals={{
              DateTimePickerModal: DateTimePickerModal as React.FC,
              LocationPickerModal: LocationPickerModal as React.FC,
              SearchTherapistsFormModal: SearchTherapistsFormModal as React.FC,
              MenuSelectionModal: MenuSelectionModal as React.FC,
              MenuPriceSelectModal: MenuPriceSelectModal as React.FC,
              MenuInfoModal: MenuInfoModal as React.FC,
              QRBookingModal: QRBookingModal as React.FC,
              AlertModal: AlertModal as React.FC,
              BookingProgressModal: BookingProgressModal as React.FC,
              UntreatedBookingModal: UntreatedBookingModal as React.FC,
              BookingReviewModal: BookingReviewModal as React.FC,
              CancelBookingModal: CancelBookingModal as React.FC,
              ReferralCouponModal: ReferralCouponModal as React.FC,
            }}
          >
            <DatesProvider settings={{ locale: 'ja' }}>
              <Notifications containerWidth={500} />
              <ServiceDown />
              <div id="recaptcha-container" />
              {getLayout(<Component {...pageProps} />)}
            </DatesProvider>
          </ModalsProvider>
        </Hydrate>
      </QueryClientProvider>
    </MantineProvider>
  );
};

export default MyApp;
