export interface IReview {
  _id: string;
  rating: number;
  comment: {
    overall: string;
  };
  categories?: {
    technique: 'GOOD' | 'BAD';
    service: 'GOOD' | 'BAD';
    cost: 'GOOD' | 'BAD';
  };
}

export interface ReviewPayload {
  bookingId: string;
  user: {
    id: string;
    role: string;
  };
  rating: number;
  comment: {
    overall: string;
  };
  categories: {
    technique: 'GOOD' | 'BAD';
    service: 'GOOD' | 'BAD';
    cost: 'GOOD' | 'BAD';
  };
}
