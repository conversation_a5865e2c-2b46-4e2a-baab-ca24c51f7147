export interface ICheckPhone {
  total: boolean;
  token?: string;
  phone?: string;
  error: string;
}

export interface CheckPhonePayload {
  countryCode: string;
  phoneNumber: string;
  action?: 'register' | 'login';
  type?: 'call' | 'sms';
}

export interface SignInPayload {
  code: string;
  type: 'call' | 'sms';
  phone: string;
}

export interface ICustomer {
  _id: string;
  isCompletedProfile: boolean;
  lastLogin: string;
  phone: string;
  name: string;
  status: string;
  birthday: string;
  updatedAt: string;
  createdAt: string;
  gender: number;
  email?: string;
  profilePicture?: {
    url: string;
  };
  isTesting?: boolean;
  requestingDeleteAccount?: {
    createdAt: string;
    deletedAt: string;
  };
  invitationCoupon?: string;
  invitationCode?: string;
  firebaseUserId?: string;
}

export interface RequestDeleteAccountPayload {
  reasons: string[];
  comment?: string;
}

export interface IInvitationCode {
  code: string;
}
