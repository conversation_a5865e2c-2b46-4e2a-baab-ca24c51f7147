import type { QueryModel } from 'utils/type';

const authQuery = {
  checkPhone: {
    apiUrl: '/account-api/mobile/customer/accounts/phone-number',
    method: 'GET',
  },
  loginV2: {
    apiUrl: '/account-api/v2/mobile/customer/login',
    method: 'POST',
  },
  login: {
    apiUrl: '/account-api/mobile/customer/login',
    method: 'POST',
  },
  currentUser: {
    apiUrl: '/api/me',
    method: 'GET',
    queryKey: ['currentUser'],
    axiosConfig: {
      baseURL: '/',
    },
  },
  updateProfile: {
    apiUrl: '/account-api/mobile/customer/me',
    method: 'PUT',
  },
  checkCanDeleteAccount: {
    apiUrl: '/booking-api/mobile/customer/has-incomplete-booking',
    method: 'GET',
    queryKey: ['currentUser', 'check-has-incomplete-booking'],
  },
  requestDeleteAccount: {
    apiUrl: '/account-api/mobile/customer/request-delete-account',
    method: 'POST',
  },
  revokeDeleteAccount: {
    apiUrl: '/account-api/mobile/customer/revoke-delete-account',
    method: 'POST',
  },
  deleteAccountImmediately: {
    apiUrl: '/account-api/mobile/customer/delete-account-immediately',
    method: 'PATCH',
  },
  inviterGetInvitationCode: {
    apiUrl: '/account-api/mobile/customer/coupon/invitation',
    method: 'GET',
    queryKey: ['currentUser', 'my-invitation-code'],
    staleTime: Infinity,
    enabled: false,
  },
  getIncentiveCoupon: {
    queryKey: ['currentUser', 'incentive-coupon-list'],
    apiUrl: '/account-api/mobile/customer/coupon/incentive',
    method: 'GET',
  },
  couponList: {
    queryKey: ['currentUser', 'coupon-list'],
    apiUrl: '/account-api/mobile/customer/coupon/available',
    method: 'GET',
  },
} satisfies QueryModel;

export default authQuery;
