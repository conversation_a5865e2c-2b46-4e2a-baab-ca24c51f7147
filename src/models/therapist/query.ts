import type { QueryModel } from 'utils/type';

const therapistQuery = {
  getTherapistList: {
    queryKey: ['public', 'therapist-list'],
    apiUrl: '/matching-api/mobile/customer/therapist',
    method: 'GET',
  },
  searchTherapists: {
    queryKey: ['public', 'search', 'therapist-list'],
    apiUrl: '/matching-api/mobile/customer/therapist',
    method: 'PATCH',
  },
  getTherapistDetail: (params: Record<string, unknown>) => ({
    queryKey: ['public', 'therapist-detail', params],
    apiUrl: `/account-api/mobile/customer/therapist/${params.id}`,
    method: 'GET',
  }),
  getTherapistReviews: (params: Record<string, unknown>) => ({
    queryKey: ['public', 'therapist-reviews', params],
    apiUrl: `/review/mobile/customer/review/therapist/${params.id}`,
    method: 'GET',
  }),
  getTherapistMenus: (params: Record<string, unknown>) => ({
    queryKey: ['public', 'therapist-menus', params],
    apiUrl: `/booking-api/mobile/customer/therapist/${params.id}/menus`,
    method: 'GET',
  }),
  getTherapistTimeslots: {
    apiUrl: ({ therapistId }: Record<string, unknown>) =>
      `/matching-api/mobile/customer/therapist/${therapistId}/timeslots`,
    method: 'PATCH',
  },
} satisfies QueryModel;

export default therapistQuery;
