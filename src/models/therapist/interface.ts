export interface ITherapistItem {
  _id: string;
  email: string;
  fullName: string;
  nickName: string;
  introduction: string;
  gender: number;
  certificate: string;
  isReviewed?: boolean;
  isNew?: boolean;
  departurePoint: {
    areaCode: string;
    level: number;
    name: string;
  }[];
  experience: {
    name: string;
  };
  approvalRate: number;
  avgResponseTime: number;
  phone: number;
  avatar?: string;
  profilePicture?: {
    privateUrl: string;
    url: string;
  };
  specialTreatmentTime: {
    duration: number;
    status: boolean;
  };
  sumDoneBookings?: number;
  sumCancelCountBookings?: number;
  summaryResponseRate?: {
    approvalRate: number;
    responseRate: number;
    responseTime: string;
    sumDoneBookings: number;
    sumChargedBookings?: number;
  };
  summaryReview?: {
    sumRating: number;
    sumReviewer: number;
  };
  freeTimeslots?: string[];
  minMenu?: {
    duration: number;
    price: number;
  };
  therapistCurrentTreatment?: {
    isBusy: boolean;
    endTime: string | null;
  };
}

export interface IPriceOption {
  currency: string;
  duration: number;
  originalPrice: number;
  price: number;
}

export interface IMenuItem {
  _id: string;
  title: string;
  titleEn?: string;
  detail: string;
  images?: {
    icon: Record<string, string>;
    large?: Record<string, string>;
    small?: Record<string, string>;
  };
  selectedOption?: IPriceOption;
  selectedExtension?: IPriceOption;
  options: IPriceOption[];
  presetPrice: {
    currency: string;
    duration: number;
    originalPrice: number;
    price: number;
    extension: {
      maxDuration: number;
      currency: string;
      duration: number;
      originalPrice: number;
      price: number;
    };
  };
  type: string;
}

export interface IReviewItem {
  _id: string;
  bookingAddress: string;
  customerGender: number;
  menus: {
    id: string;
    duration: number;
    title: string;
  }[];
  rating: number;
  comment?: {
    therapist?: string;
    customer?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface IReviewList {
  reviews: {
    data: IReviewItem[];
    page: number;
    lastPage: number;
    total: number;
  };
  summaryReview: {
    sumRating: number;
    sumReviewer: number;
  };
}
