export interface INotificationItem {
  _id: string;
  type: string;
  createdAt: string;
  extras: {
    type?: string;
    bookingId?: string;
    status?: string;
  };
  data: {
    type?: string;
    bookingId?: string;
    dateTime_booking?: string;
    time_arrivalTime?: string;
    couponCode?: string;
  };
  message: string;
  read: boolean;
}

export interface ISummaryNotification {
  total: number;
}
