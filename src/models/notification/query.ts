import type { QueryModel } from 'utils/type';

const notificationQuery = {
  getNotificationList: {
    queryKey: ['currentUser', 'notification-list'],
    apiUrl: '/notification-api/web/customer/notification',
    method: 'GET',
  },
  getSummaryNotification: {
    queryKey: ['currentUser', 'summary-notification'],
    apiUrl:
      '/notification-api/mobile/customer/notification/unread-notification-number',
    method: 'GET',
  },
  markReadBookingNotification: {
    apiUrl: ({ bookingId }: Record<string, unknown>) =>
      `/notification-api/mobile/customer/notification/${bookingId}/readBookingTimeLine`,
    method: 'PATCH',
  },
  markReadAllNotification: {
    apiUrl: '/notification-api/web/customer/notification/mark-read-all',
    method: 'PATCH',
  },
  markReadNotification: {
    apiUrl: ({ notificationId }: Record<string, unknown>) =>
      `/notification-api/mobile/customer/notification/${notificationId}/readUsers`,
    method: 'POST',
  },
} satisfies QueryModel;

export default notificationQuery;
