import type { QueryModel } from 'utils/type';

const bookingQuery = {
  getMenuList: {
    queryKey: ['public', 'customer', 'menu-list'],
    apiUrl: '/booking-api/mobile/customer/menu',
    method: 'GET',
  },
  getBookingHistory: {
    queryKey: ['currentUser', 'booking-history-list'],
    apiUrl: '/booking-api/mobile/customer/booking',
    method: 'GET',
  },
  checkAvailableTimeslot: (params: Record<string, unknown>) => ({
    queryKey: ['public', 'therapist', 'available-time', params],
    apiUrl: `/matching-api/mobile/customer/therapist/${params.therapistId}/check-available-time-slot`,
    method: 'PATCH',
    customParams: params,
  }),
  checkAvailableArea: (params: Record<string, unknown>) => ({
    queryKey: ['public', 'therapist', 'available-area', params],
    apiUrl: `/account-api/mobile/customer/therapist/${params.therapistId}/check-available-area`,
    method: 'PATCH',
    customParams: params,
  }),
  createBooking: {
    apiUrl: '/booking-api/mobile/customer/bookings',
    method: 'POST',
  },
  getBookingDetail: (params: Record<string, unknown>) => ({
    queryKey: ['currentUser', 'booking-detail', params],
    apiUrl: `/booking-api/mobile/customer/booking/${params.bookingId}`,
    method: 'GET',
  }),
  chargePayment: {
    apiUrl: ({ bookingId }: Record<string, unknown>) =>
      `/booking-api/mobile/customer/bookings/${bookingId}/charge3D`,
    method: 'POST',
  },
  rechargePayment: {
    apiUrl: ({ bookingId }: Record<string, unknown>) =>
      `/booking-api/mobile/customer/bookings/${bookingId}/recharge3D`,
    method: 'POST',
  },
  getOnGoingBooking: {
    apiUrl: '/booking-api/mobile/customer/onGoingbooking',
    method: 'GET',
    queryKey: ['currentUser', 'on-going-boooking'],
    staleTime: Infinity,
  },
  getCancelBookingFee: {
    apiUrl: ({ bookingId }: Record<string, unknown>) =>
      `/booking-api/mobile/customer/booking/${bookingId}/canceledTime`,
    method: 'PUT',
  },
  getCancellationReason: {
    apiUrl: '/booking-api/mobile/customer/cancellation-reason',
    method: 'GET',
    queryKey: ['cancellation-reason'],
    staleTime: Infinity,
  },
  cancelBooking: {
    apiUrl: ({ bookingId }: Record<string, unknown>) =>
      `/booking-api/mobile/customer/booking/${bookingId}/cancellation-reason`,
    method: 'PUT',
    omitKeys: ['bookingId'],
  },
  changeBookingStatus: {
    apiUrl: ({ bookingId }: Record<string, unknown>) =>
      `/booking-api/mobile/customer/booking/${bookingId}/status`,
    method: 'PUT',
    omitKeys: ['bookingId'],
  },
  getTotalPoints: {
    queryKey: ['currentUser', 'total-points'],
    apiUrl: '/booking-api/mobile/customer/points',
    method: 'GET',
  },
  getPointHistory: {
    queryKey: ['currentUser', 'point-history'],
    apiUrl: '/booking-api/mobile/customer/points/histories',
    method: 'GET',
  },
  checkPoints: {
    queryKey: ['currentUser', 'check-points'],
    apiUrl: '/booking-api/mobile/customer/points',
    method: 'PATCH',
    staleTime: 0,
  },
} satisfies QueryModel;

export default bookingQuery;
