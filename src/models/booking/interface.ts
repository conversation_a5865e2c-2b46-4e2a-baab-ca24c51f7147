import type { BuildingType } from 'components/Profile/AddressHistoryForm/schema';
import type { IMenuItem, ITherapistItem } from 'models/therapist';
import type {
  POINT_HISTORY_BOOKING_TYPE,
  POINT_HISTORY_REASON,
} from 'utils/constants';

export interface IPayment {
  type: 'cash' | 'gcard3d';
  amount: number;
  extra: {
    cardId: string;
  };
  transaction: {
    amount: number;
    status: number;
    error: {
      code: string;
      note: string;
    } | null;
    extra?: {
      source?: {
        id: string;
        name: string;
        brand: string;
        last4: string;
        exp_month: number;
        exp_year: number;
      };
    };
  };
}

export interface IBookingItem {
  _id: string;
  dateBooking: string;
  estimateEndBooking: string;
  duration: number;
  therapist: {
    _id: string;
    nickName: string;
    avatar?: string;
    isReviewed?: boolean;
  };
  currentStatus: {
    category: string;
    status: string;
    reason: string;
  };
  menus: IMenuItem[];
  totalPrice: number;
  payment: IPayment;
  cancellingNote?: {
    isCharged?: boolean;
  };
  prefecture?: {
    name: string;
    areaCode: string;
  };
  city?: {
    name: string;
    areaCode: string;
  };
  ward?: {
    name: string;
    areaCode: string;
  };
  district?: {
    name: string;
    areaCode: string;
  };
  address?: string;
  googleAddress?: string;
  buildingType?: BuildingType | '';
  nameplate?: string;
  buildingDetails?: string;
  accessMethod?: string;
  addressId?: string;
  parkingNote?: string;
  customerNote?: string;
  reviewExpired?: boolean;
}

export interface ICoupon {
  active?: boolean;
  amount: number;
  code: string;
  createdAt: string;
  currency: '%' | '¥';
  title: string | null;
  description: string | null;
  updatedAt: string;
  _id?: string;
  id?: string;
  rate?: number | null;
  servicerId?: string | null;
  type?: 'Invitation' | 'Incentive' | 'Public';
  availableCount?: number;
  expiredAt?: string;
  // type = 'Public'
  therapist?: {
    _id: string;
    nickName: string;
    fullName: string;
    profilePicture?: {
      url: string;
    };
  };
  rules?: {
    timezone: string;
    // type = 'Invitation' | 'Incentive'
    availableDays?: number;
    issueCouponNumber?: number;
    // type = 'Public'
    end?: string | null;
    start: string;
    min?: number;
  };
}

export interface IBookingDetail {
  _id: string;
  therapist: ITherapistItem;
  therapistGender: number;
  totalPrice: number;
  address?: string;
  googleAddress?: string;
  addressId?: string;
  buildingType?: BuildingType | '';
  nameplate?: string;
  buildingDetails?: string;
  accessMethod?: string;
  createdAt: string;
  dateBooking: string;
  duration: number;
  parkingNote?: string;
  customerNote?: string;
  payment: IPayment;
  currentStatus: {
    category: string;
    status: string;
    reason: string;
    timestamp: string;
    requestBy: string | null;
  };
  customer: {
    id: string;
    name: string;
    phone: string;
  };
  menus: IMenuItem[];
  extensions?: IMenuItem[];
  coupon?: {
    amount: number;
    code: string;
    transactionId: number;
  };
  statusHistory: {
    category: string;
    status: string;
    reason: string;
    timestamp: string;
  }[];
  prefecture?: {
    name: string;
    areaCode: string;
  };
  city?: {
    name: string;
    areaCode: string;
  };
  ward?: {
    name: string;
    areaCode: string;
  };
  district?: {
    name: string;
    areaCode: string;
  };
  cancellingNote?: {
    isCharged: boolean;
    note?: string;
    noteForTherapist?: string;
  };
  review?: IBookingReview;
  expiryCancelingBooking?: number;
  isViewed?: boolean;
  midnightFee?: number;
  reviewExpired?: boolean;
  therapistCurrentTreatment?: {
    isBusy: boolean;
    endTime: string | null;
  };
  RedirectUrl: string;
  point?: {
    used?: {
      point: number;
      discount: number;
      isUsed: boolean;
      referenceId: string;
    };
    granted?: {
      point: number;
    };
  };
}

export interface IBookingReview {
  rating: number;
  updatedAt: string;
  createdAt: string;
  bookingId: string;
  categories: {
    technique: 'GOOD' | 'BAD';
    service: 'GOOD' | 'BAD';
    cost: 'GOOD' | 'BAD';
  };
  comment?: {
    therapist?: string;
    overall?: string;
  };
  menus: IMenuItem[];
}

export interface IOnGoingBooking {
  bookingNotTreatment:
    | (IBookingDetail & {
        isViewed?: boolean;
      })
    | null;
  data: IBookingItem[];
}

export interface ITotalPoints {
  availablePoints: number; // unit: point
  minPoints: number; // unit: point
  minBookingAmount: number; // unit: ¥
  exchangeRate: {
    using: {
      pointMultiple: number;
      point: number; // unit: point
      amount: number; // unit: ¥
    };
    granting: number;
  };
  expirationInYears: number; // unit: year
}

export interface IPointHistoryItem {
  accountId?: string;
  bookingId?: string;
  bookingType?: POINT_HISTORY_BOOKING_TYPE;
  reason: POINT_HISTORY_REASON;
  createdAt: string;
  updatedAt: string;
  point: number;
  expiryDate?: string;
}

export interface ICheckPointAmount {
  availablePoints: number;
  minBookingAmount: number;
  minPoints: number;
  maxPoints: number;
  discount: number;
}
