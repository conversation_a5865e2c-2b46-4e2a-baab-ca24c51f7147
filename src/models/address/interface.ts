import type { BuildingType } from 'components/Profile/AddressHistoryForm/schema';
import type { IAreaItem } from 'models/resource';

export interface IAddressHistory {
  _id: string;
  address: string;
  areas: IAreaItem[];
  error?: string;
  isGoogle?: boolean;
}

export interface AddressHistoryItem {
  address: string;
  buildingType?: BuildingType | '';
  nameplate?: string;
  buildingDetails?: string;
  accessMethod?: string;
  areaCodes: string[];
  isGoogle?: boolean;
}
