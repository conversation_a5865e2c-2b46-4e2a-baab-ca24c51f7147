import type { QueryModel } from 'utils/type';

const addressQuery = {
  getAllAddressHistory: {
    queryKey: ['currentUser', 'address-history-list'],
    apiUrl: '/account-api/mobile/customer/address-history',
    method: 'GET',
  },
  addNewAddressHistory: {
    apiUrl: '/account-api/mobile/customer/address-history',
    method: 'POST',
  },
  deleteNewAddressHistory: {
    apiUrl: ({ addressId }: Record<string, unknown>) =>
      `/account-api/mobile/customer/address-history/${addressId}`,
    method: 'DELETE',
  },
} satisfies QueryModel;

export default addressQuery;
