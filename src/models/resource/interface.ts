export interface IPrefectureItem {
  areaCode: string;
  group: string;
  name: string;
  nameEn: string;
  googleName: string;
  level: number;
  children?: ICityItem[];
  child?: ICityItem;
}

export interface ICityItem {
  areaCode: string;
  type: string;
  name: string;
  nameEn: string;
  googleName: string;
  level: number;
  parent: string;
  image?: {
    url: string;
    privateUrl: string;
  };
  children?: ICityItem[];
  child?: ICityItem;
}

export interface GetAreaCoordinatesPayload {
  lat: number;
  lng: number;
}

export interface IAreaItem {
  name: string;
  areaCode: string;
  level: number;
  children?: IAreaItem[];
}

export interface IDeletionReasonItem {
  code: string;
  text: string;
}

export type IConfigurationItem = {
  key: string;
  value?: string;
  banner?: string;
  text?: string;
};

export type IBreadcrumb = {
  name: string;
  href?: string;
};
