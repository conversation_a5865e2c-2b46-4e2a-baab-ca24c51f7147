import type { QueryModel } from 'utils/type';

const resourceQuery = {
  getPrefectureList: {
    queryKey: ['public', 'prefecture-list'],
    apiUrl: '/account-api/web/customer/group-areas',
    method: 'GET',
  },
  getAreaCoordinates: {
    apiUrl: '/account-api/web/customer/area/coordinates',
    method: 'GET',
  },
  getAllAreas: {
    queryKey: ['public', 'areas-list'],
    apiUrl: '/account-api/mobile/customer/area',
    method: 'GET',
  },
  checkCoupon: {
    apiUrl: ({ code }: Record<string, unknown>) =>
      `/booking-api/mobile/customer/coupon/${code}`,
    method: 'PATCH',
  },
  getDeletionReasons: {
    queryKey: ['public', 'deletion-reasons'],
    apiUrl: '/account-api/mobile/customer/deletion-reasons',
  },
  voiceToken: {
    queryKey: ['currentUser', 'voice-token'],
    apiUrl: '/media/mobile/customer/voice/token',
    method: 'GET',
  },
  getConfigs: {
    queryKey: ['public', 'config'],
    apiUrl: '/account-api/configurations',
    method: 'GET',
    staleTime: Infinity,
  },
} satisfies QueryModel;

export default resourceQuery;
