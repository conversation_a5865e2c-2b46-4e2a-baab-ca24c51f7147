import type { ValidCardNiceType } from 'utils/constants';

export interface AddPaymentCardPayload {
  token: string;
}

export interface GetTokenResponse {
  resultCode: string;
  tokenObject?: { token: string };
}

export interface CardListResponse {
  cards: ICardDetail[];
  defaultCard?: string;
}

export interface ICardDetail {
  brand: ValidCardNiceType;
  exp_month: number;
  exp_year: number;
  id: string;
  last4: string;
  name: string;
}
