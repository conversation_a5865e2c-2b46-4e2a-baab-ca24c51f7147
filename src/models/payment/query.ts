import type { QueryModel } from 'utils/type';

const bookingQuery = {
  addPaymentCard: {
    apiUrl: '/payment-api/mobile/customer/gcards',
    method: 'POST',
  },
  checkCard: {
    apiUrl: ({ cardToken }: Record<string, unknown>) =>
      `/payment-api/mobile/customer/gcards/${cardToken}/detail`,
    method: 'GET',
  },
  getCardList: {
    apiUrl: '/payment-api/mobile/customer/gcards',
    method: 'GET',
    queryKey: ['currentUser', 'card-list'],
  },
  removeCard: {
    apiUrl: ({ cardId }: Record<string, unknown>) =>
      `/payment-api/mobile/customer/gcards/${cardId}`,
    method: 'DELETE',
  },
  setDefaultCard: {
    apiUrl: ({ cardId }: Record<string, unknown>) =>
      `/payment-api/mobile/customer/gcards/${cardId}/defaultCard`,
    method: 'PATCH',
  },
} satisfies QueryModel;

export default bookingQuery;
