import type { MantineThemeComponents } from '@mantine/styles/lib/theme/types/MantineTheme';

const Button: MantineThemeComponents[string] = {
  defaultProps: {
    radius: 3,
    size: 'md',
    loaderProps: {
      width: 16,
      height: 16,
    },
  },
  styles: (theme) => ({
    root: {
      transition: 'all 200ms ease',
      boxShadow: '0px 2px 2px 0px rgba(0, 0, 0, 0.16)',
      '&:focus': {
        boxShadow: `0px 0px 0px 2px ${theme.colors.gainsboro[0]}`,
      },
    },
  }),
  variants: {
    filled: (theme, params) => ({
      root: {
        backgroundColor: theme.colors[params.color || theme.primaryColor]?.[0],
        '&:hover': {
          backgroundColor:
            theme.colors[`${params.color || theme.primaryColor}Hover`]?.[0],
        },
        '&:active': {
          backgroundColor:
            theme.colors[`${params.color || theme.primaryColor}Active`]?.[0],
        },
        '&:disabled, &[data-disabled]': {
          backgroundColor: theme.colors.silver[0],
          color: theme.colors.platinum[0],
        },
      },
    }),
    outline: (theme, params) => ({
      root: {
        backgroundColor: theme.colors.cultured[0],
        border: `2px solid ${
          theme.colors[params.color || theme.primaryColor]?.[0]
        }`,
        color: theme.colors[params.color || theme.primaryColor]?.[0],
        '&:hover': {
          backgroundColor: theme.colors.platinum[0],
        },
        '&:active': {
          backgroundColor: theme.colors.silver[0],
        },
        '&:disabled, &[data-disabled]': {
          borderColor: theme.colors.silver[0],
          backgroundColor: theme.colors.cultured[0],
          color: theme.colors.silver[0],
        },
      },
    }),
  },
  sizes: {
    lg: (theme) => ({
      root: {
        padding: '0 16px',
        minHeight: 60,
        fontSize: 18,
        fontWeight: 700,
        lineHeight: '26px',
        [theme.fn.smallerThan('sm')]: {
          fontSize: 16,
          minHeight: 50,
        },
      },
    }),
    md: () => ({
      root: {
        fontSize: 14,
        fontWeight: 700,
        lineHeight: '20px',
        padding: '0 16px',
        minHeight: 40,
      },
    }),
    sm: () => ({
      root: {
        fontSize: 12,
        fontWeight: 700,
        lineHeight: '16px',
        padding: '0 12px',
        minHeight: 28,
      },
    }),
  },
};
export default Button;
