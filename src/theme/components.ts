import type { MantineThemeComponents } from '@mantine/styles/lib/theme/types/MantineTheme';

import Button from './Button';

const components: MantineThemeComponents = {
  Button,
  Container: {
    styles: {
      root: {
        paddingLeft: 20,
        paddingRight: 20,
      },
    },
  },
  Text: {
    styles: {
      root: {
        whiteSpace: 'pre-line',
        wordBreak: 'break-all',
      },
    },
  },
  NavLink: {
    styles: {
      root: {
        padding: 0,
        width: 'auto',
        color: '#3C3C3C',
        fontWeight: 'bold',
        '&[data-active], &[data-active]:hover, &:hover': {
          backgroundColor: 'transparent',
          color: '#43749A',
        },
      },
      label: {
        fontSize: 14,
      },
    },
  },
  Burger: {
    styles: {
      burger: {
        borderRadius: '2px',
        height: '3px !important',
        '&:before, &:after': {
          borderRadius: '2px',
          height: 3,
        },
      },
    },
  },
  Breadcrumbs: {
    styles: {
      root: {
        color: 'gray',
      },
      separator: {
        color: 'gray',
      },
      breadcrumb: {
        fontSize: '12px',
      },
    },
  },
  Modal: {
    defaultProps: {
      padding: 0,
      closeOnEscape: false,
      closeOnClickOutside: false,
      radius: '6px',
    },
    styles: (_theme: any, params: any) => ({
      inner: {
        paddingLeft: params.fullScreen ? 0 : 20,
        paddingRight: params.fullScreen ? 0 : 20,
      },
      header: {
        position: 'absolute',
        backgroundColor: 'transparent',
        margin: 0,
        padding: 0,
        top: 30,
        right: 30,
        '@media (max-width: 768px)': {
          top: 15,
          right: 15,
        },
      },
      content: {
        backgroundColor: 'transparent',
        position: 'relative',
        '.mantine-ScrollArea-viewport': {
          position: 'relative',
        },
      },
      close: {
        top: '0px',
        right: '0px',
        backgroundColor: '#43749a',
        color: '#ffffff',
        width: 50,
        height: 50,
        fontSize: 20,
        zIndex: 10,
        svg: {
          width: 40,
          height: 40,
        },
        '@media (max-width: 768px)': {
          width: 30,
          height: 30,
          svg: {
            width: 20,
            height: 20,
          },
        },
        '&:hover': {
          backgroundColor: '#446783',
        },
      },
    }),
  },
  Notification: {
    styles: {
      root: {
        padding: '24px !important',
      },
      icon: {
        backgroundColor: 'transparent !important',
      },
      title: {
        fontSize: 16,
        color: '#000000',
      },
      description: {
        fontSize: 14,
        color: '#3c3c3c',
      },
      closeButton: {
        flexShrink: 0,
        svg: {
          width: 24,
          height: 24,
        },
      },
    },
  },
  Tabs: {
    styles: {
      tabsList: {
        margin: '40px auto',
        display: 'flex',
        gap: 15,
        '@media (max-width: 768px)': {
          margin: '30px auto 10px',
          gap: 5,
        },
      },

      tab: {
        backgroundColor: 'white',
        borderRadius: 6,
        height: 80,
        fontSize: 20,
        color: '#767676',
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.16)',
        border: `3px solid white`,
        maxWidth: 274,
        width: '100%',
        cursor: 'pointer',
        '&[data-active="true"]': {
          color: '#43749A',
          borderColor: '#43749A',
          fontWeight: 'bold',
        },
        '@media (max-width: 768px)': {
          fontSize: 12,
          height: 38,
          borderWidth: 1,
          borderRadius: 4,
          padding: '4px 2px 2px 2px',
        },
      },

      tabLabel: {
        whiteSpace: 'nowrap',
      },

      panel: {
        h4: {
          fontSize: 24,
          fontWeight: 'bold',
          color: 'white',
          backgroundColor: '#43749A',
          padding: '12px 30px',
          height: 60,
          marginBottom: 20,
          '@media (max-width: 768px)': {
            fontSize: 18,
            height: 40,
            padding: '5px 10px',
            marginBottom: 15,
          },
        },
      },
    },
  },
  Badge: {
    styles: {
      root: {
        '&[data-tag="new"]': {
          fontSize: 12,
          backgroundColor: '#41850a',
          color: 'white',
          textTransform: 'none',
          width: 38,
          height: 20,
          padding: 0,
          '&[data-size="lg"]': {
            '@media (min-width: 768px)': {
              fontSize: 20,
              width: 64,
              height: 28,
              marginRight: 8,
            },
          },
        },
      },
      inner: {
        '[data-tag="new"] &': {
          color: 'white !important',
          fontWeight: 'bold',
        },
      },
    },
  },
};

export default components;
