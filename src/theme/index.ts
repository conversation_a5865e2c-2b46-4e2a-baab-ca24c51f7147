import type {
  DefaultMantineColor,
  MantineThemeOverride,
  Tuple,
} from '@mantine/core';
import { DEFAULT_THEME } from '@mantine/core';

import components from './components';

const genColorArray = (color: string) =>
  [
    color,
    color,
    color,
    color,
    color,
    color,
    color,
    color,
    color,
    color,
  ] as <PERSON><PERSON><string, 10>;

const extra = {
  queenBlue: genColorArray('#43749A'),
  queenBlueHover: genColorArray('#5682A4'),
  queenBlueActive: genColorArray('#225277'),
  marigold: genColorArray('#E8A62D'),
  marigoldHover: genColorArray('#FBBC12'),
  marigoldActive: genColorArray('#E39300'),
  blackOlive: genColorArray('#3C3C3C'),
  nickel: genColorArray('#727272'),
  silver: genColorArray('#C7C7C7'),
  quickSilver: genColorArray('#a6a6a6'),
  ghostWhite: genColorArray('#F5F9FB'),
  platinum: genColorArray('#e3e3e3'),
  darkLiver: genColorArray('#4d4d4d'),
  water: genColorArray('#D6EDFF'),
  red: genColorArray('#d00808'),
  tomato: genColorArray('#e82d2d'),
  lightRed: genColorArray('#f3a7a7'),
  grey: genColorArray('#6b6b6b'),
  darkGrey: genColorArray('#f2f2f2'),
  darkBlue: genColorArray('#225277'),
  purple: genColorArray('#65478c'),
  lightPurple: genColorArray('#f5f2ff'),
  sonicSilver: genColorArray('#767676'),
  avocado: genColorArray('#41850a'),
  richBlack: genColorArray('#070203'),
  mutedBlue: genColorArray('#386a91'),
  philippine: genColorArray('#B2B2B2'),
  floralWhite: genColorArray('#FFFAEF'),
  gainsboro: genColorArray('#DDDDDD'),
  spanishGray: genColorArray('#9F9C98'),
  cultured: genColorArray('#F8F8F8'),
  antiFlashWhite: genColorArray('#F0F6F9'),
  azureishWhite: genColorArray('#D9E3EB'),
  alabaster: genColorArray('#ECF3E7'),
  maximumRed: genColorArray('#DB1E0E'),
  nyanza: genColorArray('#EBFFD6'),
  papayaWhip: genColorArray('#FFF2D6'),
  steelBlue: genColorArray('#327EB9'),
} as const;

type Colors = keyof typeof extra;

declare module '@mantine/core' {
  export interface MantineThemeColorsOverride {
    colors: Record<Colors | DefaultMantineColor, Tuple<string, 10>>;
  }
}
// *NOTE: Mantine default theme
const theme: MantineThemeOverride = {
  ...DEFAULT_THEME,
  colors: extra,
  white: '#FFFFFF',
  black: '#000000',
  other: {
    headerHeight: '100px',
    headerHeightMobile: '60px',
    footerHeight: '540px',
    footerHeightMobile: '923px',
    simpleFooterHeight: '186px',
    simpleFooterHeightMobile: '140px',
  },
  primaryColor: 'queenBlue',
  datesLocale: 'ja',
  breakpoints: {
    xs: '39em', // 480px
    sm: '48em', // 768px
    md: '62em', // 992px
    lg: '80em', // 1280px
    xl: '96em', // 1536px
  },
  fontSizes: {
    xs: '10px',
    sm: '12px',
    md: '14px',
    lg: '16px',
    xl: '20px',
  },
  radius: {
    xs: '2px',
    sm: '4px',
    md: '8px',
    lg: '16px',
    xl: '32px',
  },
  spacing: {
    xs: '10px',
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  },
  primaryShade: 6,
  activeStyles: { transform: 'scale(1)' },
  fontFamily:
    'Noto Sans JP, メイリオ, Meiryo, Hiragino Sans, ヒラギノ角ゴシック, ヒラギノ角ゴ Pro W3, Hiragino Kaku Gothic Pro, ＭＳ Ｐゴシック, Helvetica Neue, Helvetica, Arial, sans-serif',
  headings: {
    fontFamily:
      'Noto Sans JP, メイリオ, Meiryo, Hiragino Sans, ヒラギノ角ゴシック, ヒラギノ角ゴ Pro W3, Hiragino Kaku Gothic Pro, ＭＳ Ｐゴシック, Helvetica Neue, Helvetica, Arial, sans-serif',
    fontWeight: 700,
    sizes: {
      h1: { fontSize: '40px', lineHeight: 1.3, fontWeight: undefined },
      h2: { fontSize: '26px', lineHeight: 1.35, fontWeight: undefined },
      h3: { fontSize: '22px', lineHeight: 1.4, fontWeight: undefined },
      h4: { fontSize: '18px', lineHeight: 1.45, fontWeight: undefined },
      h5: { fontSize: '16px', lineHeight: 1.5, fontWeight: undefined },
      h6: { fontSize: '14px', lineHeight: 1.5, fontWeight: undefined },
    },
  },
  components,
  globalStyles: (t) => ({
    html: {
      // scrollBehavior: 'smooth',
    },
    a: {
      textDecoration: 'none',
    },
    '@keyframes redDotPulse': {
      '0%': {
        backgroundColor: t.colors.lightRed,
      },
      '50%': {
        backgroundColor: t.colors.red,
      },
      '100%': {
        backgroundColor: t.colors.lightRed,
      },
    },
    '.red-dot': {
      display: 'block',
      width: 12,
      height: 12,
      borderRadius: '50%',
      backgroundColor: t.colors.red,
      animation: 'redDotPulse 3s infinite',
      '@media (max-width: 768px)': {
        width: 10,
        height: 10,
      },
    },
    '.blue-dot': {
      display: 'block',
      width: 10,
      height: 10,
      borderRadius: '50%',
      backgroundColor: t.colors.queenBlue[6],
    },
  }),
};

export default theme;
