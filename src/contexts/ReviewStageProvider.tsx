import React, { createContext, useContext, useReducer, type ReactNode } from 'react';
import type { IReviewList, ITherapistItem } from 'models/therapist';

// Types
interface ReviewStageState {
  currentStage: 'loading' | 'overview' | 'detailed' | 'filtered';
  therapistDetail?: ITherapistItem;
  reviewData?: IReviewList;
  filters: {
    rating?: number[];
    sortBy: 'newest' | 'oldest' | 'highest' | 'lowest';
    page: number;
  };
  cache: Map<string, any>;
  isLoading: boolean;
  error?: string;
}

type ReviewStageAction =
  | { type: 'SET_STAGE'; payload: ReviewStageState['currentStage'] }
  | { type: 'SET_THERAPIST_DETAIL'; payload: ITherapistItem }
  | { type: 'SET_REVIEW_DATA'; payload: IReviewList }
  | { type: 'SET_FILTERS'; payload: Partial<ReviewStageState['filters']> }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CACHE_DATA'; payload: { key: string; data: any } }
  | { type: 'CLEAR_CACHE' }
  | { type: 'RESET_STATE' };

interface ReviewStageContextType {
  state: ReviewStageState;
  dispatch: React.Dispatch<ReviewStageAction>;
  // Convenience methods
  setStage: (stage: ReviewStageState['currentStage']) => void;
  setTherapistDetail: (detail: ITherapistItem) => void;
  setReviewData: (data: IReviewList) => void;
  updateFilters: (filters: Partial<ReviewStageState['filters']>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string) => void;
  cacheData: (key: string, data: any) => void;
  getCachedData: (key: string) => any;
  clearCache: () => void;
  resetState: () => void;
}

// Initial State
const initialState: ReviewStageState = {
  currentStage: 'loading',
  filters: {
    sortBy: 'newest',
    page: 1,
  },
  cache: new Map(),
  isLoading: false,
};

// Reducer
const reviewStageReducer = (
  state: ReviewStageState,
  action: ReviewStageAction,
): ReviewStageState => {
  switch (action.type) {
    case 'SET_STAGE':
      return { ...state, currentStage: action.payload };
    
    case 'SET_THERAPIST_DETAIL':
      return { ...state, therapistDetail: action.payload };
    
    case 'SET_REVIEW_DATA':
      return { ...state, reviewData: action.payload };
    
    case 'SET_FILTERS':
      return { 
        ...state, 
        filters: { ...state.filters, ...action.payload } 
      };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'CACHE_DATA':
      const newCache = new Map(state.cache);
      newCache.set(action.payload.key, action.payload.data);
      return { ...state, cache: newCache };
    
    case 'CLEAR_CACHE':
      return { ...state, cache: new Map() };
    
    case 'RESET_STATE':
      return { ...initialState };
    
    default:
      return state;
  }
};

// Context
const ReviewStageContext = createContext<ReviewStageContextType | undefined>(undefined);

// Provider Component
export const ReviewStageProvider: React.FC<{ children: ReactNode }> = ({ 
  children 
}) => {
  const [state, dispatch] = useReducer(reviewStageReducer, initialState);

  // Convenience methods
  const setStage = (stage: ReviewStageState['currentStage']) => {
    dispatch({ type: 'SET_STAGE', payload: stage });
  };

  const setTherapistDetail = (detail: ITherapistItem) => {
    dispatch({ type: 'SET_THERAPIST_DETAIL', payload: detail });
  };

  const setReviewData = (data: IReviewList) => {
    dispatch({ type: 'SET_REVIEW_DATA', payload: data });
  };

  const updateFilters = (filters: Partial<ReviewStageState['filters']>) => {
    dispatch({ type: 'SET_FILTERS', payload: filters });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setError = (error: string) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const cacheData = (key: string, data: any) => {
    dispatch({ type: 'CACHE_DATA', payload: { key, data } });
  };

  const getCachedData = (key: string) => {
    return state.cache.get(key);
  };

  const clearCache = () => {
    dispatch({ type: 'CLEAR_CACHE' });
  };

  const resetState = () => {
    dispatch({ type: 'RESET_STATE' });
  };

  const contextValue: ReviewStageContextType = {
    state,
    dispatch,
    setStage,
    setTherapistDetail,
    setReviewData,
    updateFilters,
    setLoading,
    setError,
    cacheData,
    getCachedData,
    clearCache,
    resetState,
  };

  return (
    <ReviewStageContext.Provider value={contextValue}>
      {children}
    </ReviewStageContext.Provider>
  );
};

// Custom Hook
export const useReviewStage = (): ReviewStageContextType => {
  const context = useContext(ReviewStageContext);
  if (!context) {
    throw new Error('useReviewStage must be used within ReviewStageProvider');
  }
  return context;
};

// Selectors (for performance optimization)
export const useReviewStageSelector = <T,>(
  selector: (state: ReviewStageState) => T,
): T => {
  const { state } = useReviewStage();
  return selector(state);
};

// Common selectors
export const selectCurrentStage = (state: ReviewStageState) => state.currentStage;
export const selectTherapistDetail = (state: ReviewStageState) => state.therapistDetail;
export const selectReviewData = (state: ReviewStageState) => state.reviewData;
export const selectFilters = (state: ReviewStageState) => state.filters;
export const selectIsLoading = (state: ReviewStageState) => state.isLoading;
export const selectError = (state: ReviewStageState) => state.error;
