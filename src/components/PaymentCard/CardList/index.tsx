import IconRemove from '@icons/icon-remove.svg';
import { ActionIcon, Box, Button, Flex, Text } from '@mantine/core';
import { isEmpty, partition } from 'lodash';
import type { ICardDetail } from 'models/payment';
import React, { useState } from 'react';

import CardItem from './CardItem';
import CardSkeleton from './CardSkeleton';
import { sx } from './styles';

interface CardListProps {
  list: ICardDetail[];
  loading?: boolean;
  onRemoveCard?: (id: string) => void;
  onSelectCard?: (id: string) => void; // Function select card to used on booking, etc...
  onSelectDefault?: (id: string) => void; // Function select default card on future booking, etc...
  defaultCard?: string;
  activeCard?: string;
  isDeletingCard?: boolean;
  isSettingDefaultCard?: boolean;
  onCancel?: () => void;
  cancelTxt?: string;
  onAdd?: () => void;
  addTxt?: string;
}

const CardList: React.FC<CardListProps> = ({
  list,
  loading = false,
  onRemoveCard,
  onSelectCard,
  onSelectDefault,
  defaultCard,
  activeCard,
  isDeletingCard = false,
  isSettingDefaultCard = false,
  onCancel,
  cancelTxt,
  onAdd,
  addTxt,
}) => {
  const [selectedCard, setSelectedCard] = useState('');
  // Split card list into 2 columns (default card and the others)
  const cardSplit = partition(list, (detail) => detail.id === defaultCard);

  const handleSelectCard = (id: string) => {
    setSelectedCard(id !== selectedCard ? id : '');
  };

  const renderList = () => {
    if (loading) {
      return (
        <Flex justify="space-between" sx={sx.cardList}>
          <Flex className="card-column" direction="column">
            <CardSkeleton />
          </Flex>
          <Flex className="card-column" direction="column">
            <CardSkeleton />
          </Flex>
        </Flex>
      );
    }
    if (!isEmpty(list)) {
      return (
        <Flex
          data-payment={!onSelectDefault}
          justify="space-between"
          sx={sx.cardList}
        >
          <Flex className="card-column" direction="column">
            {cardSplit[0].map((card) => (
              <CardItem
                card={card}
                isActive={card.id === activeCard}
                isDefaultCard
                key={card.id}
                onSelect={onSelectCard}
              />
            ))}
          </Flex>
          <Flex className="card-column" direction="column" gap={20}>
            {cardSplit[1].map((card) => (
              <Box key={card.id} pos="relative" w="100%">
                {onRemoveCard && (
                  <ActionIcon
                    loading={isDeletingCard}
                    onClick={() => onRemoveCard(card.id)}
                    sx={sx.removeCard}
                    variant="transparent"
                  >
                    <IconRemove />
                  </ActionIcon>
                )}
                <CardItem
                  card={card}
                  isActive={card.id === activeCard}
                  isSelected={selectedCard === card.id}
                  onSelect={onSelectCard || handleSelectCard}
                />
              </Box>
            ))}
          </Flex>
        </Flex>
      );
    }
    return (
      <Text color="queenBlue" size={28} sx={sx.emptyTxt} weight="bold">
        ご登録中のカードはありません。
      </Text>
    );
  };

  return (
    <Flex direction="column">
      <Text color="blackOlive" size={30} sx={sx.title} weight="bold">
        お支払い方法
      </Text>
      <Text color="blackOlive" size={16} sx={sx.subTitle}>
        ご登録中のクレジットカード
      </Text>
      {renderList()}
      {!isEmpty(cardSplit[1]) && onSelectDefault && (
        <Button
          color="marigold"
          disabled={!selectedCard}
          loading={isSettingDefaultCard}
          onClick={() => onSelectDefault(selectedCard)}
          size="lg"
          sx={sx.setCardBtn}
        >
          表示カードを変更する
        </Button>
      )}
      <Flex gap={20} justify="space-between" sx={sx.btnGroup}>
        <Button
          className="cancel-btn"
          color="grey"
          onClick={onCancel}
          size="lg"
          variant="outline"
        >
          {cancelTxt || 'マイページへ戻る'}
        </Button>
        <Button disabled={list.length > 4} onClick={onAdd} size="lg">
          {addTxt || 'カードを追加する'}
        </Button>
      </Flex>
    </Flex>
  );
};

export default CardList;
