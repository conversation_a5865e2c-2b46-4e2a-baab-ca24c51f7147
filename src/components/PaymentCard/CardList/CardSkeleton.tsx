import { Box, Flex, Skeleton } from '@mantine/core';
import React from 'react';

import { sx } from './styles';

const CardSkeleton = () => {
  return (
    <Box sx={{ ...sx.cardItem, background: '#ffffff', gap: 20 }}>
      <Skeleton h={40} w={57} />
      <Flex direction="column" gap={12} w="100%">
        <Skeleton h={14} w="90%" />
        <Skeleton h={14} w="75%" />
      </Flex>
    </Box>
  );
};

export default CardSkeleton;
