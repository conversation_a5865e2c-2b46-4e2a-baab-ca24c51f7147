import { Button, Flex, Text } from '@mantine/core';
import type { ICardDetail } from 'models/payment';
import React from 'react';
import type { ValidCardNiceType } from 'utils/constants';
import { CardIcon } from 'utils/constants';
import dayjs from 'utils/dayjs';

import { sx } from './styles';

const CardItem: React.FC<{
  card: ICardDetail;
  isDefaultCard?: boolean;
  isActive?: boolean;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
}> = ({
  card,
  onSelect,
  isSelected = false,
  isDefaultCard = false,
  isActive = false,
}) => {
  const validDate = dayjs(`${card.exp_year}/${card.exp_month}`, 'YYYY/M');
  const isExpire = validDate.isBefore(dayjs(), 'month');
  return (
    <Button
      className="card-item"
      data-active={isActive}
      data-default={isDefaultCard}
      data-expire={isExpire}
      data-selected={isSelected}
      onClick={() => {
        if (onSelect) {
          onSelect(card.id);
        }
      }}
      sx={sx.cardItem}
      variant="white"
    >
      {!isExpire && isDefaultCard && (
        <span className="default-tag">表示中</span>
      )}
      {isExpire && <span className="default-tag expire-tag">EXPIRE</span>}
      {CardIcon[card.brand as ValidCardNiceType]}
      <Flex direction="column">
        <Text>
          ●●●●&nbsp;&nbsp;●●●●&nbsp;&nbsp;●●●●&nbsp;&nbsp;{card.last4}
        </Text>
        <Text>VALID&nbsp;{validDate.format('MM/YY')}</Text>
      </Flex>
    </Button>
  );
};

export default CardItem;
