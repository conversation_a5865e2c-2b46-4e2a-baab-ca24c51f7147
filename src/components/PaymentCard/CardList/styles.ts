import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  title: {
    marginBottom: 16,
    '@media (max-width: 768px)': {
      marginBottom: 12,
      fontSize: 22,
    },
  },
  subTitle: {
    '@media (max-width: 768px)': {
      fontSize: 14,
    },
  },
  emptyTxt: {
    marginTop: 48,
    marginBottom: 66,
    '@media (max-width: 768px)': {
      fontSize: 18,
      textAlign: 'center',
      marginTop: 30,
      marginBottom: 30,
    },
  },
  cardList: {
    marginTop: 32,
    marginBottom: 40,
    gap: 80,
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      alignItems: 'center',
      gap: 20,
      paddingLeft: 25,
    },
    '.card-column': {
      maxWidth: 360,
      width: '100%',
      '@media (max-width: 768px)': {
        maxWidth: 290,
      },
    },
    '&[data-payment=true]': {
      flexDirection: 'column',
      gap: 20,
    },
  },
  removeCard: {
    position: 'absolute',
    top: 'calc(50% - 13px)',
    left: -38,
    svg: {
      width: 26,
      height: 26,
    },
    '&:hover > svg': {
      opacity: 0.8,
    },
    '@media (max-width: 768px)': {
      top: 'calc(50% - 14px)',
      svg: {
        width: 15,
        height: 15,
      },
    },
  },
  cardItem: {
    width: '100%',
    height: 110,
    background: 'url(/images/card-bg.webp)',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    borderRadius: 6,
    display: 'flex',
    alignItems: 'center',
    padding: '0',
    border: '2px solid transparent',
    transition: 'all 100ms linear',
    cursor: 'pointer',
    position: 'relative',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    color: '#3c3c3c',
    fontSize: 15,
    paddingLeft: 20,
    svg: {
      marginRight: 20,
    },
    '@media (max-width: 768px)': {
      maxWidth: 290,
      fontSize: 12,
      paddingLeft: 15,
      height: 72,
      svg: {
        width: 32,
        height: 32,
        marginRight: 10,
      },
    },
    '&[data-selected=true], &[data-active=true]': {
      borderColor: '#E8A62D',
    },
    '&[data-expire=true]': {
      pointerEvents: 'none',
    },
    '.default-tag': {
      position: 'absolute',
      top: -2,
      right: -2,
      fontSize: 14,
      backgroundColor: '#E8A62D',
      color: '#ffffff',
      borderTopRightRadius: 6,
      borderBottomLeftRadius: 6,
      padding: '4px 14px',
      '@media (max-width: 768px)': {
        padding: '4px 9px',
        fontSize: 12,
      },
      '&.expire-tag': {
        backgroundColor: '#909090',
      },
    },
    '.remove-btn': {
      position: 'absolute',
      width: 26,
      height: 26,
      left: -35,
      top: '50%',
      zIndex: 1,
      svg: {
        width: 26,
        height: 26,
      },
    },
  },
  setCardBtn: {
    maxWidth: 300,
    width: '100%',
    margin: '0 0 30px auto',
    '@media (max-width: 768px)': {
      margin: '0 auto 50px auto',
      maxWidth: 180,
      fontSize: 14,
    },
  },
  btnGroup: {
    'button, a': {
      maxWidth: 300,
      width: '100%',
      padding: 0,
      '@media (max-width: 768px)': {
        maxWidth: 148,
      },
      '&.cancel-btn': {
        '@media (max-width: 768px)': {
          textAlign: 'center',
          span: {
            whiteSpace: 'normal',
          },
        },
      },
    },
  },
};
