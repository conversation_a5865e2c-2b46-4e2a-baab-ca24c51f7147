import valid from 'card-validator';
import dayjs from 'utils/dayjs';
import type { InferType } from 'yup';
import { object, string } from 'yup';

export const schema = object({
  cardno: string()
    .required('カード番号をお確めの上、入力してください。')
    .trim()
    .min(8, 'カード番号をお確めの上、入力してください。')
    .test('card-length', 'カード番号が無効です', (value) => {
      const validator = valid.number(value);
      switch (validator?.card?.niceType) {
        case 'Diners Club':
          return value.length <= 14;
        case 'American Express':
          return value.length <= 15;
        default:
          return value.length <= 16;
      }
    }),
  expire: string()
    .required()
    .test(
      'isValidCard',
      '有効期限をお確めの上、入力してください。',
      (value) => {
        return valid.expirationDate(dayjs(value).format('MM/YY')).isValid;
      },
    ),
  holdername: string().trim().required('カード名義を入力してください。'),
  securitycode: string()
    .trim()
    .required()
    .when('cardno', {
      is: (value: string) => {
        const validator = valid.number(value);
        return validator?.card?.niceType === 'American Express';
      },
      then: (context) =>
        context.length(4, 'セキュリティコードをお確めの上、入力してください。'),
      otherwise: (context) =>
        context.length(3, 'セキュリティコードをお確めの上、入力してください。'),
    }),
});

export type AddCardFormValues = InferType<typeof schema>;
