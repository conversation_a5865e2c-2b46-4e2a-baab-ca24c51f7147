import { yupResolver } from '@hookform/resolvers/yup';
import IconChevronRight from '@icons/icon-chevron-up.svg';
import IconDropdown from '@icons/icon-dropdown.svg';
import type { Sx } from '@mantine/core';
import { Box, Collapse, Flex, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import valid from 'card-validator';
import { MonthPickerField, TextField } from 'components/Form';
import { merge } from 'lodash';
import type { GetTokenResponse } from 'models/payment';
import Script from 'next/script';
import React, { useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import type { ValidCardNiceType } from 'utils/constants';
import { CardIcon, GMO_TOKEN_ERRORS } from 'utils/constants';
import dayjs from 'utils/dayjs';
import notification from 'utils/notification';

import type { AddCardFormValues } from './schema';
import { schema } from './schema';
import { sx } from './styles';

const AddCardForm: React.FC<{
  initialValues?: AddCardFormValues;
  onSubmit: (token: string) => void;
  headerContent: React.ReactNode;
  footerContent: (loading: boolean) => React.ReactNode;
  customSx?: Sx;
}> = ({
  initialValues,
  onSubmit,
  headerContent,
  footerContent,
  customSx = {},
}) => {
  const [opened, { toggle }] = useDisclosure(false);
  const [gettingToken, setGettingToken] = useState<boolean>(false);
  const { control, handleSubmit, watch } = useForm<AddCardFormValues>({
    defaultValues: initialValues,
    mode: 'onBlur',
    resolver: yupResolver(schema),
  });

  const formValues = watch();
  const cardType = valid.number(formValues.cardno);

  const handleSubmitCard: SubmitHandler<AddCardFormValues> = (values) => {
    setGettingToken(true);
    (window as any).Multipayment.init(process.env.NEXT_PUBLIC_GMO_SHOP_ID);
    (window as any).Multipayment.getToken(
      {
        ...values,
        cardno: values.cardno.trim(),
        expire: dayjs(values.expire.trim()).format('YYMM'),
      },
      (response: GetTokenResponse) => {
        setGettingToken(false);
        if (response.resultCode === '000' && response.tokenObject?.token) {
          onSubmit(response.tokenObject.token);
        } else {
          notification.show({
            type: 'error',
            message: GMO_TOKEN_ERRORS[response.resultCode],
          });
        }
      },
    );
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(handleSubmitCard)}
      sx={merge({}, sx.addCardFormWrapper, customSx)}
    >
      <Script
        src={process.env.NEXT_PUBLIC_GMO_BASE_URL}
        type="text/javascript"
      />
      {headerContent}
      <Text
        className="credit-card-note"
        color="queenBlue.6"
        data-active={opened}
        onClick={toggle}
        size={16}
        weight="bold"
      >
        デビットカードをご利用される方へ <IconChevronRight />
      </Text>
      <Collapse className="note-content" in={opened}>
        <Text color="blackOlive" mt={20} size={14}>
          ・デビットカードでのご利用は、予約リクエストした時点で、お客様の口座から一時的に決済金額が差し引かれます。
          <br />
          ・マッチング不成立やキャンセルをされた場合は、一定期間を経過すると銀行口座に差し引かれた金額が戻ります。
          <br />
          ・引き落とされた金額が口座に戻るタイミングはカード会社によって異なります。
          <br />
          ・運営側で返金日をお調べすることは出来ない為、お客様からカード会社へ直接お問い合わせいただく様お願い申し上げます。
        </Text>
      </Collapse>
      <Flex direction="column" gap={32} sx={sx.formContent}>
        <TextField
          control={control}
          format="################"
          icon={CardIcon[cardType.card?.niceType as ValidCardNiceType]}
          label="カード番号"
          name="cardno"
          placeholder="123456789012"
          sx={{
            svg: {
              width: 24,
              height: 24,
            },
          }}
        />
        <TextField
          control={control}
          label="カード名義"
          maxLength={30}
          name="holdername"
          placeholder="TARO YAMADA"
        />
        <Flex gap={20} sx={sx.formRow}>
          <MonthPickerField
            control={control}
            defaultLevel="year"
            label="有効期限"
            maxLevel="year"
            minDate={dayjs().toDate()}
            name="expire"
            placeholder="MM/YY"
            rightSection={<IconDropdown />}
            size="lg"
            sx={{
              '.mantine-Input-rightSection': {
                pointerEvents: 'none',
              },
            }}
            valueFormat="MM/YY"
          />
          <TextField
            control={control}
            format="####"
            label="セキュリティコード"
            name="securitycode"
            placeholder="000"
          />
        </Flex>
      </Flex>
      <Text color="blackOlive" size={20} sx={sx.cardTypeTitle} weight="bold">
        ご利用いただけるカード会社
      </Text>
      <Flex sx={sx.cardTypeWrapper}>
        {['Visa', 'MasterCard', 'JCB', 'Diners Club', 'American Express'].map(
          (key) => (
            <Box className="card-type" key={key}>
              {CardIcon[key as ValidCardNiceType]}
            </Box>
          ),
        )}
      </Flex>
      <Text
        color="blackOlive"
        size={16}
        sx={{
          '@media (max-width: 768px)': {
            fontSize: 12,
          },
        }}
      >
        本サービスのクレジットカード決済は、GMOペイメントゲートウェイ株式会社のオンライン決済サービスを利用していますので、Hoguguにクレジットカ
        ード情報を保持させることなく、決済を完了させることができます。
      </Text>
      {/* Footer content will need a button submit type to submit the form */}
      {footerContent(gettingToken)}
    </Box>
  );
};

export default AddCardForm;
