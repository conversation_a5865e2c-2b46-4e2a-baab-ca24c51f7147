import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  addCardFormWrapper: {
    display: 'flex',
    flexDirection: 'column',
    '.credit-card-note': {
      display: 'flex',
      alignItems: 'center',
      gap: 14,
      cursor: 'pointer',
      textDecoration: 'underline',
      textUnderlineOffset: 5,
      svg: {
        width: 14,
        height: 14,
      },
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
      '&[data-active=true]': {
        svg: {
          transform: 'rotate(-180deg)',
        },
      },
    },
  },
  formContent: {
    marginTop: 24,
  },
  formRow: {
    '& > *': { flex: '0 1 50%' },
    '@media (max-width: 768px)': {
      gap: 10,
    },
  },
  cardTypeTitle: {
    marginTop: 32,
    '@media (max-width: 768px)': {
      fontSize: 16,
      marginTop: 24,
    },
  },
  cardTypeWrapper: {
    gap: 12,
    padding: '17px 0 25px',
    '@media (max-width: 768px)': {
      padding: '12px 0 18px',
      gap: 4,
    },
    '.card-type': {
      width: 97,
      height: 65,
      backgroundColor: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '5px',
      boxShadow: '0 2px 3px 0 rgba(0, 0, 0, 0.08)',
      '@media (max-width: 768px)': {
        width: 60,
        height: 40,
        svg: {
          width: 32,
          height: 32,
        },
      },
    },
  },
};
