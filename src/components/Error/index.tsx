import { Button, Stack, Text } from '@mantine/core';
import Link from 'next/link';

import { sx } from './styles';

const errorContent: Record<number, { title: string; description: string }> = {
  404: {
    title: 'ページが見つかりません',
    description:
      'アクセスいただいたURLが見つかりません。\n以下のホームから再度アクセスしてください。',
  },
  403: {
    title: 'アクセスが集中しています',
    description: '時間をおいてから再度アクセスしてください。',
  },
  503: {
    title: 'ページが見つかりません',
    description: 'メンテナンス中のため一時的に\nご利用いただけません。',
  },
};

const ErrorComponent: React.FC<{ statusCode: number }> = ({ statusCode }) => {
  const content = errorContent[statusCode];
  return (
    <Stack align="center" justify="center" spacing={0} sx={sx.errorWrapper}>
      <Text sx={sx.code}>{statusCode}</Text>
      <Text sx={sx.error}>Error</Text>
      <Text sx={sx.title}>{content?.title}</Text>
      <Text sx={sx.description}>{content?.description}</Text>
      <Link href={process.env.NEXT_PUBLIC_LP_DOMAIN || '/'}>
        <Button sx={sx.homeBtn}>ホームへ戻る</Button>
      </Link>
    </Stack>
  );
};

export default ErrorComponent;
