import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  errorWrapper: {
    minHeight: 'inherit',
    padding: '80px 0 60px',
    backgroundColor: '#ffffff',
    '@media (max-width: 768px)': {
      padding: '40px 20px 52px',
    },
  },
  code: {
    fontSize: 120,
    fontWeight: 'bold',
    color: '#d2dde7',
    lineHeight: 1,
    '@media (max-width: 768px)': {
      fontSize: 80,
    },
  },
  error: {
    lineHeight: 1,
    color: '#d2dde7',
    fontSize: 25,
    fontWeight: 'bold',
  },
  title: {
    marginTop: 28,
    color: '#3c3c3c',
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 1,
    '@media (max-width: 768px)': {
      marginTop: 16,
      fontSize: 20,
    },
  },
  description: {
    textAlign: 'center',
    lineHeight: 1.4,
    fontSize: 16,
    color: '#3c3c3c',
    marginTop: 10,
    '@media (max-width: 768px)': {
      marginTop: 14,
    },
  },
  homeBtn: {
    marginTop: 30,
    width: 180,
    height: 50,
    fontSize: 16,
  },
};
