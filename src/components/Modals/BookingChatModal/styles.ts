import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  therapistName: {
    height: 100,
    display: 'flex',
    alignItems: 'center',
    padding: '0 100px 0 50px',
    flexShrink: 0,
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    '@media (max-width: 768px)': {
      height: 60,
      padding: '0 50px 0 20px',
      fontSize: 18,
    },
  },
  chatContainer: {
    overflowY: 'auto',
    overflowX: 'hidden',
    flex: '1 1 100%',
    display: 'flex',
    flexDirection: 'column-reverse',
    padding: '60px 0',
    '@media (max-width: 768px)': {
      padding: '45px 0',
    },
    '& > *': {
      flexShrink: 0,
    },
  },
  chatPolicy: (theme) => ({
    flexShrink: 0,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '20px 50px',
    gap: 16,
    backgroundColor: theme.colors.ghostWhite[0],
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      textAlign: 'center',
      gap: 12,
      padding: '12px 20px',
    },
    '.policy-text': {
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
    '.accept-btn': {
      maxWidth: 130,
      width: '100%',
      height: 50,
      fontSize: 18,
      '@media (max-width: 768px)': {
        maxWidth: 100,
        fontSize: 12,
        height: 30,
      },
    },
  }),
  chatInput: (theme) => ({
    display: 'flex',
    flexShrink: 0,
    backgroundColor: theme.colors.queenBlue[6],
    alignItems: 'center',
    padding: '20px 50px',
    gap: 20,
    '@media (max-width: 768px)': {
      padding: '10px 20px',
    },
    '.message-input': {
      flex: '1 1 100%',
      textarea: {
        fontSize: 16,
        padding: '11.5px 20px',
        '@media (max-width: 768px)': {
          padding: '9px 8px',
          fontSize: 14,
        },
      },
    },
    '.send-btn': {
      padding: 0,
      flexShrink: 0,
      width: 50,
      height: 50,
      borderRadius: '50%',
      '@media (max-width: 768px)': {
        width: 46,
        height: 46,
      },
    },
  }),
};

export const styles: Record<string, any> = {
  bookingChatModalWrapper: (theme: MantineTheme) => ({
    inner: {
      padding: '0px !important',
    },
    content: {
      height: theme.other?.viewHeight,
      borderRadius: 0,
      maxHeight: `${theme.other?.viewHeight}px !important`,
    },
    header: {
      top: 25,
      right: 50,
      '@media (max-width: 768px)': {
        right: 20,
      },
    },
    body: {
      display: 'flex',
      flexDirection: 'column',
      background: 'white',
      height: '100%',
    },
  }),
};
