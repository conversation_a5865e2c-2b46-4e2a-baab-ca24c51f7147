import IconSendMessage from '@icons/icon-send-message.svg';
import type { ModalProps } from '@mantine/core';
import {
  Box,
  Button,
  Loader,
  Modal,
  Text,
  Textarea,
  Title,
} from '@mantine/core';
import {
  useIntersection,
  useLocalStorage,
  useMediaQuery,
  useScrollLock,
} from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import { ChatBubble } from 'components/Messages';
import { useFetchData, useMessage } from 'hooks';
import type { IBookingDetail } from 'models/booking';
import { bookingQuery } from 'models/booking';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { LOCAL_STORAGE_KEY, REGEX } from 'utils/constants';
import { checkChattingExpired } from 'utils/helpers';

import ChatSkeleton from './ChatSkeleton';
import { styles, sx } from './styles';

interface BookingChatModalProps extends ModalProps {
  bookingId: string;
}

const BookingChatModal: React.FC<BookingChatModalProps> = ({
  bookingId,
  opened,
  onClose,
  ...props
}) => {
  const [text, setText] = useState('');
  const [bookingAccepted, setBookingAccepted] = useLocalStorage<{
    ids: string[];
  }>({
    key: LOCAL_STORAGE_KEY.BOOKING_POLICY_CHECKED,
  });
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const { data: bookingDetail } = useFetchData<IBookingDetail>({
    ...bookingQuery.getBookingDetail({ bookingId }),
    enabled: false,
  });

  const { ref: loadingRef, entry } = useIntersection({
    threshold: 1,
  });

  const isChattingExpired = checkChattingExpired(bookingDetail?.statusHistory);

  const {
    messageIds,
    messages,
    hasMoreMessages,
    isFirstFetch,
    channelDetail,
    handleCreateChannel,
    handleSendMessage,
    handleReadThread,
  } = useMessage(
    !isChattingExpired
      ? {
          bookingId,
          bookingDate: bookingDetail?.dateBooking,
          receiverId: bookingDetail?.therapist._id,
          receiverName:
            bookingDetail?.therapist.nickName ||
            bookingDetail?.therapist.fullName,
          senderId: bookingDetail?.customer.id,
          senderName: bookingDetail?.customer.name,
          inView: entry?.isIntersecting || false,
        }
      : {},
  );
  useScrollLock(opened);

  const latestMessageId = messageIds[0] || null;
  const messageListRef = useRef<HTMLDivElement>(null);

  const messageList = useMemo(
    () =>
      messageIds.map((id, index) => {
        let isShowAvatar = false;
        let isShowDate = false;
        const messageData = messages[id];
        const nextMessageData = messageIds[index - 1]
          ? messages[messageIds[index - 1] || '']
          : null;
        const prevMessageData = messageIds[index + 1]
          ? messages[messageIds[index + 1] || '']
          : null;

        if (
          !nextMessageData ||
          (nextMessageData.senderID &&
            messageData?.senderID !== nextMessageData.senderID)
        ) {
          isShowDate = true;
        }

        if (
          !prevMessageData ||
          (prevMessageData.senderID &&
            messageData?.senderID !== prevMessageData.senderID)
        ) {
          isShowAvatar = true;
        }

        return (
          <ChatBubble
            data={messageData}
            isEndGroup={isShowDate}
            isStartGroup={isShowAvatar}
            key={id}
            therapistAvatar={bookingDetail?.therapist?.avatar}
            therapistId={bookingDetail?.therapist?._id}
          />
        );
      }),
    [
      bookingDetail?.therapist?._id,
      bookingDetail?.therapist?.avatar,
      messageIds,
      messages,
    ],
  );

  useEffect(() => {
    if (latestMessageId && opened) {
      setTimeout(() => {
        if (messageListRef.current) {
          messageListRef.current.scrollTo(
            0,
            messageListRef.current.scrollHeight,
          );
        }
      }, 500);
    }
  }, [latestMessageId, opened]);

  useEffect(() => {
    // Create channel if detail empty
    if (opened && Object.keys(channelDetail).length === 0) {
      handleCreateChannel();
    }
  }, [channelDetail, handleCreateChannel, opened]);

  useEffect(() => {
    // If latest message only read by therapist handle read room
    if (opened && latestMessageId) {
      handleReadThread();
    }
  }, [handleReadThread, latestMessageId, opened]);

  useEffect(() => {
    if (opened) {
      setText('');
    }
  }, [opened]);

  useEffect(() => {
    if (channelDetail?.status === 'done') {
      onClose();
    }
  }, [channelDetail?.status, onClose]);

  const onSendingMessage = async () => {
    const bookingAcceptedIds = bookingAccepted?.ids || [];
    if (!bookingAcceptedIds.includes(bookingId)) {
      openContextModal({
        modal: 'AlertModal',
        size: 630,
        innerProps: {
          content: 'メッセージを送信するには、注意事項を承諾してください',
          hasOkBtn: true,
        },
        zIndex: 1000,
        centered: true,
      });
      return;
    }
    if (channelDetail?.status === 'done') {
      openContextModal({
        modal: 'AlertModal',
        size: 630,
        innerProps: {
          content:
            'この治療に関するメッセージを表示することはできなくなりました。',
          hasOkBtn: true,
        },
        zIndex: 1000,
        centered: true,
      });
      return;
    }
    if (text.trim()) {
      setText('');
      await handleSendMessage({
        content: text.trim(),
      });
    }
  };

  const handleOnEnter = async (event: React.KeyboardEvent) => {
    if (event.keyCode === 13 && event.shiftKey === false) {
      event.preventDefault();
      onSendingMessage();
    }
  };

  const onAcceptPolicy = () => {
    const bookingAcceptedIds = bookingAccepted?.ids || [];
    bookingAcceptedIds?.push(bookingId);
    setBookingAccepted({
      ids: bookingAcceptedIds,
    });
  };

  return (
    <Modal
      lockScroll={false}
      onClose={onClose}
      opened={opened}
      size={830}
      styles={styles.bookingChatModalWrapper}
      zIndex={202}
      {...props}
    >
      <Title lineClamp={1} order={3} size={26} sx={sx.therapistName}>
        {bookingDetail?.therapist?.nickName}
      </Title>
      {!isFirstFetch ? (
        <Box ref={messageListRef} sx={sx.chatContainer}>
          {messageList}
          {hasMoreMessages && (
            <Box ref={loadingRef} sx={{ textAlign: 'center' }}>
              <Loader size="md" />
            </Box>
          )}
        </Box>
      ) : (
        <ChatSkeleton />
      )}
      {!bookingAccepted?.ids?.includes(bookingId) && (
        <Box sx={sx.chatPolicy}>
          <Text className="policy-text" size={14} weight="bold">
            {!mobileScreen
              ? '“HOGUGU”ではセラピストとの連絡先の交換、アプリ外での\n決済を禁止しております。必要に応じ、セラピストとのチャット内容は\n運営が確認させていただく場合がございます。'
              : '“HOGUGU”ではセラピストとの連絡先の交換、\nアプリ外での決済を禁止しております。必要に応じ、\nセラピストとのチャット内容は\n運営が確認させていただく場合がございます。'}
          </Text>
          <Button
            className="accept-btn"
            color="marigold"
            onClick={onAcceptPolicy}
          >
            承諾
          </Button>
        </Box>
      )}
      <Box sx={sx.chatInput}>
        <Textarea
          autosize
          className="message-input"
          maxRows={3}
          minRows={1}
          onChange={(e) => {
            const { value } = e.target;
            setText(value.replace(REGEX.EMOJI, ''));
          }}
          onKeyDown={handleOnEnter}
          placeholder="メッセージを入力"
          value={text}
        />
        <Button
          className="send-btn"
          color="marigold"
          onClick={onSendingMessage}
          type="button"
          variant="filled"
        >
          <IconSendMessage />
        </Button>
      </Box>
    </Modal>
  );
};

export default BookingChatModal;
