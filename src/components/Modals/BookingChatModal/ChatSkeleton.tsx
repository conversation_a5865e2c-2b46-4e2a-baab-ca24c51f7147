import { Box } from '@mantine/core';
import { ChatBubble } from 'components/Messages';
import React from 'react';

import { sx } from './styles';

const ChatSkeleton = () => {
  return (
    <Box sx={sx.chatContainer}>
      <ChatBubble isEndGroup={true} isStartGroup={false} loading />
      <ChatBubble isEndGroup={false} isStartGroup={false} loading />
      <ChatBubble isEndGroup={false} isStartGroup={true} loading />
      <ChatBubble
        data={{ senderType: 'therapist' }}
        isEndGroup={true}
        isStartGroup={false}
        loading
      />
      <ChatBubble
        data={{ senderType: 'therapist' }}
        isEndGroup={false}
        isStartGroup={false}
        loading
      />
      <ChatBubble
        data={{ senderType: 'therapist' }}
        isEndGroup={false}
        isStartGroup={true}
        loading
      />
      <ChatBubble isEndGroup={true} isStartGroup={true} loading />
    </Box>
  );
};

export default ChatSkeleton;
