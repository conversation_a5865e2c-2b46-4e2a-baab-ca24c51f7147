import IconDropdown from '@icons/icon-dropdown.svg';
import IconMidnight from '@icons/icon-midnight.svg';
import {
  Box,
  Button,
  Container,
  Flex,
  LoadingOverlay,
  Select,
  Text,
  Title,
} from '@mantine/core';
import { Calendar } from '@mantine/dates';
import { useMediaQuery, usePrevious } from '@mantine/hooks';
import type { ContextModalProps } from '@mantine/modals';
import { get, invoke } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { MIDNIGHT_FEE, MIDNIGHT_TIMES } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

import { styles, sx } from './styles';

type DateTimePickerModalProps = {
  confirmBtnTxt?: string;
  initialData?: string;
  onConfirm: (date: string) => void;
  // Override current function if passing in props
  mapTimeOptions?: (selectedDate: Date) => Promise<
    {
      label: string;
      value: string;
      disabled?: boolean;
    }[]
  >;
};

const DateTimePickerModal = ({
  context,
  id,
  innerProps,
}: ContextModalProps<DateTimePickerModalProps>) => {
  const [datePick, setDatePick] = useState<Date>();
  const [timePick, setTimePick] = useState<string>();
  const [options, setOptions] = useState<
    { label: string; value: string; disabled?: boolean }[]
  >([]);
  const [loading, setLoading] = useState(false);
  const mobileScreen = useMediaQuery('(max-width: 768px)', true, {
    getInitialValueInEffect: false,
  });
  const previousDatePick = usePrevious(datePick);

  useEffect(() => {
    if (innerProps.initialData) {
      const date = dayjs(innerProps.initialData);
      setDatePick(date.toDate());
      setTimePick(date.format('HH:mm'));
    }
  }, [innerProps.initialData]);

  // Handle load time options
  useEffect(() => {
    async function loadOptions() {
      if (datePick === previousDatePick) return;
      let timeOptions;
      setLoading(true);
      if (innerProps.mapTimeOptions && datePick) {
        timeOptions = await innerProps.mapTimeOptions(datePick);
      } else {
        timeOptions = helpers.mapTimeOptions(datePick);
      }
      setOptions(timeOptions);
      // Check if current time select is disabled. There are 2 case that happen here
      // First: if time options getting from timeslots api, time pick will be remove if option was disabled
      // Second: if not getting from timeslots api, time pick will check valid date time
      const optionFound = timeOptions.find(
        (option) => option.value === timePick,
      );
      if (innerProps.mapTimeOptions && optionFound?.disabled) {
        setTimePick('');
      } else {
        const timeSplit = (timePick || '00:00').split(':');
        const dateTime = dayjs(datePick)
          .set('h', Number(timeSplit[0]))
          .set('m', Number(timeSplit[1]));
        const validDateTime = dayjs(
          helpers.getValidDate(dateTime.toISOString()),
        );
        setTimePick(validDateTime.format('HH:mm'));
      }
      setLoading(false);
    }
    loadOptions();
  }, [datePick, innerProps, previousDatePick, timePick]);

  const handlePickDate = (value: Date) => {
    if (timePick) {
      const timeSplit = timePick.split(':');
      const dateTime = dayjs(value)
        .set('h', Number(timeSplit[0]))
        .set('m', Number(timeSplit[1]));
      setDatePick(value);
      setTimePick(dateTime.format('HH:mm'));
    } else {
      setDatePick(value);
    }
  };

  const handlePickTime = (value: string) => {
    setTimePick(value);
  };

  const handleOnConfirm = () => {
    if (timePick && datePick) {
      const times = timePick.split(':');
      const date = dayjs(datePick)
        .set('h', Number(times[0]))
        .set('m', Number(times[1]));
      invoke(innerProps, 'onConfirm', date.toISOString());
      context.closeModal(id);
    }
  };

  const isMidnight = useMemo(() => {
    return timePick && MIDNIGHT_TIMES.includes(timePick);
  }, [timePick]);

  return (
    <Box sx={sx.modalWrapper}>
      <Container p={0} size={1140}>
        <Title order={3} size={24} sx={sx.modalTitle}>
          希望日時選択
        </Title>
        <Box sx={sx.dateTimePickerWrapper}>
          <Box sx={sx.datePickWrapper}>
            <Calendar
              excludeDate={(date) => {
                return !dayjs(date).isBetween(
                  dayjs(),
                  dayjs().add(30, 'day'),
                  'day',
                  '[]',
                );
              }}
              firstDayOfWeek={0}
              getDayProps={(date) => {
                return {
                  selected: dayjs(date).isSame(dayjs(datePick), 'day'),
                  onClick: () => handlePickDate(date),
                };
              }}
              level="month"
              locale="ja"
              monthLabelFormat="YYYY年 MM月"
              size={mobileScreen ? 'sm' : 'xl'}
              styles={styles.calendarStyles}
            />
          </Box>
          <Box sx={sx.timePickWrapper}>
            <Box sx={sx.selectFieldWrapper}>
              <Select
                data={options}
                label="開始時間"
                onChange={handlePickTime}
                rightSection={<IconDropdown />}
                styles={styles.selectStyles}
                value={timePick}
              />
              <span>〜</span>
            </Box>
          </Box>
        </Box>
        {isMidnight && (
          <Flex
            align="center"
            bg="#fffaef"
            gap={{ base: 12, sm: 16 }}
            justify="flex-start"
            mt={{ base: 20, sm: 8 }}
            mx={{ base: -24, sm: 0 }}
            px={{ base: 16, sm: 20 }}
            py={{ base: 12, sm: 16 }}
            sx={(theme) => ({
              color: theme.colors.marigold,
              svg: {
                flexShrink: 0,
              },
              [theme.fn.smallerThan('sm')]: {
                svg: {
                  width: 16,
                  height: 16,
                },
              },
            })}
          >
            <IconMidnight />
            <Text color="marigold" fw={500} fz={{ base: 12, sm: 16 }}>
              23:30〜5:30のご予約には別途深夜料金
              {helpers.numberFormat(MIDNIGHT_FEE)}円がかかります
            </Text>
          </Flex>
        )}
        <Box mt={{ base: 20, sm: 30 }} ta="center" w="100%">
          <Button
            disabled={!timePick || !datePick}
            onClick={handleOnConfirm}
            sx={sx.confirmBtn}
          >
            {get(innerProps, 'confirmBtnTxt', '次へ')}
          </Button>
        </Box>
        <LoadingOverlay
          overlayBlur={1}
          transitionDuration={500}
          visible={loading}
        />
      </Container>
    </Box>
  );
};

export default DateTimePickerModal;
