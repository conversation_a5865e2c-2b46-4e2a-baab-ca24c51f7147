import type { CSSObject } from '@emotion/react';
import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  modalWrapper: {
    padding: '78px 30px 66px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    '@media (max-width: 768px)': {
      padding: '30px 24px',
    },
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 60,
    '@media (max-width: 768px)': {
      marginBottom: 24,
    },
  },
  dateTimePickerWrapper: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flexWrap: 'nowrap',
    gap: 24,
    '@media (max-width: 768px)': {
      gap: 24,
      maxWidth: 265,
      margin: '0 auto',
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
  },
  datePickWrapper: {},
  timePickWrapper: {
    maxWidth: 200,
    flex: '1 0 200px',
    marginTop: 72,
    '@media (max-width: 768px)': {
      marginTop: 0,
      maxWidth: 'unset',
      flex: '1 1 100%',
      width: '100%',
    },
  },
  selectFieldWrapper: {
    display: 'flex',
    alignItems: 'flex-end',
    gap: 24,
    fontSize: 24,
    span: {
      marginBottom: 8,
    },
    '@media (max-width: 768px)': {
      gap: 8,
      span: {
        marginBottom: 0,
      },
    },
  },
  confirmBtn: {
    maxWidth: 240,
    width: '100%',
    height: 60,
    fontSize: 18,
    margin: '0 auto',
    '@media (max-width: 768px)': {
      height: 40,
      fontSize: 16,
      maxWidth: 140,
      display: 'inherit',
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  calendarStyles: {
    calendarHeader: {
      maxWidth: '100%',
    },
    calendarHeaderLevel: {
      color: '#3C3C3C',
      fontSize: 24,
      fontWeight: 'bold',
      position: 'relative',
      '@media (max-width: 768px)': {
        fontSize: 16,
      },
      '&:after': {
        content: '"必須"',
        marginTop: 4,
        marginLeft: 16,
        color: '#bf2020',
        fontSize: 14,
        '@media (max-width: 768px)': {
          marginTop: 0,
          marginLeft: 10,
          fontSize: 12,
        },
      },
    },
    calendarHeaderControl: {
      color: '#4F7397',
    },
    weekday: {
      color: '#3C3C3C',
    },
    day: {
      width: 60,
      height: 60,
      color: '#43749A',
      fontSize: 22,
      '@media (max-width: 768px)': {
        fontSize: 16,
        width: 36,
        height: 36,
      },
      '&:disabled': {
        color: '#a5a5a5',
        position: 'relative',
        '&:after': {
          content: '"/"',
          color: '#555555',
          position: 'absolute',
          fontSize: 40,
          top: 3,
          left: '40%',
          transform: 'rotate(30deg)',
          '@media (max-width: 768px)': {
            fontSize: 24,
          },
        },
      },
      '&[data-today=true]': {
        border: 'solid 1px #e0e0e0',
      },
      '&[data-weekend]': {
        color: '#43749A',
      },
      '&[data-outside]': {
        color: '#ced4da',
      },
      '&[data-selected]': {
        color: '#ffffff',
      },
    },
  },
  selectStyles: {
    root: {
      maxWidth: 160,
      '@media (max-width: 768px)': {
        maxWidth: 100,
      },
    },
    label: {
      fontSize: 24,
      marginBottom: 18,
      fontWeight: 'bold',
      '@media (max-width: 768px)': {
        fontSize: 14,
        marginBottom: 8,
      },
      '&:after': {
        content: '"必須"',
        marginTop: 4,
        marginLeft: 20,
        color: '#bf2020',
        fontSize: 14,
        '@media (max-width: 768px)': {
          marginTop: 0,
          marginLeft: 12,
          fontSize: 12,
        },
      },
    },
    input: {
      fontSize: 16,
      height: 50,
      '@media (max-width: 768px)': {
        height: 36,
        fontSize: 14,
      },
    },
    item: {
      fontSize: 16,
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
    },
    rightSection: {
      pointerEvents: 'none',
      '@media (max-width: 768px)': {
        svg: {
          width: 6,
          height: 6,
        },
      },
    },
  },
};
