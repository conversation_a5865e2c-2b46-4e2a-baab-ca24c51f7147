import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  contentWrapper: {
    height: '100%',
    padding: '60px 30px',
    overflow: 'auto',
    '@media (max-width: 768px)': {
      padding: '84px 20px 60px',
      height: '100%',
    },
    '.title': {
      textAlign: 'center',
      '@media (max-width: 768px)': {
        textAlign: 'left',
        position: 'absolute',
        zIndex: 1,
        backgroundColor: 'white',
        top: 0,
        left: 0,
        width: '100%',
        boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
        height: 60,
        display: 'flex',
        alignItems: 'center',
        fontSize: 18,
        padding: '0 42px 0 20px',
      },
    },
    '.sub-title': {
      '@media (max-width: 768px)': {
        fontSize: 16,
      },
    },
  },
  addressItem: {
    minHeight: 80,
    border: '1px solid #dddddd',
    borderRadius: 6,
    padding: '10px 30px',
    cursor: 'pointer',
    '@media (max-width: 768px)': {
      padding: 10,
      minHeight: 50,
      gap: 10,
    },
    '.address-name, .add-text': {
      flex: '1 1 auto',
    },
    '.icon-add, .icon-remove,.icon-end': {
      flexShrink: 0,
    },
    '.add-text': {
      '@media (max-width: 768px)': {
        fontSize: 16,
      },
    },
    '.address-name': {
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
    },
    '.icon-add': {
      borderRadius: '50%',
      width: 50,
      height: 50,
      backgroundColor: '#43749a',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
      '@media (max-width: 768px)': {
        width: 28,
        height: 28,
        svg: {
          width: 12,
          height: 12,
        },
      },
    },
    '.icon-remove': {
      svg: {
        height: 22,
        width: 22,
      },
      '@media (max-width: 768px)': {
        svg: {
          width: 20,
          height: 20,
        },
      },
    },
    '.icon-end': {
      width: 16,
      height: 16,
      '@media (max-width: 768px)': {
        width: 12,
        height: 12,
      },
    },
  },
};

export const styles: Record<string, any> = {
  addessHistoryModalWrapper: (theme: MantineTheme) => ({
    inner: {
      padding: '0px !important',
    },
    content: {
      '@media (max-width: 768px)': {
        height: theme.other?.viewHeight,
      },
    },
    body: {
      background: 'white',
      height: '100%',
    },
  }),
};
