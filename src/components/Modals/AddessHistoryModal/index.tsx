import type { ModalProps } from '@mantine/core';
import { Modal } from '@mantine/core';
import { useLocalStorage, useMediaQuery } from '@mantine/hooks';
import { AddressHistoryForm } from 'components/Profile';
import type { AddressHistoryFormValues } from 'components/Profile/AddressHistoryForm/schema';
import type { AddressHistoryItem } from 'models/address';
import { useEffect, useState } from 'react';
import { LOCAL_STORAGE_KEY } from 'utils/constants';
import { eventLog } from 'utils/helpers';

import AddressHistoryList from './AddressHistoryList';
import { styles, sx } from './styles';

interface AddessHistoryModalProps extends ModalProps {
  handleSelectAddress: (value: AddressHistoryItem) => void;
  initialValues?: AddressHistoryFormValues;
  openForm?: boolean;
}

const AddessHistoryModal: React.FC<AddessHistoryModalProps> = ({
  opened,
  onClose,
  handleSelectAddress,
  initialValues,
  openForm,
}) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const [step, setStep] = useState(1);
  const [addressHistories, setAddressHistories] = useLocalStorage<
    AddressHistoryItem[]
  >({
    key: LOCAL_STORAGE_KEY.ADDRESS_HISTORIES,
    defaultValue: [],
  });

  useEffect(() => {
    if (opened && openForm) {
      setStep(2);
    }
  }, [openForm, opened]);

  const handleCloseModal = () => {
    onClose();
    setTimeout(() => {
      setStep(1);
    }, 1000);
  };

  const handleAddAddressHistory = async (values: AddressHistoryItem) => {
    setAddressHistories([values, ...addressHistories].slice(0, 3));
    eventLog('view_address', {
      address_type: 'manual',
      reference_section: 'registered_address',
    });
    handleSelectAddress(values);
    handleCloseModal();
  };

  return (
    <Modal
      centered
      fullScreen={mobileScreen}
      onClose={handleCloseModal}
      opened={opened}
      size={630}
      styles={styles.addessHistoryModalWrapper}
    >
      {step === 1 ? (
        <AddressHistoryList
          onOpenAdd={() => setStep(2)}
          onSelectAddress={(value) => {
            eventLog('view_address', {
              address_type: 'manual',
              reference_section: 'history_address',
            });
            handleSelectAddress(value);
            handleCloseModal();
          }}
        />
      ) : (
        <AddressHistoryForm
          backFn={() => setStep(1)}
          customSx={sx.addAddressWrapper}
          initialValues={initialValues}
          onSubmit={handleAddAddressHistory}
        />
      )}
    </Modal>
  );
};

export default AddessHistoryModal;
