import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconLocation from '@icons/icon-location.svg';
import IconRemove from '@icons/icon-remove.svg';
import { ActionIcon, Box, Flex, Text, Title } from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import type { AddressHistoryItem } from 'models/address';
import React from 'react';
import { LOCAL_STORAGE_KEY } from 'utils/constants';
import { formatAddress } from 'utils/helpers';

import { sx } from './styles';

const AddressHistoryList: React.FC<{
  onSelectAddress: (value: AddressHistoryItem) => void;
  onOpenAdd: () => void;
}> = ({ onSelectAddress, onOpenAdd }) => {
  const [addressHistories, setAddressHistories] = useLocalStorage<
    AddressHistoryItem[]
  >({
    key: LOCAL_STORAGE_KEY.ADDRESS_HISTORIES,
    defaultValue: [],
  });

  const handleDeleteAddressHistory = (removeIndex: number) => {
    const newHistories = addressHistories.filter(
      (_item, index) => index !== removeIndex,
    );
    setAddressHistories(newHistories);
  };

  return (
    <Box sx={sx.contentWrapper}>
      <Title className="title" color="blackOlive" mb={40} order={3} size={30}>
        住所入力・変更
      </Title>
      <Flex
        align="center"
        gap={30}
        onClick={onOpenAdd}
        sx={sx.addressItem}
        tabIndex={0}
      >
        <Box className="icon-add">
          <IconLocation />
        </Box>
        <Text className="add-text" color="blackOlive" size={20} weight="bold">
          エリアから入力
        </Text>
        <IconChevronRight className="icon-end" />
      </Flex>
      <Title
        className="sub-title"
        color="blackOlive"
        mb={16}
        mt={32}
        order={3}
        size={22}
      >
        直近の住所履歴
      </Title>
      <Flex direction="column" gap={20}>
        {addressHistories.map((item, index) => {
          return (
            <Flex
              align="center"
              gap={30}
              key={index}
              onClick={() => onSelectAddress(item)}
              sx={sx.addressItem}
              tabIndex={0}
            >
              <ActionIcon
                className="icon-remove"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDeleteAddressHistory(index);
                }}
                sx={sx.removeCard}
                variant="transparent"
              >
                <IconRemove />
              </ActionIcon>
              <Text
                className="address-name"
                color="blackOlive"
                lineClamp={4}
                size={20}
                weight="bold"
              >
                {formatAddress(item)}
              </Text>
              <IconChevronRight className="icon-end" />
            </Flex>
          );
        })}
      </Flex>
    </Box>
  );
};

export default AddressHistoryList;
