import { An<PERSON>, Box, Button, Text } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import QRCode from 'react-qr-code';

import { sx } from './styles';

type QRBookingModalProps = {
  link: string;
};

const QRBookingModal = ({
  context,
  id,
  innerProps: { link },
}: ContextModalProps<QRBookingModalProps>) => {
  const handleOnClose = () => {
    context.closeModal(id);
  };

  return (
    <Box sx={sx.qrBookingModalWrapper}>
      <Text mb={24} size={24} weight="bold">
        HOGUGU アプリで
        <br />
        ご予約手続きをされる場合
      </Text>
      <Text lh={1} mb={24} size={16}>
        QRコードからアプリへ移動
      </Text>
      <QRCode
        size={240}
        style={{ border: '10px solid #f5f9fb', padding: 10 }}
        value={link}
      />
      <Text mb={16} mt={24} size={16}>
        HOGUGUアプリを
        <br />
        これからダウンロードされる場合
      </Text>
      <Anchor
        href="https://info.hogugu.com/information/"
        lh={1}
        rel="noreferrer"
        size={16}
        target="_blank"
        weight="bold"
      >
        HOGUGUのご利用方法
      </Anchor>
      <Button fullWidth fz={20} h={70} mt={24} onClick={handleOnClose}>
        キャンセル
      </Button>
    </Box>
  );
};

export default QRBookingModal;
