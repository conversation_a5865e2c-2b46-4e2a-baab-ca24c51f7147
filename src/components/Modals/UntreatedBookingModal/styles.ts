import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  untreatedBookingModalWrapper: {
    padding: '60px 30px',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    textAlign: 'center',
    '@media (max-width: 768px)': {
      padding: '40px 20px',
    },
    '.title': {
      '@media (max-width: 768px)': {
        fontSize: 22,
        marginBottom: 20,
      },
    },
    '.description': {
      textAlign: 'center',
      letterSpacing: '0.48px',
      '@media (max-width: 768px)': {
        fontSize: 13,
        marginBottom: 8,
      },
    },
    '.detail-btn': {
      maxWidth: 300,
      width: '100%',
      marginTop: 40,
      '@media (max-width: 768px)': {
        marginTop: 20,
        maxWidth: '100%',
      },
    },
  },
  bookingInfoWrapper: {
    backgroundColor: '#f5f9fb',
    padding: 20,
    borderRadius: '6px',
    width: '100%',
    boxShadow: '0 0 4px 0 rgba(0, 0, 0, 0.16)',
    '@media (max-width: 768px)': {
      padding: '15px 10px 22px',
    },
    '.therapist-avatar': {
      objectFit: 'cover',
      borderRadius: '50%',
      '@media (max-width: 768px)': {
        width: 55,
        height: 55,
      },
    },
    '.therapist-name': {
      fontSize: 15,
      textAlign: 'start',
    },
    '.booking-content': {
      display: 'flex',
      alignItems: 'center',
      gap: 12,
      svg: {
        width: 20,
        height: 20,
      },
      '@media (max-width: 768px)': {
        gap: 8,
        svg: {
          width: 13,
          height: 13,
        },
      },
    },
  },
};
