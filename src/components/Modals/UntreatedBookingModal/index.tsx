import IconClock from '@icons/icon-clock.svg';
import IconMenu from '@icons/icon-menu.svg';
import IconWarning from '@icons/icon-warning-2.svg';
import { Box, Button, Flex, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import type { ContextModalProps } from '@mantine/modals';
import type { IBookingDetail } from 'models/booking';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

import { sx } from './styles';

type UntreatedBookingModalProps = {
  bookingDetail?: IBookingDetail;
};

const UntreatedBookingModal = ({
  context,
  id,
  innerProps,
}: ContextModalProps<UntreatedBookingModalProps>) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const { bookingDetail } = innerProps;

  return (
    <Box sx={sx.untreatedBookingModalWrapper}>
      <IconWarning />
      <Title
        className="title"
        color="blackOlive"
        mb={30}
        mt={24}
        order={3}
        size={30}
      >
        決済完了のお知らせ
      </Title>
      <Text className="description" color="blackOlive" mb={20} size={16}>
        セラピストは、施術終了時刻まで現地にて待機しておりましたが、お客様とご連絡が取れませんでした。誠に残念ながら当日キャンセルとなりますので、キャンセルポリシーに従い、施術料金100％発生となります旨ご了承くださいませ。
      </Text>
      <Flex direction="column" gap={10} sx={sx.bookingInfoWrapper}>
        <Flex gap={mobileScreen ? 10 : 16}>
          <Image
            alt="Therapist avatar"
            className="therapist-avatar"
            height={72}
            src={
              bookingDetail?.therapist.avatar ||
              '/icons/icon-avatar-default-therapist.svg'
            }
            width={72}
          />
          <Flex
            align="start"
            direction="column"
            gap={10}
            justify={mobileScreen ? 'center' : 'start'}
          >
            <Text
              className="therapist-name"
              lineClamp={mobileScreen ? 2 : 1}
              size={16}
              weight="bold"
            >
              {bookingDetail?.therapist?.nickName ||
                bookingDetail?.therapist?.fullName}
            </Text>
            {!mobileScreen && (
              <>
                <Text className="booking-content" size={14} weight="bold">
                  <IconClock />
                  {bookingDetail?.dateBooking
                    ? dayjs(bookingDetail.dateBooking).format('llll')
                    : '---'}
                </Text>
                <Text className="booking-content" size={14} weight="bold">
                  <IconMenu />
                  {bookingDetail?.menus.length}メニュー&nbsp;&nbsp;&nbsp;
                  {bookingDetail?.duration || 0}分&nbsp;&nbsp;&nbsp;
                  {helpers.numberFormat(bookingDetail?.payment.amount || 0)}円
                </Text>
              </>
            )}
          </Flex>
        </Flex>
        {mobileScreen && (
          <>
            <Text className="booking-content" size={14} weight="bold">
              <IconClock />
              {bookingDetail?.dateBooking
                ? dayjs(bookingDetail.dateBooking).format('llll')
                : '---'}
            </Text>
            <Text className="booking-content" size={14} weight="bold">
              <IconMenu />
              {bookingDetail?.menus.length}メニュー&nbsp;&nbsp;&nbsp;
              {bookingDetail?.duration || 0}分&nbsp;&nbsp;&nbsp;
              {helpers.numberFormat(bookingDetail?.payment.amount || 0)}円
            </Text>
          </>
        )}
      </Flex>
      <Button
        className="detail-btn"
        component={Link}
        href={`/booking/${bookingDetail?._id}`}
        onClick={() => {
          context.closeModal(id);
        }}
        size="lg"
      >
        詳細
      </Button>
    </Box>
  );
};

export default UntreatedBookingModal;
