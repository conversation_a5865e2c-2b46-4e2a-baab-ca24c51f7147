import IconTicket from '@icons/icon-ticket.svg';
import { Box, Button, Text, Title } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import React from 'react';

import useStyles from './styles';

type ReferralCouponModalProps = {
  code: string;
  onConfirm: () => void;
};

const ReferralCouponModal = ({
  context,
  id,
  innerProps: { code, onConfirm },
}: ContextModalProps<ReferralCouponModalProps>) => {
  const { classes } = useStyles();
  const handleOnConfirm = () => {
    context.closeModal(id);
    onConfirm();
  };
  return (
    <Box className={classes.mainContent}>
      <Box
        bg="mutedBlue"
        display="table"
        h={{ base: 64, sm: 88 }}
        mb={{ base: 16, sm: 24 }}
        mx="auto"
        sx={(theme) => ({
          borderRadius: '50%',
          [theme.fn.smallerThan('sm')]: {
            svg: {
              width: 32,
              height: 32,
            },
          },
        })}
        w={{ base: 64, sm: 88 }}
      >
        <Box
          display="table-cell"
          sx={{
            verticalAlign: 'middle',
          }}
        >
          <IconTicket />
        </Box>
      </Box>
      <Title
        color="black"
        fw="bold"
        fz={{ base: 20, sm: 30 }}
        lh={1.2}
        mb={{ base: 12, sm: 24 }}
        order={3}
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            br: {
              display: 'none',
            },
          },
        })}
      >
        紹介クーポン
        <br />
        ご利用いただきありがとうございます。
      </Title>
      <Box className={classes.codeBanner}>
        <Text color="blackOlive" fw={500} fz={{ base: 14, sm: 20 }} lh={1}>
          お客様のクーポンコード:
          <Text
            color="richBlack"
            display="inline"
            fw="bold"
            inherit
            ml={{ base: 4, sm: 8 }}
            sx={{ whiteSpace: 'nowrap' }}
          >
            {code}
          </Text>
        </Text>
      </Box>
      <Text
        color="blackOlive"
        fw={500}
        fz={{ base: 14, sm: 20 }}
        lh={1.3}
        mt={{ base: 16, sm: 24 }}
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            br: {
              display: 'none',
            },
          },
        })}
      >
        こちらのクーポンは本日からご利用できます。クーポン一覧にも表示されるのでご確認ください。
      </Text>
      <Button
        fullWidth
        fz={{ base: 16, sm: 18 }}
        h={{ base: 40, sm: 60 }}
        maw={{ base: 180, sm: 300 }}
        mt={{ base: 24, sm: 40 }}
        mx="auto"
        onClick={handleOnConfirm}
      >
        次へ
      </Button>
    </Box>
  );
};

export default ReferralCouponModal;
