import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  mainContent: {
    background: 'white',
    padding: '60px 38px',
    textAlign: 'center',
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 16px',
    },
  },
  codeBanner: {
    background: theme.colors.water,
    padding: '20px 36px',
    position: 'relative',
    borderRadius: '6px',
    display: 'inline-block',
    [theme.fn.smallerThan('sm')]: {
      padding: '17px 32px 17px 23px',
    },
    '&:after, &:before': {
      content: "''",
      width: 28,
      height: 28,
      background: 'white',
      position: 'absolute',
      top: '50%',
      borderRadius: '50%',
      [theme.fn.smallerThan('sm')]: {
        width: 20,
        height: 20,
      },
    },
    '&:after': {
      right: 0,
      transform: 'translate(50%, -50%)',
    },
    '&:before': {
      left: 0,
      transform: 'translate(-50%, -50%)',
    },
  },
}));

export default useStyles;
