import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconTarget from '@icons/icon-target.svg';
import { Box, Button, Group, Text, Title, Transition } from '@mantine/core';

import { sx } from './styles';

interface AreaSelectionProps {
  handleFindCurrentLocation: () => void;
  handlePickPrefecture: (prefecture: any) => void;
  geolocationMessage?: string;
  prefectureGroup: Record<string, unknown>;
  initialPrefecture: Record<string, unknown>;
  isLoadingCoordinates?: boolean;
  handleCloseModal?: () => void;
}

const AreaSelection: React.FC<AreaSelectionProps> = ({
  handleFindCurrentLocation,
  handlePickPrefecture,
  geolocationMessage,
  prefectureGroup,
  initialPrefecture,
  isLoadingCoordinates = false,
  handleCloseModal,
}) => {
  return (
    <Box>
      <Title order={3} size={24} sx={sx.title}>
        エリア
      </Title>
      <Text onClick={handleCloseModal} size={14} sx={sx.back} variant="link">
        <IconChevronRight
          style={{ transform: 'rotate(180deg)', marginRight: 14 }}
        />
        検索条件
      </Text>
      <Box component="section" sx={sx.currentLocationSearchWrapper}>
        <Title order={4} size={20}>
          現在地から探す
        </Title>
        <Button
          fullWidth
          leftIcon={<IconTarget />}
          loading={isLoadingCoordinates}
          onClick={handleFindCurrentLocation}
        >
          位置情報を取得
        </Button>
        <Transition
          duration={400}
          mounted={!!geolocationMessage}
          timingFunction="ease"
          transition="fade"
        >
          {(style) => (
            <Text style={style} sx={sx.geolocationMessage}>
              {geolocationMessage}
            </Text>
          )}
        </Transition>
      </Box>
      <Box component="section" sx={sx.prefectureListWrapper}>
        <Title order={4} size={20}>
          都道府県を選択
        </Title>
        {Object.keys(prefectureGroup).map((key) => {
          return (
            <Box key={key} sx={sx.prefectureSectionWrapper}>
              <Text size={18} sx={sx.sectionTitle} weight="bold">
                {key}
              </Text>
              <Group sx={sx.sectionGroup}>
                {(prefectureGroup[key] as any).map((prefecture: any) => {
                  return (
                    <Box
                      data-selected={
                        initialPrefecture.nameEn === prefecture.nameEn
                      }
                      key={prefecture.nameEn}
                      onClick={() => handlePickPrefecture(prefecture)}
                      sx={sx.prefecture}
                    >
                      <span>{prefecture.name}</span>
                      <IconChevronRight />
                    </Box>
                  );
                })}
              </Group>
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

export default AreaSelection;
