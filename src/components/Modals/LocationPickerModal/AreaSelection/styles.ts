import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  title: {
    textAlign: 'center',
    marginBottom: 60,
    '@media (max-width: 768px)': {
      marginBottom: 0,
    },
  },
  back: {
    color: '#000000',
    marginBottom: 15,
    marginTop: 10,
    display: 'none',
    cursor: 'pointer',
    '@media (max-width: 768px)': {
      display: 'inline-block',
    },
  },
  currentLocationSearchWrapper: {
    marginBottom: 30,
    '@media (max-width: 768px)': {
      marginBottom: 15,
    },
    h4: {
      marginBottom: 15,
      '@media (max-width: 768px)': {
        marginBottom: 10,
        fontSize: 16,
      },
    },
    button: {
      height: 70,
      fontSize: 20,
      '@media (max-width: 768px)': {
        height: 60,
        fontSize: 16,
      },
    },
  },
  geolocationMessage: {
    fontSize: 20,
    marginTop: 8,
    color: '#bf2020',
    '@media (max-width: 768px)': {
      fontSize: 16,
    },
  },
  prefectureListWrapper: {
    h4: {
      marginBottom: 15,
      '@media (max-width: 768px)': {
        marginBottom: 10,
        fontSize: 16,
      },
    },
  },
  prefectureSectionWrapper: {
    '@media (max-width: 768px)': {
      marginBottom: 15,
    },
  },
  sectionTitle: {
    color: '#43749a',
    backgroundColor: '#f5f9fb',
    padding: '12px 19px',
    '@media (max-width: 768px)': {
      fontSize: 16,
    },
  },
  sectionGroup: {
    padding: '24px 19px 30px',
    gap: 20,
    alignItems: 'stretch',
    '@media (max-width: 768px)': {
      padding: 0,
      gap: 0,
    },
  },
  prefecture: {
    fontWeight: 'bold',
    padding: '6px 8px 6px 11px',
    color: '#7f7f7f',
    display: 'flex',
    fontSize: 16,
    alignItems: 'center',
    cursor: 'pointer',
    justifyContent: 'space-between',
    flex: '0 0 calc(25% - 15px)',
    borderBottom: '1px solid #d6d6d6',
    '@media (max-width: 768px)': {
      flex: '0 0 50%',
      border: '1.5px solid #f5f9fb',
      fontSize: 14,
      padding: '14px 20px',
    },
    '&:hover': {
      color: '#43749a',
      borderColor: '#43749a',
    },
    // '&[data-selected=true]': {
    //   color: '#43749a',
    //   borderColor: '#43749a',
    //   '@media (max-width: 768px)': {
    //     borderColor: '#f5f9fb',
    //   },
    // },
  },
};
