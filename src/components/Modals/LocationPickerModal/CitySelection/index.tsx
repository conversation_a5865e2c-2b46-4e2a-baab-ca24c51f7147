import IconChevronRight from '@icons/icon-chevron-right.svg';
import { Box, Button, Group, Text, Title } from '@mantine/core';
import type { ICityItem, IPrefectureItem } from 'models/resource';
import { useState } from 'react';

import { styles, sx } from './styles';

interface CitySelectionProps {
  handleGoBackArea: () => void;
  handleSelectCity: (city?: ICityItem) => void;
  cityGroup: Record<string, ICityItem[]>;
  selectedPrefecture: IPrefectureItem;
  initialCity: Record<string, unknown>;
}

const CitySelection: React.FC<CitySelectionProps> = ({
  handleGoBackArea,
  handleSelectCity,
  cityGroup,
  selectedPrefecture,
  initialCity,
}) => {
  const [filter, setFilter] = useState<string>(
    Object.keys(cityGroup).sort()[0] || '',
  );
  const cityList: ICityItem[] = cityGroup[filter] || [];

  return (
    <Box>
      <Title order={3} size={24} sx={sx.title}>
        市区町村
      </Title>
      <Text onClick={handleGoBackArea} size={16} sx={sx.back} variant="link">
        <IconChevronRight
          style={{ transform: 'rotate(180deg)', marginRight: 14 }}
        />
        エリア
      </Text>
      <Text size={24} sx={sx.prefectureName}>
        {selectedPrefecture.name}
      </Text>
      <Button
        fullWidth
        onClick={() => handleSelectCity()}
        rightIcon={<IconChevronRight />}
        styles={styles.selectAllBtn}
      >
        {selectedPrefecture.name}すべて
      </Button>
      <Group sx={sx.kanaGroup}>
        {['あ', 'か', 'さ', 'た', 'な', 'は', 'ま', 'や', 'ら', 'わ'].map(
          (key) => {
            return (
              <Button
                data-selected={key === filter}
                disabled={!Object.keys(cityGroup).includes(key)}
                key={key}
                onClick={() => setFilter(key)}
                variant="white"
              >
                {key}行
              </Button>
            );
          },
        )}
      </Group>
      <Box sx={sx.citySectionWrapper}>
        {filter && (
          <Text size={18} sx={sx.sectionTitle} weight="bold">
            {filter}行
          </Text>
        )}
        <Group sx={sx.sectionGroup}>
          {cityList.map((city) => {
            return (
              <Box
                data-selected={initialCity.nameEn === city.nameEn}
                key={city.nameEn}
                onClick={() => handleSelectCity(city)}
                sx={sx.city}
              >
                <span>{city.name}</span>
                <IconChevronRight />
              </Box>
            );
          })}
        </Group>
      </Box>
    </Box>
  );
};

export default CitySelection;
