import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  title: {
    textAlign: 'center',
  },
  back: {
    color: '#000000',
    marginBottom: 24,
    marginTop: 10,
    display: 'inline-block',
    cursor: 'pointer',
    '@media (max-width: 768px)': {
      margin: '10px 0',
      fontSize: 14,
    },
  },
  prefectureName: {
    marginBottom: 30,
    textAlign: 'center',
    fontWeight: 'bold',
    '@media (max-width: 768px)': {
      display: 'none',
    },
  },
  kanaGroup: {
    gap: 20,
    padding: '26px 0 32px',
    button: {
      flex: '0 0 calc(20% - 16px)',
      backgroundColor: '#f5f9fb',
      fontSize: 16,
      borderRadius: 4,
      '&:hover': {
        textDecoration: 'underline',
      },
      '&[data-selected=true]': {
        backgroundColor: '#43749A',
        color: '#fff',
      },
    },
    '@media (max-width: 768px)': {
      gap: 6,
      padding: '14px 0 18px',
      button: {
        fontSize: 14,
        flex: '0 0 calc(20% - 5px)',
        padding: 0,
      },
    },
  },
  sectionTitle: {
    color: '#3c3c3c',
    backgroundColor: '#f8f8f8',
    padding: '12px 19px',
    '@media (max-width: 768px)': {
      fontSize: 14,
      padding: '8px 20px',
    },
  },
  sectionGroup: {
    padding: '24px 19px',
    gap: 20,
    alignItems: 'stretch',
    '@media (max-width: 768px)': {
      gap: 0,
      padding: 0,
    },
  },
  city: {
    fontWeight: 'bold',
    padding: '6px 8px 6px 11px',
    color: '#7f7f7f',
    display: 'flex',
    fontSize: 16,
    alignItems: 'center',
    cursor: 'pointer',
    justifyContent: 'space-between',
    flex: '0 0 calc(25% - 15px)',
    borderBottom: '1px solid #d6d6d6',
    '@media (max-width: 768px)': {
      flex: '0 0 50%',
      border: '1.5px solid #f8f8f8',
      fontSize: 14,
      padding: '14px 20px',
    },
    '&:hover': {
      color: '#43749a',
      borderColor: '#43749a',
    },
    // '&[data-selected=true]': {
    //   color: '#43749a',
    //   borderColor: '#43749a',
    //   '@media (max-width: 768px)': {
    //     borderColor: '#f8f8f8',
    //   },
    // },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  selectAllBtn: {
    root: {
      fontSize: 20,
      color: '#ffffff',
      height: 70,
      position: 'relative',
      '@media (max-width: 768px)': {
        height: 45,
        fontSize: 16,
      },
    },
    rightIcon: {
      '@media (max-width: 768px)': {
        position: 'absolute',
        right: 30,
      },
    },
  },
};
