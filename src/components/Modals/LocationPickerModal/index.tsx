import { Box, Container } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import { useFetchData, useMutate } from 'hooks';
import { get, invoke, isEmpty } from 'lodash';
import type {
  GetAreaCoordinatesPayload,
  ICityItem,
  IPrefectureItem,
} from 'models/resource';
import { resourceQuery } from 'models/resource';
import { useMemo, useState } from 'react';
import { eventLog, requestLocation } from 'utils/helpers';
import notification from 'utils/notification';

import AreaSelection from './AreaSelection';
import CitySelection from './CitySelection';
import styles from './styles';

type LocationPickerModalProps = {
  onConfirm: (data: unknown[]) => void;
  initialData?: Record<string, unknown>[];
};

const LocationPickerModal = ({
  id,
  context,
  innerProps,
}: ContextModalProps<LocationPickerModalProps>) => {
  const [geolocationMessage, setGeolocationMessage] = useState<string>('');
  const [selectedPrefecture, setSelectedPrefecture] = useState<
    IPrefectureItem | undefined
  >();
  const { data: prefectureList } = useFetchData<IPrefectureItem[]>({
    ...resourceQuery.getPrefectureList,
    enabled: false,
  });

  const { mutateAsync: getAreaCoordinatesFn, isLoading: isLoadingCoordinates } =
    useMutate<GetAreaCoordinatesPayload, IPrefectureItem>(
      resourceQuery.getAreaCoordinates,
    );

  const prefectureGroup = useMemo(() => {
    const entities: Record<string, IPrefectureItem[]> = {};
    if (prefectureList) {
      prefectureList.forEach((item) => {
        if (item.group) {
          const entitiesGroup = entities[item.group];
          if (entitiesGroup) {
            entities[item.group] = [...entitiesGroup, item];
          } else {
            entities[item.group] = [item];
          }
        }
      });
    }
    return entities;
  }, [prefectureList]);

  const cityGroup = useMemo(() => {
    const entities: Record<string, ICityItem[]> = {};
    if (selectedPrefecture) {
      selectedPrefecture.children?.forEach((city) => {
        if (city.type) {
          const entitiesGroup = entities[city.type];
          if (entitiesGroup) {
            entities[city.type] = [...entitiesGroup, city];
          } else {
            entities[city.type] = [city];
          }
        }
      });
    }
    return entities;
  }, [selectedPrefecture]);

  const setFailedGeolocationMessage = () =>
    setGeolocationMessage(
      '位置情報が取得できません。\n許可いただくか、電波の届く位置で再度お試しください。',
    );

  const handleFindCurrentLocation = () => {
    requestLocation({
      successCallback: (position) => {
        getAreaCoordinatesFn(
          {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          },
          {
            onSuccess: (data) => {
              const areas = [];
              if (data) {
                eventLog('view_address', {
                  address_type: 'manual',
                  reference_section: 'google_address_with_confirmation',
                });
                areas.push(data);
                if (!isEmpty(data.child)) {
                  areas.push(data.child);
                }
                invoke(innerProps, 'onConfirm', areas);
                context.closeModal(id);
              } else {
                notification.show({
                  message: '位置情報が取得できません。',
                });
              }
            },
          },
        );
      },
      errorCallback: setFailedGeolocationMessage,
    });
  };

  const handleGoBackArea = () => {
    setSelectedPrefecture(undefined);
  };

  const handleCloseModal = () => {
    context.closeModal(id);
  };

  const handlePickPrefecture = (prefecture: IPrefectureItem) => {
    setSelectedPrefecture(prefecture);
  };

  const handleSelectCity = (city?: ICityItem) => {
    eventLog('view_address', {
      address_type: 'manual',
      reference_section: 'area_address',
    });
    if (city) {
      invoke(innerProps, 'onConfirm', [selectedPrefecture, city]);
    } else {
      invoke(innerProps, 'onConfirm', [selectedPrefecture]);
    }
    context.closeModal(id);
  };

  return (
    <Box sx={styles.modalWrapper}>
      <Container p={0} size={1140}>
        {isEmpty(selectedPrefecture) ? (
          <AreaSelection
            geolocationMessage={geolocationMessage}
            handleCloseModal={handleCloseModal}
            handleFindCurrentLocation={handleFindCurrentLocation}
            handlePickPrefecture={handlePickPrefecture}
            initialPrefecture={get(innerProps, 'initialData[0]', {})}
            isLoadingCoordinates={isLoadingCoordinates}
            prefectureGroup={prefectureGroup}
          />
        ) : (
          <CitySelection
            cityGroup={cityGroup}
            handleGoBackArea={handleGoBackArea}
            handleSelectCity={handleSelectCity}
            initialCity={get(innerProps, 'initialData[1]', {})}
            selectedPrefecture={selectedPrefecture}
          />
        )}
      </Container>
    </Box>
  );
};

export default LocationPickerModal;
