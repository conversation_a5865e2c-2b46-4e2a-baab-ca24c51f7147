import { Box, Button, Stack, Title } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import _invoke from 'lodash/invoke';
import helpers from 'utils/helpers';

import { sx } from './styles';

type MenuPriceSelectModalProps = {
  priceOptions: {
    currency: string;
    duration: number;
    originalPrice: number;
    price: number;
  }[];
  priceSelected?: {
    currency: string;
    duration: number;
    originalPrice: number;
    price: number;
  };
  onCancel: () => void;
  onSelect: (option: Record<string, unknown>) => void;
};

const MenuPriceSelectModal = ({
  context,
  id,
  innerProps,
}: ContextModalProps<MenuPriceSelectModalProps>) => {
  const { priceOptions = [], priceSelected } = innerProps;

  const handleOnCancel = () => {
    _invoke(innerProps, 'onCancel');
    context.closeModal(id);
  };

  const handleOnSelect = (option: Record<string, unknown>) => {
    _invoke(innerProps, 'onSelect', option);
    context.closeModal(id);
  };

  return (
    <Box sx={sx.modalWrapper}>
      <Title order={3} size={24} sx={sx.modalTitle}>
        時間を選択してください
      </Title>
      <Stack sx={sx.priceGroupWrapper}>
        {priceOptions
          .sort((a, b) => a.duration - b.duration)
          .map((option, index) => {
            const seleced =
              priceSelected?.duration === option.duration &&
              priceSelected?.price === option.price;
            return (
              <Button
                data-selected={seleced}
                fullWidth
                key={index}
                onClick={() => handleOnSelect(option)}
                sx={sx.priceOption}
                variant="white"
              >
                {option.duration}分 / ¥{helpers.numberFormat(option.price)}
              </Button>
            );
          })}
        <Button
          fullWidth
          onClick={handleOnCancel}
          sx={sx.cancelBtn}
          variant="white"
        >
          キャンセル
        </Button>
      </Stack>
    </Box>
  );
};

export default MenuPriceSelectModal;
