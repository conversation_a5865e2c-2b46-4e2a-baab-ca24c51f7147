import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  modalWrapper: {
    maxWidth: 335,
    margin: '0 auto',
    padding: 0,
    background: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
  },
  modalTitle: {
    borderRadius: '6px 6px 0 0',
    textAlign: 'center',
    backgroundColor: 'rgba(83, 127, 161, 0.9)',
    color: '#ffffff',
    fontSize: 18,
    padding: '18px 0',
    marginBottom: 30,
    '@media (max-width: 768px)': {
      fontSize: 14,
      padding: '14px 0',
      marginBottom: 12,
    },
  },
  priceGroupWrapper: {
    padding: '0 20px 23px',
    '@media (max-width: 768px)': {
      padding: '0 15px 10px',
      gap: 4,
    },
  },
  priceOption: {
    borderRadius: 6,
    height: 60,
    fontSize: 18,
    fontWeight: 'bold',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    position: 'relative',
    '&:hover': {
      backgroundColor: '#f5f9fb',
      '@media (max-width: 768px)': {
        backgroundColor: 'transparent',
      },
    },
    '&[data-selected=true]:after': {
      position: 'absolute',
      content: 'url(/icons/icon-checked-1.svg)',
      left: 16,
      top: 18,
      '@media (max-width: 768px)': {
        top: 4,
      },
    },
    '@media (max-width: 768px)': {
      fontSize: 16,
      boxShadow: 'none',
      height: 32,
      borderBottom: '1px solid #dbdbdb',
      backgroundColor: 'transparent',
    },
  },
  cancelBtn: {
    height: 42,
    fontSize: 18,
    fontWeight: 'bold',
    borderRadius: 6,
    marginTop: 5,
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.16)',
    backgroundColor: '#e4edf4',
    color: '#5480a2',
    '@media (max-width: 768px)': {
      marginTop: 9,
    },
  },
};
