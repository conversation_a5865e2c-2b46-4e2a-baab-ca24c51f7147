import IconAdd from '@icons/icon-add.svg';
import IconChervonRight from '@icons/icon-chevron-right.svg';
import type { ModalProps } from '@mantine/core';
import { Box, Button, Flex, Modal, Radio, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { AddCardForm } from 'components/PaymentCard';
import CardItem from 'components/PaymentCard/CardList/CardItem';
import { useFetchData, useMutate } from 'hooks';
import { get } from 'lodash';
import type { AddPaymentCardPayload, CardListResponse } from 'models/payment';
import { paymentQuery } from 'models/payment';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { eventLog } from 'utils/helpers';
import notification from 'utils/notification';

import { styles, sx } from './styles';

interface PaymentMethodModalProps extends ModalProps {
  activeCard?: string;
  defaultCard?: string;
  handleChangePayment: (id: string) => void;
  selectPaymentText?: string;
  isChangingPayment?: boolean;
}

const PaymentMethodModal: React.FC<PaymentMethodModalProps> = ({
  opened,
  onClose,
  activeCard,
  defaultCard,
  handleChangePayment,
  selectPaymentText,
  isChangingPayment,
}) => {
  const { pathname } = useRouter();
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const [selected, setSelected] = useState(activeCard);
  const [step, setStep] = useState(1);

  const { data, refetch: refetchList } = useFetchData<CardListResponse>({
    ...paymentQuery.getCardList,
    staleTime: 1000 * 60 * 2,
  });

  const { mutateAsync: addPayment, isLoading: isAddingCard } =
    useMutate<AddPaymentCardPayload>({
      ...paymentQuery.addPaymentCard,
      successMessage: 'カードを追加登録しました。',
    });

  useEffect(() => {
    if (opened) {
      setSelected(activeCard);
    }
  }, [activeCard, opened]);

  const handleAddCard = async (cardToken: string) => {
    try {
      await addPayment({ token: cardToken });
      if (pathname === '/booking' && data?.cards.length === 0) {
        eventLog('add_payment_info', {
          page: 'Booking',
        });
      }
      refetchList();
      setStep(1);
    } catch (e) {
      notification.show({
        type: 'error',
        message: get(e, 'error', "Can't add card at the moment."),
      });
    }
  };

  const handleCloseModal = () => {
    setStep(1);
    onClose();
  };

  return (
    <Modal
      centered
      fullScreen={mobileScreen}
      onClose={handleCloseModal}
      opened={opened}
      size={630}
      styles={styles.paymentMethodModalWrapper}
    >
      {step === 1 ? (
        <Box sx={sx.contentWrapper}>
          <Title
            className="title"
            color="blackOlive"
            mb={24}
            order={3}
            size={30}
          >
            お支払い方法
          </Title>
          <Text className="description" color="blackOlive" mb={32} size={16}>
            ご登録中のクレジットカード
          </Text>
          <Flex className="card-list" direction="column" gap={24}>
            {data?.cards?.map((card) => {
              return (
                <Radio
                  checked={card.id === selected}
                  key={card.id}
                  label={
                    <CardItem
                      card={card}
                      isDefaultCard={card.id === defaultCard}
                      isSelected={card.id === selected}
                      onSelect={(id) => setSelected(id)}
                    />
                  }
                  onChange={() => setSelected(card.id)}
                  styles={styles.radioStyles}
                  value={card.id}
                />
              );
            })}
          </Flex>
          <Text
            className="add-card-btn"
            color="queenBlue.6"
            onClick={() => setStep(2)}
            size={16}
            tabIndex={0}
            weight="bold"
          >
            <IconAdd className="icon-add" />
            カードを追加する <IconChervonRight />
          </Text>
          <Flex gap={20} justify="space-between" sx={sx.btnGroup}>
            <Button
              className="cancel-btn"
              color="grey"
              onClick={handleCloseModal}
              size="lg"
              variant="outline"
            >
              キャンセル
            </Button>
            <Button
              disabled={!selected}
              loading={isChangingPayment}
              onClick={() => {
                if (selected) {
                  handleChangePayment(selected);
                }
              }}
              size="lg"
            >
              {selectPaymentText || '保存'}
            </Button>
          </Flex>
        </Box>
      ) : (
        <AddCardForm
          customSx={sx.contentWrapper}
          footerContent={(formLoading) => (
            <Flex gap={20} justify="space-between" mt={40} sx={sx.btnGroup}>
              <Button
                className="cancel-btn"
                color="grey"
                onClick={() => setStep(1)}
                size="lg"
                variant="outline"
              >
                戻る
              </Button>
              <Button
                loading={isAddingCard || formLoading}
                size="lg"
                type="submit"
              >
                登録する
              </Button>
            </Flex>
          )}
          headerContent={
            <>
              <Text
                className="title"
                color="blackOlive"
                mb={24}
                size={30}
                weight="bold"
              >
                {mobileScreen
                  ? 'クレジットカードの登録・追加登録'
                  : 'クレジットカードの登録\n追加登録'}
              </Text>
              <Text
                className="description extra"
                color="blackOlive"
                mb={24}
                size={16}
              >
                予約時にご利用いただくクレジットカードをご登録ください。
              </Text>
            </>
          }
          onSubmit={handleAddCard}
        />
      )}
    </Modal>
  );
};

export default PaymentMethodModal;
