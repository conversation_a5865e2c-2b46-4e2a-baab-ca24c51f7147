import type { MantineTheme, Styles, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  contentWrapper: {
    height: '100%',
    padding: '60px 30px 30px',
    overflow: 'auto',
    '@media (max-width: 768px)': {
      padding: '84px 20px 98px',
      height: 'calc(100%)',
    },
    '.title': {
      textAlign: 'center',
      '@media (max-width: 768px)': {
        textAlign: 'left',
        position: 'absolute',
        zIndex: 1,
        backgroundColor: 'white',
        top: 0,
        left: 0,
        width: '100%',
        boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
        height: 60,
        display: 'flex',
        alignItems: 'center',
        fontSize: 18,
        padding: '0 42px 0 20px',
      },
    },
    '.description': {
      textAlign: 'center',
      '@media (max-width: 768px)': {
        textAlign: 'left',
        fontsize: 14,
        '&.extra': {
          marginBottom: 16,
        },
      },
    },
    '.card-list': {
      width: '100%',
    },
    '.add-card-btn': {
      textAlign: 'center',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      textDecoration: 'underline',
      textUnderlineOffset: 5,
      margin: '32px 0 0',
      cursor: 'pointer',
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
      '.icon-add': {
        width: 22,
        height: 22,
      },
    },
  },
  cardAddWrapper: {
    height: '100%',
    padding: '60px 30px 30px',
    overflow: 'auto',
  },
  btnGroup: {
    marginTop: 40,
    width: '100%',
    backgroundColor: 'white',
    '@media (max-width: 768px)': {
      gap: 15,
      padding: '16px 20px',
      marginTop: 0,
      boxShadow: '0 -4px 8px 0 rgba(0, 0, 0, 0.06)',
      position: 'fixed',
      bottom: 0,
      left: 0,
    },
    button: {
      flex: '1 1 50%',
      '&.cancel-btn': {
        padding: 0,
        span: {
          lineHeight: 1.2,
          textAlign: 'center',
          '@media (max-width: 768px)': {
            whiteSpace: 'pre-line',
          },
        },
      },
    },
  },
};

export const styles: Record<string, Styles<string, any>> = {
  paymentMethodModalWrapper: (theme: MantineTheme) => ({
    inner: {
      padding: '0px !important',
    },
    content: {
      '@media (max-width: 768px)': {
        height: theme.other?.viewHeight,
      },
    },
    body: {
      background: 'white',
      height: '100%',
    },
  }),
  radioStyles: {
    root: {
      width: '100%',
      position: 'relative',
    },
    body: {
      justifyContent: 'center',
      gap: 16,
    },
    inner: {
      alignSelf: 'center',
      svg: {
        color: '#e8a62d',
        transform: 'scale(2) !important',
        '@media (max-width: 768px)': {
          transform: 'scale(1) !important',
        },
      },
    },
    radio: {
      cursor: 'pointer',
      width: 28,
      height: 28,
      borderWidth: '1.5px',
      '@media (max-width: 768px)': {
        padding: '84px 20px 20px',
        width: 16,
        height: 16,
      },
      '&:checked': {
        backgroundColor: 'white',
        borderColor: '#b2b2b2',
      },
    },
    labelWrapper: {
      flex: '0 1 360px',
    },
    label: {
      paddingLeft: 0,
    },
  },
};
