import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  avatarContainer: {
    '@media (max-width: 768px)': {
      paddingTop: 40,
    },
  },
  name: {
    '@media (max-width: 768px)': {
      fontSize: 16,
    },
  },
  subText: {
    '@media (max-width: 768px)': {
      fontSize: 14,
    },
  },
  actionGroup: {
    '@media (max-width: 768px)': {
      gap: 70,
      marginTop: 20,
    },
  },
  button: {
    borderRadius: '50%',
    '@media (max-width: 768px)': {
      width: 64,
      height: 64,
      minWidth: 64,
      minHeight: 64,
    },
  },
  buttonText: {
    marginTop: 12,
    '@media (max-width: 768px)': {
      fontSize: 14,
    },
  },
  minimizeButton: {
    svg: { transform: 'rotate(180deg)' },
    '@media (max-width: 768px)': {
      width: 30,
      height: 30,
      minWidth: 30,
      minHeight: 30,
      svg: {
        width: 16,
        height: 8,
      },
    },
  },
  draggableScreen: {
    pointerEvents: 'none',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'center',
    position: 'fixed',
    width: '100%',
    height: `calc(100vh - ${theme.other.headerHeight})`,
    top: theme.other.headerHeight,
    left: 0,
    zIndex: 10,
    '@media (max-width: 768px)': {
      height: `calc(100vh - ${theme.other.headerHeightMobile})`,
      top: theme.other.headerHeightMobile,
    },
  },
  toolbar: {
    pointerEvents: 'all',
    backgroundColor: '#fafff5',
    gap: 38,
    maxWidth: 580,
    width: '100%',
    border: '1px solid #41850a',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    padding: '9px 24px 9px 16px',
    display: 'flex',
    cursor: 'pointer',
    zIndex: 2,
    '@media (max-width: 768px)': {
      width: 'calc(100% - 32px)',
      padding: '12px 16px 12px',
    },
  },
  mobileMuteButton: {
    '@media (max-width: 768px)': {
      display: 'none',
    },
  },
  toolbarHangup: {
    '@media (max-width: 768px)': {
      '& button': {
        width: 44,
        height: 44,
        minHeight: 44,
        minWidth: 44,
      },
      '& .mantine-Text-root': {
        display: 'none',
      },
    },
  },
  toolbarName: {
    '@media (max-width: 768px)': {
      fontSize: 12,
    },
  },
  toolbarInfo: {
    flex: 1,
    '@media (max-width: 768px)': {
      fontSize: 12,
      gap: 10,
    },
  },
  toolbarAvatar: {
    borderRadius: '50%',
    '@media (max-width: 768px)': {
      width: 42,
      height: 42,
      minWidth: 42,
      minHeight: 42,
    },
  },
  redButton: {
    backgroundColor: '#db1e0e',
    '&:hover': { backgroundColor: '#db1e0e' },
  },
}));

export default useStyles;
