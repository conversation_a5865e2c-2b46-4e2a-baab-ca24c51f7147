import CheckIcon from '@icons/icon-check.svg';
import ArrowUpIcon from '@icons/icon-chevron-up.svg';
import CloseIcon from '@icons/icon-close.svg';
import DragIcon from '@icons/icon-drag.svg';
import HangupIcon from '@icons/icon-hangup.svg';
import MuteIcon from '@icons/icon-mute.svg';
import {
  ActionIcon,
  Anchor,
  Avatar,
  Box,
  Flex,
  Modal,
  Text,
} from '@mantine/core';
import { usePrevious } from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import { Call } from '@twilio/voice-sdk';
import dayjs from 'dayjs';
import { motion, useDragControls } from 'framer-motion';
import { useGlobalState, useUser } from 'hooks';
import useTimer from 'hooks/useTimer';
import type { MouseEventHandler } from 'react';
import { useEffect, useRef, useState } from 'react';
import helpers, { eventLog } from 'utils/helpers';

import useStyles from './styles';

const CallNotificationModal = () => {
  const { data: user } = useUser();
  const { count, start, stop } = useTimer({});
  const { call, setCall, device } = useGlobalState();
  const [minimize, setMinimize] = useState(false);
  const [acceptCall, setAcceptCall] = useState(false);
  const [mute, setMute] = useState(false);
  const { classes } = useStyles();
  const browserType = helpers.detectBrowser();
  const prevUserId = usePrevious(user?._id);

  const callerAvatarParam = call?.customParameters.get('callerAvatar');
  // On IOS app, if therapist has no avatar, dev set callerAvatar as `avatar`
  const callerAvatar =
    callerAvatarParam === 'avatar' || !callerAvatarParam
      ? '/icons/icon-default-avatar.svg'
      : callerAvatarParam;
  const callerName = call?.customParameters.get('callerName');
  const receiverAvatar =
    call?.customParameters.get('receiverAvatar') ||
    '/icons/icon-default-avatar.svg';
  const receiverName = call?.customParameters.get('receiverName');

  const callDirection = call?.direction;
  const isOutgoingCall = callDirection === Call.CallDirection.Outgoing;

  const draggableRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);
  const controls = useDragControls();

  useEffect(() => {
    if (prevUserId && !user?._id) {
      device?.disconnectAll();
      setCall(undefined);
      stop();
      setMinimize(false);
    }
  }, [device, prevUserId, setCall, stop, user?._id]);

  useEffect(() => {
    if (callDirection === Call.CallDirection.Outgoing) {
      setAcceptCall(true);
      start();
    }
  }, [callDirection, start]);

  // Hangup when making outgoing call or the call was connected
  const handleHangUpCall: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.stopPropagation();
    device?.disconnectAll();
    setCall(undefined);
    stop();
    setMinimize(false);
  };

  // Reject when there is an incoming call
  const handleRejectCall = () => {
    call?.reject();
    setCall(undefined);
    eventLog('decline_call');
  };

  const handleMute: MouseEventHandler<HTMLButtonElement> = async (e) => {
    e.stopPropagation();
    setMute((prevMute) => {
      call?.mute(!prevMute);
      return !prevMute;
    });
  };

  const handleAcceptCall = async () => {
    const isEnabledAudio = await helpers.isEnabledAudio();
    if (isEnabledAudio) {
      setAcceptCall(true);
      call?.accept();
      start();
    } else {
      const supportLink = helpers.getSupportLink(browserType);
      openContextModal({
        modal: 'AlertModal',
        size: 630,
        innerProps: {
          content: supportLink ? (
            <Text sx={{ wordBreak: 'break-word' }}>
              通話を開始するには、マイクへのアクセスを許可する必要があります。マイクの設定を変更する方法は
              <Anchor href={supportLink} target="blank">
                こちら
              </Anchor>
              をご覧ください
            </Text>
          ) : (
            '通話を開始するには、マイクへのアクセスを許可する必要があります。'
          ),
        },
        centered: true,
      });
    }
  };

  useEffect(() => {
    if (call) {
      call.on('cancel', () => {
        stop();
        setAcceptCall(false);
        setMinimize(false);
      });
      call.on('disconnect', () => {
        stop();
        setAcceptCall(false);
        setCall(undefined);
        setMinimize(false);
      });
    }
  }, [call, setCall, stop]);

  if (!call) {
    return null;
  }
  if (minimize) {
    return (
      <Box className={classes.draggableScreen} ref={draggableRef}>
        <motion.div
          className={classes.toolbar}
          drag
          dragConstraints={draggableRef}
          dragControls={controls}
          dragListener={false}
          dragMomentum={false}
          dragTransition={{
            bounceStiffness: 100,
            bounceDamping: 10,
          }}
          onClick={() => {
            if (!isDragging.current) {
              setMinimize(false);
            }
          }}
          onDragEnd={() => {
            setTimeout(() => {
              isDragging.current = false;
            }, 150);
          }}
          onDragStart={() => {
            isDragging.current = true;
          }}
          style={{
            y: -24,
          }}
        >
          <Flex align="center" className={classes.toolbarInfo} gap={13}>
            <Flex align="center">
              <ActionIcon
                onPointerDown={(event) => {
                  controls.start(event);
                }}
                sx={{
                  marginLeft: -5,
                  marginRight: -5,
                  touchAction: 'none',
                  cursor: 'grab',
                }}
                variant="transparent"
              >
                <DragIcon />
              </ActionIcon>
            </Flex>
            <Avatar
              alt=""
              className={classes.toolbarAvatar}
              size={48}
              src={isOutgoingCall ? receiverAvatar : callerAvatar}
            />
            <Box sx={{ flex: 1 }}>
              <Text
                className={classes.toolbarName}
                fw={700}
                fz={16}
                lineClamp={1}
              >
                {isOutgoingCall ? receiverName : callerName}
              </Text>
              <Text fw={500} mt={8}>
                {dayjs.duration(count * 1000).format('mm:ss')}
              </Text>
            </Box>
          </Flex>
          <Flex gap={24}>
            <Box className={classes.mobileMuteButton}>
              <ActionIcon
                color="avocado"
                onClick={handleMute}
                size={50}
                sx={{ borderRadius: '50%' }}
                variant={mute ? 'filled' : 'outline'}
              >
                <MuteIcon color={mute ? '#fff' : '#41850a'} />
              </ActionIcon>
              <Text align="center" fw={500} fz={12} mt={4}>
                消音
              </Text>
            </Box>
            <Box className={classes.toolbarHangup}>
              <ActionIcon
                onClick={handleHangUpCall}
                size={50}
                sx={{ borderRadius: '50%', backgroundColor: '#db1e0e' }}
                variant="filled"
              >
                <HangupIcon />
              </ActionIcon>
              <Text align="center" fw={500} fz={12} mt={4}>
                通話終了
              </Text>
            </Box>
          </Flex>
        </motion.div>
      </Box>
    );
  }

  return (
    <div>
      <Modal.Root
        onClose={() => {}}
        opened={!minimize && !!call}
        trapFocus={false}
      >
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header>
            {acceptCall ? (
              <ActionIcon
                className={classes.minimizeButton}
                color="queenBlue"
                onClick={() => setMinimize(true)}
                size={50}
                variant="filled"
              >
                <ArrowUpIcon color="#fff" />
              </ActionIcon>
            ) : (
              <Modal.CloseButton onClick={handleRejectCall} />
            )}
          </Modal.Header>
          <Modal.Body
            bg="#fff"
            maw={461}
            sx={{
              padding: '30px 30px 60px',
            }}
          >
            <Box>
              <Flex
                className={classes.avatarContainer}
                justify="center"
                pt={58}
              >
                <Avatar
                  alt=""
                  size={88}
                  src={isOutgoingCall ? receiverAvatar : callerAvatar}
                  sx={{ borderRadius: '50%' }}
                />
              </Flex>
              <Text
                align="center"
                className={classes.name}
                fw={700}
                fz={20}
                mt={20}
              >
                {isOutgoingCall ? receiverName : callerName}
              </Text>
              <Text align="center" className={classes.subText} fz={16} mt={12}>
                {!acceptCall
                  ? 'からの着信中です。'
                  : dayjs.duration(count * 1000).format('mm:ss')}
              </Text>
              <Flex
                align="center"
                className={classes.actionGroup}
                gap={60}
                justify="center"
                mt={28}
              >
                {!acceptCall ? (
                  <>
                    <Box>
                      <ActionIcon
                        className={`${classes.button} ${classes.redButton}`}
                        onClick={() => {
                          call?.reject();
                        }}
                        size={72}
                        variant="filled"
                      >
                        <CloseIcon color="#fff" />
                      </ActionIcon>
                      <Text
                        align="center"
                        className={classes.buttonText}
                        fw={500}
                        fz={16}
                      >
                        拒否
                      </Text>
                    </Box>
                    <Box>
                      <ActionIcon
                        className={classes.button}
                        color="queenBlue"
                        onClick={handleAcceptCall}
                        size={72}
                        variant="filled"
                      >
                        <CheckIcon color="#fff" />
                      </ActionIcon>
                      <Text
                        align="center"
                        className={classes.buttonText}
                        fw={500}
                        fz={16}
                      >
                        応答
                      </Text>
                    </Box>
                  </>
                ) : (
                  <>
                    <Box>
                      <ActionIcon
                        className={classes.button}
                        color="queenBlue"
                        onClick={handleMute}
                        size={72}
                        variant={mute ? 'filled' : 'outline'}
                      >
                        <MuteIcon color={mute ? '#fff' : '#43749a'} />
                      </ActionIcon>
                      <Text
                        align="center"
                        className={classes.buttonText}
                        fw={500}
                        fz={16}
                      >
                        消音
                      </Text>
                    </Box>
                    <Box>
                      <ActionIcon
                        className={`${classes.button} ${classes.redButton}`}
                        onClick={handleHangUpCall}
                        size={72}
                      >
                        <HangupIcon color="#fff" />
                      </ActionIcon>
                      <Text
                        align="center"
                        className={classes.buttonText}
                        fw={500}
                        fz={16}
                      >
                        通話終了
                      </Text>
                    </Box>
                  </>
                )}
              </Flex>
            </Box>
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </div>
  );
};

export default CallNotificationModal;
