import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  approvalRateModalWrapper: {
    backgroundColor: theme.white,
    padding: '60px 30px 30px',
    textAlign: 'center',
    [theme.fn.smallerThan('sm')]: {
      padding: '40px 20px 20px',
      '.modal-icon': {
        width: 64,
        height: 64,
      },
    },
  },
  ratingContentContainer: {
    backgroundColor: theme.colors.ghostWhite[0],
    borderRadius: '6px',
    padding: 24,
    [theme.fn.smallerThan('sm')]: {
      padding: '16px 12px',
    },
  },
  ratingContentWrapper: {
    textAlign: 'left',
    '&:not(:last-of-type):after': {
      content: "''",
      display: 'block',
      height: 1,
      margin: '16px 0',
      backgroundColor: theme.colors.platinum[0],
      [theme.fn.smallerThan('sm')]: {
        margin: '12px 0',
      },
    },
    '.title': {
      marginBottom: 8,
      display: 'flex',
      alignItems: 'center',
      fontSize: 24,
      lineHeight: '34px',
      fontWeight: 700,
      color: theme.colors.queenBlue[0],
      [theme.fn.smallerThan('sm')]: {
        fontSize: 16,
        lineHeight: '24px',
        marginBottom: 4,
      },
      svg: {
        marginRight: 8,
        width: 30,
        height: 30,
        [theme.fn.smallerThan('sm')]: {
          width: 20,
          height: 20,
        },
      },
    },
    '.content': {
      fontWeight: 400,
      fontSize: 20,
      lineHeight: '32px',
      [theme.fn.smallerThan('sm')]: {
        fontSize: 14,
        lineHeight: '20px',
      },
    },
  },
}));

export default useStyles;
