import IconCancelCount from '@icons/icon-cancel-count.svg';
import IconClock from '@icons/icon-clock.svg';
import IconInfo from '@icons/icon-info-circle.svg';
import IconPhoneNotify from '@icons/icon-phone-notify.svg';
import type { ModalProps } from '@mantine/core';
import { Box, Modal, Text, Title } from '@mantine/core';
import React from 'react';

import useStyles from './styles';

interface TherapistAprrovalRateModalProps extends ModalProps {}

const TherapistApprovalRateModal: React.FC<TherapistAprrovalRateModalProps> = ({
  opened,
  ...rest
}) => {
  const { classes } = useStyles();
  return (
    <Modal
      classNames={{
        content: classes.approvalRateModalWrapper,
      }}
      opened={opened}
      size={630}
      {...rest}
    >
      <IconInfo className="modal-icon" />
      <Title
        fw={700}
        fz={{ base: 20, sm: 30 }}
        lh={{ base: '32px', sm: '40px' }}
        mb={8}
        mt={{ base: 16, sm: 24 }}
        order={4}
      >
        返答率・返答時間・承認後
        <br />
        キャンセル数について
      </Title>
      <Text
        color="blackOlive"
        fw={500}
        fz={{ base: 14, sm: 16 }}
        lh={{ base: '20px' }}
        mb={{ base: 16, sm: 24 }}
      >
        ※計測期間中は「--」と表示されます。
      </Text>
      <Box className={classes.ratingContentContainer}>
        <Box className={classes.ratingContentWrapper}>
          <Text className="title">
            <IconPhoneNotify />
            返答率
          </Text>
          <Text className="content">
            直近90日間で、セラピストがリクエストに対し返答した確率です。
          </Text>
        </Box>
        <Box className={classes.ratingContentWrapper}>
          <Text className="title">
            <IconClock />
            返答時間
          </Text>
          <Text className="content">
            リクエストに対し返答に要した時間の平均時間です。
          </Text>
        </Box>
        <Box className={classes.ratingContentWrapper}>
          <Text className="title">
            <IconCancelCount />
            承認後キャンセル数
          </Text>
          <Text className="content">
            直近90日間で、セラピストが確定予約に対してキャンセルした予約件数です。
          </Text>
        </Box>
      </Box>
    </Modal>
  );
};

export default TherapistApprovalRateModal;
