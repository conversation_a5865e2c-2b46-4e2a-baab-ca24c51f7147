const styles: Record<string, any> = {
  searchTherapistsModalWrapper: {
    display: 'flex',
    flexDirection: 'column',
    padding: '30px 15px 50px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
  },
  modalTitle: {
    display: 'flex',
    justifyContent: 'center',
    marginBottom: 36,
    fontWeight: 'bold',
    div: {
      position: 'relative',
      '@media (max-width: 768px)': {
        fontSize: 24,
      },
      '&:after': {
        content: '" "',
        borderRadius: 3,
        position: 'absolute',
        width: '110%',
        height: 6,
        background:
          'linear-gradient(160deg, #43749a 0%, #43749a 50%, #e8a62d 50%, #e8a62d 100%);',
        bottom: -6,
        left: -4,
      },
    },
  },
  fieldControl: {
    root: {
      border: '1.5px solid #dddddd',
      color: '#767676',
      backgroundColor: '#ffffff',
      borderRadius: 6,
      marginBottom: 10,
      padding: 20,
      fontSize: 14,
      svg: {
        color: '#000000',
      },
      '&:hover, &:focus': {
        borderColor: '#43749a',
      },
    },
    label: {
      display: 'flex',
      alignItems: 'center',
      flexWrap: 'wrap',
      gap: 16,
      fontWeight: 'bold',
      svg: {
        width: 18,
        height: 18,
        flexShrink: 0,
      },
      span: {
        gap: 10,
        display: 'flex',
        alignItems: 'flex-end',
      },
    },
  },
  errorValidate: {
    color: '#bf2020',
    marginBottom: 10,
  },
  genderField: {
    '.mantine-InputWrapper-label': {
      padding: '0 15px 5px',
      fontSize: 16,
      display: 'flex',
      alignItems: 'center',
      gap: 10,
      color: '#727272',
      margin: 0,
      svg: {
        color: '#000000',
      },
    },
    '.mantine-Chip-root': {
      flexGrow: 1,
    },
    '.mantine-Chip-label': {
      height: 50,
      fontSize: 16,
    },
  },
  submitBtn: {
    height: 50,
    maxWidth: 180,
    fontSize: 16,
    borderRadius: 4,
    width: '100%',
    margin: '40px auto 0',
  },
};

export default styles;
