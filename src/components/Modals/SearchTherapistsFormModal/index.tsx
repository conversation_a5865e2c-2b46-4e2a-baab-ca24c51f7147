import IconAvatar from '@icons/icon-avatar.svg';
import IconCalendar from '@icons/icon-calendar.svg';
import IconClock from '@icons/icon-clock.svg';
import IconLocation from '@icons/icon-location.svg';
import IconNote from '@icons/icon-note.svg';
import { Box, Button, Text } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import { openContextModal } from '@mantine/modals';
import { ChipField } from 'components/Form';
import type { SearchTherapistsFormValues } from 'components/Therapists/SearchForm';
import { isEmpty } from 'lodash';
import type { IMenuItem } from 'models/therapist';
import type { Control } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { GENDER_SEARCHING } from 'utils/constants';
import dayjs from 'utils/dayjs';

import styles from './styles';

type SearchTherapistsFormModalProps = {
  control: Control<SearchTherapistsFormValues>;
  menus?: IMenuItem[];
  onSubmit: (values: any) => void;
};

const SearchTherapistsFormModal = ({
  innerProps: { menus = [], onSubmit, control },
}: ContextModalProps<SearchTherapistsFormModalProps>) => {
  const dateBooking = useController({
    name: 'dateBooking',
    control,
  });
  const selectMenu = useController({
    name: 'selectMenu',
    control,
  });
  const areaNames = useController({
    name: 'areaNames',
    control,
  });

  const menuSelected: string[] = [];
  (menus || []).forEach((menu) => {
    if ((selectMenu.field.value || []).includes(menu.titleEn)) {
      menuSelected.push(menu.title);
    }
  });

  return (
    <Box
      component="form"
      onSubmit={onSubmit}
      sx={styles.searchTherapistsModalWrapper}
    >
      <Box sx={styles.modalTitle}>
        <Text size={24}>検索条件</Text>
      </Box>
      <Button
        onClick={() => {
          openContextModal({
            modal: 'DateTimePickerModal',
            size: 830,
            innerProps: {
              initialData: dateBooking.field.value,
              onConfirm: (date: string) => {
                dateBooking.field.onChange(date);
              },
            },
          });
        }}
        styles={styles.fieldControl}
        unstyled
      >
        <span>
          <IconCalendar />
          {dayjs(dateBooking.field.value).format('LL')}
        </span>
        <span>
          <IconClock />
          {dayjs(dateBooking.field.value).format('HH:mm')}
        </span>
      </Button>
      <Button
        onClick={() => {
          openContextModal({
            modal: 'LocationPickerModal',
            size: 830,
            innerProps: {
              initialData: areaNames.field.value,
              onConfirm: (data: any[]) => areaNames.field.onChange(data),
            },
          });
        }}
        styles={styles.fieldControl}
        unstyled
      >
        <span>
          <IconLocation />
          {(areaNames.field.value || []).map((area) => area.name).join(' ')}
        </span>
      </Button>
      {areaNames.fieldState?.error?.message && (
        <Text sx={styles.errorValidate}>
          {areaNames.fieldState.error.message}
        </Text>
      )}
      <Button
        mb={20}
        onClick={() => {
          openContextModal({
            modal: 'MenuSelectionModal',
            size: 768,
            innerProps: {
              menus,
              initialData: selectMenu.field.value,
              onConfirm: (data: any[]) => selectMenu.field.onChange(data),
            },
            styles: {
              header: {
                top: '15px !important',
                right: '15px !important',
              },
              close: {
                width: 30,
                height: 30,
                svg: {
                  width: 20,
                  height: 20,
                },
              },
            },
          });
        }}
        styles={styles.fieldControl}
        unstyled
      >
        <span>
          <IconNote />
          <Text lineClamp={1}>
            {isEmpty(menuSelected) ? 'メニューすべて' : menuSelected.join(', ')}
          </Text>
        </span>
      </Button>
      <ChipField
        control={control}
        inputWrapperProps={{ sx: styles.genderField }}
        label={
          <>
            <IconAvatar />
            セラピストの性別
          </>
        }
        name="gender"
        options={['1', '2', '0'].map((key) => ({
          value: key,
          children: GENDER_SEARCHING[key],
        }))}
      />
      <Button sx={styles.submitBtn} type="submit">
        検索する
      </Button>
    </Box>
  );
};

export default SearchTherapistsFormModal;
