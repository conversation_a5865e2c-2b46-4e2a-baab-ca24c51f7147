import IconCloseYellow from '@icons/icon-close-yellow.svg';
import type { ModalProps } from '@mantine/core';
import {
  Box,
  Button,
  Checkbox,
  Flex,
  Group,
  Loader,
  Modal,
  Text,
  Title,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import { useFetchData } from 'hooks';
import { isEmpty, keyBy } from 'lodash';
import type { IMenuItem, IPriceOption } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import helpers, { eventLog } from 'utils/helpers';

import { styles, sx } from './styles';

interface MenuSelectModalProps extends ModalProps {
  menus: IMenuItem[];
  handleChangeMenus: (menus: IMenuItem[]) => void;
  therapistId: string;
}

// Used for booking page selecting menu
const MenuSelectModal: React.FC<MenuSelectModalProps> = ({
  opened,
  onClose,
  menus = [],
  therapistId,
  handleChangeMenus,
}) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const [checked, setChecked] = useState<Record<string, IMenuItem>>({});
  const { data: therapistMenus = [], isLoading } = useFetchData<IMenuItem[]>({
    ...therapistQuery.getTherapistMenus({ id: therapistId }),
    staleTime: 1000 * 60 * 2,
    enabled: !!therapistId && opened,
  });

  useEffect(() => {
    if (opened && menus) {
      setChecked(keyBy(menus, '_id'));
    }
  }, [menus, opened]);

  const handleCloseModal = () => {
    onClose();
  };

  const handleOnCheck = (menu: IMenuItem, price: IPriceOption) => {
    const newChecked: Record<string, IMenuItem> = {
      ...checked,
      [menu._id]: {
        ...menu,
        selectedOption: price,
      },
    };
    setChecked(newChecked);
  };

  const handleOnRemove = (menu: IMenuItem) => {
    const newChecked: Record<string, IMenuItem> = {
      ...checked,
    };

    if (newChecked[menu._id]) {
      delete newChecked[menu._id];
      setChecked(newChecked);
    }
  };

  const handleOnConfirm = () => {
    const selectedMenus = Object.keys(checked).map(
      (key) => checked[key],
    ) as IMenuItem[];
    handleChangeMenus(selectedMenus);
    handleCloseModal();
  };

  return (
    <Modal
      centered
      fullScreen={mobileScreen}
      onClose={handleCloseModal}
      opened={opened}
      size={630}
      styles={styles.menuSelectModalWrapper}
    >
      <Box sx={sx.contentWrapper}>
        <Title className="title" color="blackOlive" mb={40} order={3} size={30}>
          メニュー追加・変更
        </Title>
        <Flex sx={sx.menuListWrapper}>
          {isLoading && (
            <Flex align="center" h={350} justify="center">
              <Loader size="xl" />
            </Flex>
          )}
          {therapistMenus.map((menu) => {
            const menuDurationPrice = checked[menu._id]
              ? `${
                  checked[menu._id]?.selectedOption?.duration
                }分 / ¥${helpers.numberFormat(
                  checked[menu._id]?.selectedOption?.price,
                )}`
              : `${menu.options[0]?.duration}分 / ¥${helpers.numberFormat(
                  menu.options[0]?.price,
                )} 〜`;
            return (
              <Checkbox
                checked={!isEmpty(checked[menu._id])}
                key={menu._id}
                label={
                  <Group
                    className="menu-label"
                    noWrap
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      eventLog('select_menu', {
                        therapist_id: therapistId,
                        menu_id: menu._id,
                      });
                      openContextModal({
                        modal: 'MenuPriceSelectModal',
                        centered: true,
                        size: 335,
                        zIndex: 202,
                        innerProps: {
                          priceOptions: menu.options,
                          priceSelected: checked[menu._id]?.selectedOption,
                          onSelect: (option: IPriceOption) =>
                            handleOnCheck(menu, option),
                        },
                        withCloseButton: false,
                      });
                    }}
                    spacing={0}
                  >
                    <IconCloseYellow
                      className="icon-close"
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleOnRemove(menu);
                      }}
                    />
                    <div className="menu-image">
                      <Image
                        alt="Menu image"
                        fill
                        sizes="15vw"
                        src={
                          menu.images?.large?.url || '/images/menu-default.webp'
                        }
                      />
                    </div>
                    <div className="menu-info">
                      <Text component="span" lineClamp={1}>
                        {menu.title}
                      </Text>
                      <Text component="span" lineClamp={1}>
                        {menuDurationPrice}
                      </Text>
                    </div>
                    <div
                      className="menu-info-icon"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        eventLog('view_menu', {
                          menu_id: menu._id,
                          menu_name: menu.title,
                          menu_type: menu.type,
                        });
                        openContextModal({
                          modal: 'MenuInfoModal',
                          size: 1340,
                          innerProps: {
                            menu,
                          },
                          zIndex: 202,
                          styles: styles.modalStyles,
                        });
                      }}
                    >
                      <Image
                        alt="Menu info icon"
                        height={24}
                        src="/icons/icon-info-1.svg"
                        width={24}
                      />
                    </div>
                  </Group>
                }
                onChange={(e) => {
                  if (e.target.checked) {
                    openContextModal({
                      modal: 'MenuPriceSelectModal',
                      size: 335,
                      centered: true,
                      innerProps: {
                        priceOptions: menu.options,
                        priceSelected: checked[menu._id]?.selectedOption,
                        onSelect: (option: IPriceOption) =>
                          handleOnCheck(menu, option),
                      },
                      withCloseButton: false,
                    });
                  } else {
                    handleOnRemove(menu);
                  }
                }}
                styles={styles.checkboxStyles}
                value={menu._id}
                wrapperProps={{
                  'data-checked': !isEmpty(checked[menu._id]),
                }}
              />
            );
          })}
        </Flex>
      </Box>
      <Flex gap={20} justify="space-between" sx={sx.btnGroup}>
        <Button
          color="grey"
          onClick={handleCloseModal}
          size="lg"
          variant="outline"
        >
          キャンセル
        </Button>
        <Button onClick={handleOnConfirm} size="lg">
          保存
        </Button>
      </Flex>
    </Modal>
  );
};

export default MenuSelectModal;
