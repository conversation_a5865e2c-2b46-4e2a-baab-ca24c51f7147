import type { MantineTheme, Sx } from '@mantine/core';
import type { Styles } from '@mantine/styles';

export const sx: Record<string, Sx> = {
  contentWrapper: {
    height: '100%',
    padding: '60px 30px 30px',
    overflow: 'auto',
    '@media (max-width: 768px)': {
      padding: '86px 20px 98px',
      height: 'calc(100%)',
    },
    '.title': {
      textAlign: 'center',
      '@media (max-width: 768px)': {
        textAlign: 'left',
        position: 'absolute',
        zIndex: 1,
        backgroundColor: 'white',
        top: 0,
        left: 0,
        width: '100%',
        boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
        height: 60,
        display: 'flex',
        alignItems: 'center',
        fontSize: 18,
        padding: '0 42px 0 20px',
      },
    },
  },
  menuListWrapper: {
    gap: 16,
    flexDirection: 'column',
  },
  btnGroup: {
    width: '100%',
    backgroundColor: 'white',
    position: 'sticky',
    bottom: 0,
    left: 0,
    padding: 20,
    boxShadow: '0 -4px 8px 0 rgba(0, 0, 0, 0.06)',
    zIndex: 1,
    '@media (max-width: 768px)': {
      gap: 15,
      padding: '16px 20px',
      position: 'fixed',
    },
    button: {
      flex: '1 1 50%',
    },
  },
};

export const styles: Record<string, Styles<string, any>> = {
  menuSelectModalWrapper: (theme: MantineTheme) => ({
    inner: {
      padding: '0px !important',
    },
    content: {
      '@media (max-width: 768px)': {
        height: theme.other?.viewHeight,
      },
    },
    body: {
      background: 'white',
      height: '100%',
    },
  }),
  checkboxStyles: {
    root: {
      display: 'flex',
      '&[data-checked=true]': {
        '.mantine-Checkbox-labelWrapper': {
          borderColor: '#dca338',
          borderWidth: 2,
          position: 'relative',
          '@media (max-width: 768px)': {
            borderWidth: 1,
          },
          '.menu-info span:nth-of-type(2)': {
            color: '#dca338',
            fontWeight: 'bold',
          },
          '.icon-close': {
            display: 'block',
          },
        },
      },
    },
    inner: {
      display: 'none',
    },
    input: {
      cursor: 'pointer',
      borderRadius: 0,
      borderWidth: 1.5,
    },
    body: {
      width: '100%',
      alignItems: 'center',
    },
    labelWrapper: {
      borderRadius: 6,
      border: '1px solid #d1d1d1',
      width: '100%',
      boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    },
    label: {
      padding: 0,
      position: 'relative',
      backgroundColor: '#ffffff',
      borderRadius: 6,
      '.icon-close': {
        position: 'absolute',
        top: 0,
        left: 0,
        transform: 'translate(-50%, -50%) scale(1.5)',
        zIndex: 1,
        display: 'none',
        '@media (max-width: 768px)': {
          transform: 'translate(-50%, -50%)',
        },
      },
      '.menu-label': {
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
        '@media (max-width: 768px)': {
          fontSize: 14,
        },
      },
      '.menu-image': {
        flexShrink: 0,
        position: 'relative',
        aspectRatio: '1.3',
        minHeight: 120,
        alignSelf: 'stretch',
        '@media (max-width: 768px)': {
          minHeight: 55,
        },
        img: {
          borderTopLeftRadius: 6,
          borderBottomLeftRadius: 6,
          objectFit: 'cover',
        },
      },
      '.menu-info': {
        flex: '1 1 auto',
        display: 'flex',
        flexDirection: 'column',
        padding: '0 8px 0 20px',
        gap: 0,
        span: {
          color: '#696969',
          fontSize: 18,
          fontWeight: 'bold',
          '@media (max-width: 768px)': {
            fontSize: 14,
          },
          '&:nth-of-type(2)': {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#909090',
            '@media (max-width: 768px)': {
              fontSize: 12,
            },
          },
        },
      },
      '.menu-info-icon': {
        flexShrink: 0,
        marginRight: '20px',
        '@media (max-width: 768px)': {
          img: {
            width: 16,
            height: 16,
          },
        },
      },
    },
  },
  modalStyles: {
    header: {
      top: 65,
      right: 30,
      '@media (max-width: 768px)': {
        top: 15,
        right: 15,
      },
    },
    close: {
      background: '#ffffff !important',
      color: '#43749a !important',
      '@media (max-width: 768px)': {
        backgroundColor: '#43749a !important',
        color: '#ffffff !important',
      },
    },
  },
};
