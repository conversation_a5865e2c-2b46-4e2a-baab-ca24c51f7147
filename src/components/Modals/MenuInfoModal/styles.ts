import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  modalWrapper: {
    width: '100%',
    padding: '115px 32px',
    backgroundColor: 'rgba(67, 116, 154, 0.9)',
    borderRadius: 6,

    '@media (max-width: 768px)': {
      padding: 0,
    },
  },
  modalContent: {
    display: 'flex',
    gap: '20px 50px',
    padding: 0,
    '@media (max-width: 768px)': {
      flexDirection: 'column',
    },
  },
  menuImage: {
    position: 'relative',
    flex: '0 0 470px',
    '@media (max-width: 768px)': {
      flex: '1 1 auto',
    },
    '&:before': {
      content: '""',
      height: 0,
      display: 'block',
      paddingTop: 'calc(390 / 470 * 100%)',
      '@media (max-width: 768px)': {
        paddingTop: 'calc(180 / 335 * 100%)',
      },
    },
    img: {
      position: 'absolute',
      height: '100%',
      width: '100%',
      objectFit: 'cover',
      borderRadius: 6,
      '@media (max-width: 768px)': {
        borderRadius: '6px 6px 0 0',
      },
    },
  },
  menuContent: {
    flex: '1 1 auto',
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    gap: 24,
    color: '#FFFFFF',
    fontSize: 18,
    '@media (max-width: 768px)': {
      padding: '0 15px 26px',
      fontSize: 12,
      gap: 14,
    },
    'span:first-of-type': {
      fontSize: 30,
      fontWeight: 'bold',
      '@media (max-width: 768px)': {
        fontSize: 16,
      },
    },
  },
};
