import { Box, Container, Text } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import type { IMenuItem } from 'models/therapist';
import Image from 'next/image';

import { sx } from './styles';

type MenuInfoModalProps = {
  menu: IMenuItem;
};

const MenuInfoModal = ({
  innerProps,
}: ContextModalProps<MenuInfoModalProps>) => {
  const { menu } = innerProps;

  return (
    <Box sx={sx.modalWrapper}>
      <Container size={1240} sx={sx.modalContent}>
        <Box sx={sx.menuImage}>
          <Image
            alt="Menu image"
            fill
            src={menu?.images?.large?.url || '/images/menu-default.webp'}
          />
        </Box>
        <Text sx={sx.menuContent}>
          <span>{menu.title}</span>
          <span>{menu.detail}</span>
        </Text>
      </Container>
    </Box>
  );
};

export default MenuInfoModal;
