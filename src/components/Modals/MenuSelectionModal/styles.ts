import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  menuSelectionModalWrapper: {
    padding: '30px 15px',
    backgroundColor: '#FFFFFF',
    borderRadius: 6,
  },
  modalTitle: {
    textAlign: 'center',
  },
  back: {
    color: '#000000',
    marginBottom: 10,
    marginTop: 10,
    cursor: 'pointer',
    display: 'inline-block',
  },
  confirmBtn: {
    height: 40,
    maxWidth: 140,
    fontSize: 16,
    width: '100%',
    margin: '20px auto 0',
    display: 'inherit',
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  checkBoxStyles: {
    input: {
      border: '1.5px solid #b2b2b2',
      borderRadius: 0,
    },
    body: {
      alignItems: 'center',
    },
    labelWrapper: {
      display: 'flex',
      width: '100%',
    },
    label: {
      display: 'flex',
      alignItems: 'center',
      color: '#696969',
      fontWeight: 'bold',
      width: '100%',
      img: {
        marginRight: 12,
        objectFit: 'cover',
      },
    },
  },
  selectAllBtn: {
    root: {
      color: '#ffffff',
      position: 'relative',
      height: 45,
      fontSize: 16,
    },
    rightIcon: {
      position: 'absolute',
      right: 30,
    },
  },
};
