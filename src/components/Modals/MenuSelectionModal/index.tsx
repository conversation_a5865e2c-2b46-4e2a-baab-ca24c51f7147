import IconChevronRight from '@icons/icon-chevron-right.svg';
import { Box, Button, Checkbox, Flex, Text, Title } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import type { IMenuItem } from 'models/therapist';
import Image from 'next/image';
import { useEffect, useState } from 'react';

import { styles, sx } from './styles';

type MenuSelectionModalProps = {
  initialData: string[];
  menus?: IMenuItem[];
  onConfirm: (values: string[]) => void;
};

const MenuSelectionModal = ({
  context,
  id,
  innerProps: { menus = [], onConfirm, initialData },
}: ContextModalProps<MenuSelectionModalProps>) => {
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    setSelected(initialData.filter((n) => n));
  }, [initialData]);

  return (
    <Box sx={sx.menuSelectionModalWrapper}>
      <Title order={3} size={24} sx={sx.modalTitle}>
        施術メニュー
      </Title>
      <Text
        onClick={() => {
          context.closeModal(id);
        }}
        size={14}
        sx={sx.back}
        variant="link"
      >
        <IconChevronRight
          style={{ transform: 'rotate(180deg)', marginRight: 14 }}
        />
        検索条件
      </Text>
      <Button
        fullWidth
        onClick={() => {
          onConfirm([]);
          context.closeModal(id);
        }}
        rightIcon={<IconChevronRight />}
        styles={styles.selectAllBtn}
      >
        メニューすべて
      </Button>
      <Checkbox.Group
        onChange={(value) => setSelected(value)}
        pt={10}
        value={selected}
      >
        <Flex direction="column" gap={20}>
          {(menus || []).map((menu) => {
            return (
              <Checkbox
                key={menu._id}
                label={
                  <>
                    <Image
                      alt="Menu image"
                      height={50}
                      src={
                        menu.images?.small?.url || '/images/menu-default.webp'
                      }
                      width={70}
                    />
                    <span>{menu.title}</span>
                  </>
                }
                styles={styles.checkBoxStyles}
                value={menu.titleEn}
              />
            );
          })}
        </Flex>
      </Checkbox.Group>
      <Button
        onClick={() => {
          onConfirm(selected);
          context.closeModal(id);
        }}
        sx={sx.confirmBtn}
      >
        次へ
      </Button>
    </Box>
  );
};

export default MenuSelectionModal;
