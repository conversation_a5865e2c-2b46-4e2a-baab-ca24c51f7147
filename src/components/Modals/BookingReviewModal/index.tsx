import { Avatar, Box, Container, Flex, Rating, Text } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import type { ITherapistItem } from 'models/therapist';
import { useRouter } from 'next/router';

import { styles, sx } from './styles';

type BookingReviewModalProps = {
  bookingId: string;
  therapist: ITherapistItem;
};

const BookingReviewModal = ({
  id,
  context,
  innerProps,
}: ContextModalProps<BookingReviewModalProps>) => {
  const { therapist, bookingId } = innerProps;
  const router = useRouter();

  const handleRating = (rating: number) => {
    context.closeModal(id);
    router.push(
      `/booking/${bookingId}/review?rating=${rating}&ref=PopupReminder`,
    );
  };

  return (
    <Box sx={sx.modalWrapper}>
      <Container p={0} size={630}>
        <Flex align="center" direction="column" gap={30} sx={sx.content}>
          <Avatar
            alt={therapist?.nickName}
            radius="50%"
            size={88}
            src={
              therapist?.avatar || '/icons/icon-avatar-default-therapist.svg'
            }
            styles={styles.avatar}
          />
          <Text align="center" fw="bold">
            ご利用ありがとうございました
          </Text>
          <Text align="center" color="blackOlive" component="span">
            セラピストはいかがでしたか?
            <br />
            星を選んで評価しましょう
          </Text>
          <Rating
            color="#e8e8e8"
            onChange={handleRating}
            styles={styles.rating}
            value={0}
          />
        </Flex>
      </Container>
    </Box>
  );
};

export default BookingReviewModal;
