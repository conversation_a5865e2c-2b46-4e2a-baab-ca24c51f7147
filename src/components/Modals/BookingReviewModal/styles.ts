import type { CSSObject } from '@emotion/react';
import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  modalWrapper: {
    padding: '60px 30px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    fontSize: 30,
    span: {
      fontSize: 20,
    },
    '@media (max-width: 768px)': {
      padding: '32px 14px',
      fontSize: 20,
      span: {
        fontSize: 14,
        marginTop: -4,
      },
    },
  },

  content: {
    '@media (max-width: 768px)': {
      gap: 14,
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  rating: {
    symbolBody: {
      cursor: 'pointer',
      svg: {
        stroke: '#e8e8e8',
        fill: '#e8e8e8',
        width: 50,
        height: 50,
      },
      '@media (max-width: 768px)': {
        svg: {
          width: 30,
          height: 30,
        },
      },
    },
  },

  avatar: {
    root: {
      '@media (max-width: 768px)': {
        width: 55,
        minWidth: 55,
        height: 55,
      },
    },
  },
};
