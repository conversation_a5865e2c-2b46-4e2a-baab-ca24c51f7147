import type { CSSObject } from '@emotion/react';
import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  alertModalWrapper: {
    padding: '60px 20px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    position: 'relative',
    textAlign: 'center',
    '@media (max-width: 768px)': {
      padding: '40px 20px',
    },
  },
  icon: {
    width: 88,
    height: 88,
    marginBottom: 24,
    svg: {
      width: 88,
      height: 88,
    },
    '@media (max-width: 768px)': {
      marginBottom: 16,
      width: 64,
      height: 64,
      svg: {
        width: 64,
        height: 64,
      },
    },
  },
  title: {
    fontSize: 30,
    '@media (max-width: 768px)': {
      fontSize: 20,
    },
  },
  content: {
    fontSize: 20,
    fontWeight: 400,
    lineHeight: '32px',
    '@media (max-width: 768px)': {
      fontSize: 14,
      lineHeight: '20px',
    },
  },
  rememberAnswerCheckbox: {
    textAlign: 'center',
    '.mantine-Checkbox-body': {
      justifyContent: 'center',
      alignItems: 'center',
      '.mantine-Checkbox-input': {
        borderRadius: 2,
      },
      '.mantine-Checkbox-label': {
        paddingLeft: 16,
        fontSize: 20,
        fontWeight: 400,
        lineHeight: '32px',
        color: '#3C3C3C',
        '@media (max-width: 768px)': {
          fontSize: 14,
          lineHeight: '20px',
          paddingLeft: 8,
        },
      },
    },
  },
  btnGroup: {
    marginTop: 40,
    width: '100%',
    '@media (max-width: 768px)': {
      marginTop: 24,
      gap: 9,
    },
    button: {
      flex: '1 1 auto',
      height: 60,
      fontSize: 18,
      maxWidth: 300,
      '@media (max-width: 768px)': {
        height: 40,
        fontSize: 14,
        maxWidth: 180,
      },
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {};
