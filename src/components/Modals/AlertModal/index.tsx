import IconWarning from '@icons/icon-warning-circle.svg';
import type { ButtonProps } from '@mantine/core';
import { Box, Button, Checkbox, Flex, Text, Title } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import { useState } from 'react';

import { sx } from './styles';

type AlertModalProps = {
  title?: string;
  content?: string;
  rememberCheckboxText?: string;
  className?: string;
  icon?: React.ReactNode;
  onConfirm?: (remember?: boolean) => void;
  onClose?: () => void;
  confirmText?: string;
  cancelText?: string;
  haveActionBtn?: boolean;
  hasOkBtn?: boolean;
  confirmButtonProps?: ButtonProps;
};

const AlertModal = ({
  context,
  id,
  innerProps: {
    title,
    content,
    rememberCheckboxText,
    icon = <IconWarning />,
    className,
    onConfirm,
    onClose,
    confirmText,
    cancelText,
    haveActionBtn = false,
    hasOkBtn = false,
    confirmButtonProps,
  },
}: ContextModalProps<AlertModalProps>) => {
  const [remember, setRemember] = useState(false);
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
    context.closeModal(id);
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm(remember);
    }
    context.closeModal(id);
  };

  return (
    <Box
      className={`modal-container ${className || ''}`}
      sx={sx.alertModalWrapper}
    >
      {icon && (
        <Box className="modal-icon" sx={sx.icon}>
          {icon}
        </Box>
      )}
      <Flex
        className="modal-info"
        direction="column"
        gap={{ base: 16, sm: 30 }}
      >
        {title && (
          <Title className="modal-title" color="black" order={3} sx={sx.title}>
            {title}
          </Title>
        )}
        {content && (
          <Text className="modal-content" color="blackOlive" sx={sx.content}>
            {content}
          </Text>
        )}
        {rememberCheckboxText && (
          <Checkbox
            checked={remember}
            className="remember-answer-checkbox"
            label="次回から表示しない"
            onChange={(e) => setRemember(e.currentTarget.checked)}
            sx={sx.rememberAnswerCheckbox}
          />
        )}
      </Flex>
      {haveActionBtn && (
        <Flex
          className="modal-btn-group"
          gap={16}
          justify="center"
          sx={sx.btnGroup}
        >
          <Button
            color="grey"
            fullWidth
            onClick={handleClose}
            variant="outline"
          >
            {cancelText || 'いいえ'}
          </Button>
          <Button fullWidth onClick={handleConfirm}>
            {confirmText || 'はい'}
          </Button>
        </Flex>
      )}
      {hasOkBtn && (
        <Flex
          className="modal-btn-group"
          gap={16}
          justify="center"
          sx={sx.btnGroup}
        >
          <Button fullWidth onClick={handleConfirm} {...confirmButtonProps}>
            {confirmText || 'OK'}
          </Button>
        </Flex>
      )}
    </Box>
  );
};

export default AlertModal;
