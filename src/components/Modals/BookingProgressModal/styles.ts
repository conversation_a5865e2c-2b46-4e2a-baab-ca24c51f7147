import type { CSSObject } from '@emotion/react';
import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  modalWrapper: {
    padding: '85px 30px 60px 100px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    '@media (max-width: 768px)': {
      padding: '65px 30px 50px 30px',
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  timelineStyles: {
    root: {
      '@media (max-width: 768px)': {
        paddingLeft: 'calc(25px / 2 + 10px / 2)',
      },
    },
    item: {
      padding: '14px 0 0 100px',
      '@media (max-width: 768px)': {
        padding: '0 0 0 30px',
      },
      '&:not(:first-of-type)': {
        marginTop: 34,
        '@media (max-width: 768px)': {
          marginTop: 30,
        },
      },
      '&:before': {
        left: -8,
        bottom: -34,
        borderLeft: '2px dashed #305c7e',
        '@media (max-width: 768px)': {
          left: -6,
          bottom: -30,
        },
      },
      '&:after': {
        content: '""',
        width: 56,
        display: 'block',
        position: 'absolute',
        top: 24,
        left: 4,
        borderTop: '2px dashed #305c7e',
        '@media (max-width: 768px)': {
          display: 'none',
        },
      },
    },
    itemBody: {
      '@media (max-width: 768px)': {
        display: 'flex',
        flexDirection: 'column-reverse',
        '.timestamp': {
          fontSize: 13,
        },
      },
    },
    itemBullet: {
      borderColor: '#305c7e',
      backgroundColor: '#305c7e',
      '&[data-active=true]': {
        borderColor: '#305c7e',
        backgroundColor: 'white',
      },
      '@media (max-width: 768px)': {
        left: 'calc(-25px / 2 - 10px / 2)',
        width: 25,
        height: 25,
        borderWidth: 10,
      },
    },
    itemTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      '@media (max-width: 768px)': {
        margin: '4px 0 0 0',
        fontSize: 15,
      },
    },
  },
};
