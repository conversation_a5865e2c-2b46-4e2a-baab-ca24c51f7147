import { Box, Container, Text, Timeline } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import { BOOKING_PROGRESS_STATUS } from 'utils/constants';
import dayjs from 'utils/dayjs';

import { styles, sx } from './styles';

type BookingProgressModalProps = {
  currentStatus: string;
  statusHistory: {
    status: string;
    reason: string;
    timestamp: string;
  }[];
};

const BookingProgressModal = ({
  innerProps,
}: ContextModalProps<BookingProgressModalProps>) => {
  const { currentStatus, statusHistory = [] } = innerProps;
  const history = [...statusHistory].reverse();

  const renderHistory = () => {
    if (currentStatus === 'CANCELED' || currentStatus === 'DONE') {
      return history.map((context) => {
        const title =
          context.status === 'DONE' &&
          context.reason === 'finishWithoutTreatment'
            ? '決済完了'
            : BOOKING_PROGRESS_STATUS[context.status];
        return (
          <Timeline.Item
            key={context.status}
            lineVariant="dashed"
            title={title}
          >
            <Text className="timestamp" color="blackOlive" size={16}>
              {context?.timestamp
                ? dayjs(context?.timestamp).format('YYYY/MM/DD HH:mm')
                : '—:—'}
            </Text>
          </Timeline.Item>
        );
      });
    }
    return ['PENDING', 'CONFIRMED', 'ARRIVED', 'DONE'].map((key, index) => {
      return (
        <Timeline.Item
          key={key}
          lineVariant="dashed"
          title={BOOKING_PROGRESS_STATUS[key]}
        >
          <Text className="timestamp" color="blackOlive" size={16}>
            {history[index]?.timestamp
              ? dayjs(history[index]?.timestamp).format('YYYY/MM/DD HH:mm')
              : '—:—'}
          </Text>
        </Timeline.Item>
      );
    });
  };
  return (
    <Box sx={sx.modalWrapper}>
      <Container p={0} size={630}>
        <Timeline
          active={statusHistory.length - 1}
          bulletSize={50}
          lineWidth={15}
          styles={styles.timelineStyles}
        >
          {renderHistory()}
        </Timeline>
      </Container>
    </Box>
  );
};

export default BookingProgressModal;
