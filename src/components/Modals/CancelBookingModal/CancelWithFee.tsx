import { Box, Button, Flex, Text, Textarea, Title } from '@mantine/core';
import React, { useState } from 'react';
import helpers from 'utils/helpers';

import { styles, sx } from './styles';

const CancelWithFee = ({
  fee,
  onSubmit,
  onCancel,
  onConfirm,
  cancelBookingSuccess,
  isCancelling,
  isSendingReason,
}: {
  fee: number;
  onSubmit: ({ reasons, note }: { reasons: string[]; note?: string }) => void;
  onCancel: () => void;
  onConfirm: () => void;
  cancelBookingSuccess: boolean;
  isCancelling: boolean;
  isSendingReason: boolean;
}) => {
  const [note, setNote] = useState('');

  if (cancelBookingSuccess) {
    return (
      <Box className="cancel-without-fee" sx={sx.cancelContent}>
        <Title className="title" color="blackOlive" size={30}>
          キャンセル理由を
          <br />
          お聞かせください
        </Title>
        <Textarea
          autosize
          description={`${note.length}/200`}
          inputWrapperOrder={['label', 'input', 'description']}
          label="セラピストへのメッセージ"
          maxLength={200}
          maxRows={6}
          minRows={6}
          onChange={(event) => setNote(event.currentTarget.value)}
          placeholder="任意"
          styles={styles.textareaStyles}
          value={note}
        />
        <Button
          className="submit-btn"
          color="marigold"
          disabled={!note.trim()}
          loading={isSendingReason}
          onClick={() => {
            if (!note.trim()) return;
            onSubmit({
              reasons: [],
              note: note.trim() ? note.trim() : undefined,
            });
          }}
        >
          送る
        </Button>
        <Box>
          <Text
            className="skip-btn"
            onClick={onCancel}
            size={16}
            tabIndex={0}
            weight="bold"
          >
            スキップ
          </Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={sx.cancelContent}>
      <Title className="title" color="blackOlive" size={30}>
        ご確認
      </Title>
      <Text className="description" color="blackOlive" size={20}>
        この予約をキャンセルした場合、施術代金の全額
        <b>￥{helpers.numberFormat(fee)}</b>を
        <br />
        キャンセル料として頂戴いたします。
      </Text>
      <Flex sx={sx.actionBtnGroup}>
        <Button color="grey" onClick={onCancel} variant="outline">
          戻る
        </Button>
        <Button color="marigold" loading={isCancelling} onClick={onConfirm}>
          キャンセルする
        </Button>
      </Flex>
    </Box>
  );
};

export default CancelWithFee;
