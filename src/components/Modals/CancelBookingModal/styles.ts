import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  cancelBookingModalWrapper: {
    padding: '60px 20px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    position: 'relative',
    textAlign: 'center',
    '@media (max-width: 768px)': {
      padding: '40px 20px 30px',
    },
  },
  icon: (theme) => ({
    backgroundColor: theme.colors.queenBlue[6],
    width: 88,
    height: 88,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    '@media (max-width: 768px)': {
      marginBottom: 20,
    },
  }),
  cancelContent: (theme) => ({
    width: '100%',
    '.title': {
      marginBottom: 30,
      fontSize: 30,
      '@media (max-width: 768px)': {
        marginBottom: 20,
        fontSize: 20,
      },
    },
    '.description': {
      fontSize: 20,
      wordBreak: 'break-word',
      a: {
        color: theme.colors.queenBlue[6],
        textDecoration: 'underline',
        textUnderlineOffset: 5,
      },
      '@media (max-width: 768px)': {
        fontSize: 13,
      },
    },
    '.confirm-btn': {
      marginTop: 45,
      maxWidth: 300,
      width: '100%',
      height: 60,
      fontSize: 18,
      '@media (max-width: 768px)': {
        height: 50,
        marginTop: 20,
      },
    },
    '.submit-btn': {
      marginTop: 40,
      maxWidth: 300,
      width: '100%',
      height: 60,
      fontSize: 18,
      '@media (max-width: 768px)': {
        height: 40,
        maxWidth: 180,
        fontSize: 16,
        marginTop: 20,
      },
    },
    '.skip-btn': {
      color: theme.colors.queenBlue[6],
      textDecoration: 'underline',
      textUnderlineOffset: 5,
      marginTop: 30,
      cursor: 'pointer',
      display: 'inline-block',
      '@media (max-width: 768px)': {
        fontSize: 13,
      },
    },
    '&.cancel-without-fee': {
      maxWidth: 431,
    },
  }),
  actionBtnGroup: {
    marginTop: 40,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    gap: 20,
    '@media (max-width: 768px)': {
      marginTop: 20,
      gap: 16,
      flexDirection: 'column-reverse',
    },
    button: {
      maxWidth: 275,
      width: '100%',
      height: 60,
      fontSize: 18,
      '@media (max-width: 768px)': {
        height: 40,
        fontSize: 16,
        maxWidth: 180,
      },
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  reasonList: {
    root: {
      '.mantine-CheckboxGroup-description': {
        fontSize: 20,
        color: '#3C3C3C',
        fontWeight: 'bold',
        textAlign: 'left',
        marginBottom: 10,
        '@media (max-width: 768px)': {
          fontSize: 13,
        },
      },
    },
  },
  checkboxStyles: {
    label: {
      fontSize: 20,
      padding: 0,
      marginLeft: 20,
      cursor: 'pointer',
      '@media (max-width: 768px)': {
        fontSize: 13,
        marginLeft: 10,
      },
    },
    input: {
      width: 24,
      height: 24,
      borderRadius: 0,
      cursor: 'pointer',
      '&:checked': {
        backgroundColor: '#3C3C3C',
        borderColor: '#3C3C3C',
      },
      '@media (max-width: 768px)': {
        width: 18,
        height: 18,
      },
    },
    body: {
      alignItems: 'center',
    },
    inner: {
      width: 24,
      height: 24,
      '@media (max-width: 768px)': {
        width: 18,
        height: 18,
      },
    },
  },
  textareaStyles: {
    root: {
      '&.other-reason': {
        marginTop: 16,
        '@media (max-width: 768px)': {
          marginTop: 10,
        },
      },
    },
    label: {
      display: 'block',
      fontSize: 16,
      textAlign: 'left',
      color: '#3c3c3c',
      fontWeight: 'bold',
      marginBottom: 8,
      '@media (max-width: 768px)': {
        fontSize: 13,
      },
    },
    input: {
      fontSize: 13,
      '&::placeholder': {
        fontSize: 13,
        color: '#767676',
      },
    },
    description: {
      textAlign: 'end',
      color: 'black',
      fontSize: 12,
    },
  },
};
