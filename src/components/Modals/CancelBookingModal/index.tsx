import IconFeedback from '@icons/icon-feedback.svg';
import { Box, Button, Text, Title } from '@mantine/core';
import type { ContextModalProps } from '@mantine/modals';
import { useCountdown, useFetchData, useMutate } from 'hooks';
import type { IBookingDetail } from 'models/booking';
import { bookingQuery } from 'models/booking';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import type { BOOKING_STATUSES } from 'utils/constants';
import { BOOKING_CATEGORIES } from 'utils/constants';
import { eventLog } from 'utils/helpers';

import CancelWithFee from './CancelWithFee';
import CancelWithoutFee from './CancelWithoutFee';
import { sx } from './styles';

type CancelBookingModalProps = {
  onConfirm: () => void;
  onClose?: () => void;
  fee: number;
  expiryCancelingBooking: number;
  bookingStatus: BOOKING_STATUSES;
  bookingCategory: BOOKING_CATEGORIES;
  bookingId: string;
};

const CancelBookingModal = ({
  context,
  id,
  innerProps: {
    fee,
    expiryCancelingBooking,
    bookingStatus,
    bookingId,
    bookingCategory,
  },
}: ContextModalProps<CancelBookingModalProps>) => {
  const [confirm, setConfirm] = useState(false);
  const {
    count,
    start: startCount,
    isDone,
  } = useCountdown({
    countStart: expiryCancelingBooking || 15,
  });

  const { refetch: refetchDetail } = useFetchData<IBookingDetail>({
    ...bookingQuery.getBookingDetail({ bookingId }),
    enabled: false,
  });
  const { mutateAsync: cancelBookingFn, isLoading: isSendingReason } =
    useMutate(bookingQuery.cancelBooking);
  const {
    mutateAsync: changeBookingStatus,
    isLoading,
    isSuccess,
  } = useMutate(bookingQuery.changeBookingStatus);

  useEffect(() => {
    startCount();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isDone && !confirm) {
      context.closeModal(id);
      eventLog('decline_cancel');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDone]);

  const hanldeCancel = () => {
    context.closeModal(id);
  };

  const handleCancelBooking = async () => {
    await changeBookingStatus({
      bookingId,
      newStatus: 'canceled',
    });
    eventLog('cancel_reservation', {
      booking_id: bookingId,
    });
    await refetchDetail();
  };

  const handleSendReason = async ({
    reasons,
    note,
  }: {
    reasons: string[];
    note?: string;
  }) => {
    await cancelBookingFn({
      bookingId,
      reasons,
      ...(bookingCategory === BOOKING_CATEGORIES.REQUEST
        ? {
            note,
          }
        : {
            noteForTherapist: note,
          }),
    });
    eventLog('cancellation_survey', {
      booking_id: bookingId,
      type: fee > 0 ? 'fee' : 'without fee',
      options: fee > 0 ? 'Null' : reasons,
      additional_note: note,
    });
    hanldeCancel();
  };

  const handleConfirmCancel = async () => {
    if (fee > 0) {
      setConfirm(true);
      return;
    }
    handleCancelBooking();
    setConfirm(true);
  };

  const renderContent = () => {
    if (confirm) {
      if (fee > 0) {
        return (
          <CancelWithFee
            cancelBookingSuccess={isSuccess}
            fee={fee}
            isCancelling={isLoading}
            isSendingReason={isSendingReason}
            onCancel={hanldeCancel}
            onConfirm={handleCancelBooking}
            onSubmit={handleSendReason}
          />
        );
      }
      return (
        <CancelWithoutFee
          bookingStatus={bookingStatus}
          isSendingReason={isSendingReason}
          onCancel={hanldeCancel}
          onSubmit={handleSendReason}
        />
      );
    }
    return (
      <Box sx={sx.cancelContent}>
        <Title className="title" color="blackOlive" size={30}>
          ご確認
        </Title>
        <Text className="description" color="blackOlive" size={20}>
          <Link
            href={`${process.env.NEXT_PUBLIC_LP_URL}/terms-of-use/cancelation-policy.html`}
            rel="noreferrer"
            target="_blank"
          >
            キャンセルポリシー
          </Link>
          を確認の上、 <br />
          この予約をキャンセルしますか？
        </Text>
        <Button
          className="confirm-btn"
          color="marigold"
          loading={isLoading}
          onClick={handleConfirmCancel}
        >
          キャンセルする（00:{count < 10 ? `0${count}` : count}）
        </Button>
      </Box>
    );
  };

  return (
    <Box sx={sx.cancelBookingModalWrapper}>
      <Box sx={sx.icon}>
        <IconFeedback />
      </Box>
      {renderContent()}
    </Box>
  );
};

export default CancelBookingModal;
