import {
  Box,
  Button,
  Checkbox,
  Flex,
  Loader,
  Text,
  Textarea,
  Title,
} from '@mantine/core';
import { useFetchData } from 'hooks';
import { bookingQuery } from 'models/booking';
import React, { useEffect, useState } from 'react';
import { BOOKING_STATUSES } from 'utils/constants';

import { styles, sx } from './styles';

const CancelWithoutFee = ({
  bookingStatus,
  onSubmit,
  onCancel,
  isSendingReason,
}: {
  bookingStatus: string;
  onSubmit: ({ reasons, note }: { reasons: string[]; note?: string }) => void;
  onCancel: () => void;
  isSendingReason: boolean;
}) => {
  const [reasons, setReasons] = useState<string[]>([]);
  const [note, setNote] = useState('');
  const { data, isLoading } = useFetchData<{ key: string; text: string }[]>(
    bookingQuery.getCancellationReason,
  );

  useEffect(() => {
    if (!reasons.includes('other')) {
      setNote('');
    }
  }, [reasons]);

  return (
    <Box className="cancel-without-fee" sx={sx.cancelContent}>
      <Title className="title" color="blackOlive" size={30}>
        キャンセル理由を
        <br />
        お聞かせください
      </Title>
      {bookingStatus === BOOKING_STATUSES.PENDING ? (
        <>
          {isLoading && (
            <Flex align="center" h={150} justify="center">
              <Loader />
            </Flex>
          )}
          <Checkbox.Group
            description="理由を選択してください"
            onChange={(value) => setReasons(value)}
            styles={styles.reasonList}
            value={reasons}
          >
            <Flex direction="column" gap={20}>
              {data?.map((item) => (
                <Checkbox
                  key={item.key}
                  label={item.text}
                  styles={styles.checkboxStyles}
                  value={item.key}
                />
              ))}
            </Flex>
          </Checkbox.Group>
          {reasons.includes('other') && (
            <Textarea
              autosize
              className="other-reason"
              description={`${note.length}/200`}
              inputWrapperOrder={['label', 'input', 'description']}
              maxLength={200}
              maxRows={6}
              minRows={6}
              onChange={(event) => setNote(event.currentTarget.value)}
              placeholder="セラピストについてなにかございましたらご記入ください。"
              styles={styles.textareaStyles}
              value={note}
            />
          )}
        </>
      ) : (
        <>
          <Textarea
            autosize
            description={`${note.length}/200`}
            inputWrapperOrder={['label', 'input', 'description']}
            label="セラピストへメッセージする"
            maxLength={200}
            maxRows={6}
            minRows={6}
            onChange={(event) => setNote(event.currentTarget.value)}
            placeholder="任意"
            styles={styles.textareaStyles}
            value={note}
          />
        </>
      )}
      <Button
        className="submit-btn"
        color="marigold"
        disabled={
          bookingStatus === BOOKING_STATUSES.PENDING
            ? reasons.length === 0 ||
              (reasons.includes('other') && !note.trim())
            : !note.trim()
        }
        loading={isSendingReason}
        onClick={() => {
          if (bookingStatus === BOOKING_STATUSES.PENDING) {
            if (
              reasons.length === 0 ||
              (reasons.includes('other') && !note.trim())
            )
              return;
            onSubmit({
              reasons,
              note: note.trim() ? note.trim() : undefined,
            });
          } else {
            if (!note.trim()) return;
            onSubmit({
              reasons,
              note: note.trim() ? note.trim() : undefined,
            });
          }
        }}
      >
        送る
      </Button>
      <Box>
        <Text
          className="skip-btn"
          onClick={onCancel}
          size={16}
          tabIndex={0}
          weight="bold"
        >
          スキップ
        </Text>
      </Box>
    </Box>
  );
};

export default CancelWithoutFee;
