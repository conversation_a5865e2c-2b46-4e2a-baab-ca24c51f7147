import { yupResolver } from '@hookform/resolvers/yup';
import WarningIcon from '@icons/icon-warning-3.svg';
import type { ModalProps } from '@mantine/core';
import { Box, Button, Group, Modal, Stack, Text, Title } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { useQueryClient } from '@tanstack/react-query';
import NumberField from 'components/Form/NumberField';
import { useFetchData } from 'hooks';
import type { ICheckPointAmount, ITotalPoints } from 'models/booking';
import { bookingQuery } from 'models/booking';
import { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import helpers, { exchangePointToPrice } from 'utils/helpers';
import request from 'utils/request';
import { number, object } from 'yup';

import type { ApplyPointFormValues } from './schema';
import useStyles from './styles';

interface ApplyPointModalProps extends ModalProps {
  totalPrice: number;
  onConfirm?: (values: number) => void;
  checkPointAmount?: ICheckPointAmount;
}

const ApplyPointModal = ({
  totalPrice,
  onConfirm,
  onClose,
  opened,
  checkPointAmount,
  ...rest
}: ApplyPointModalProps) => {
  const queryClient = useQueryClient();
  const { classes } = useStyles();
  const { data: totalPoints } = useFetchData<ITotalPoints>(
    bookingQuery.getTotalPoints,
  );
  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<ApplyPointFormValues>({
    mode: 'onChange',
    resolver: yupResolver(
      object({
        point: number()
          .transform((value) => (Number.isNaN(value) ? null : value))
          .min(100, '利用可能ポイントの範囲内で設定してください')
          .max(
            checkPointAmount?.maxPoints || 0,
            '利用可能ポイントの範囲内で設定してください',
          )
          .required()
          .test(
            'divisible-by-100',
            '利用可能ポイントの範囲内で設定してください',
            (value) => {
              return value % 100 === 0;
            },
          ),
      }),
    ),
  });
  const watchPoint = watch('point');

  useEffect(() => {
    if (opened) {
      reset({
        point: undefined,
      });
    }
  }, [opened, reset]);

  const handleSubmitForm: SubmitHandler<ApplyPointFormValues> = async (
    values,
  ) => {
    const checkPointData = await queryClient.fetchQuery({
      queryFn: async () => {
        const { data: result } = await request<ICheckPointAmount>({
          url: bookingQuery.checkPoints.apiUrl,
          data: { totalPrice, point: values.point },
          method: bookingQuery.checkPoints.method,
        });
        return result;
      },
      queryKey: ['currentUser', 'check-points'],
    });
    if (checkPointData && values.point > checkPointData?.maxPoints) {
      openContextModal({
        modal: 'AlertModal',
        withCloseButton: false,
        size: 630,
        innerProps: {
          icon: <WarningIcon />,
          content:
            'ご利用いただけるポイントに更新がありました。\nご確認ください。',
          hasOkBtn: true,
          confirmButtonProps: {
            style: {
              maxWidth: 'calc(100% - 20px)',
              marginTop: -16,
              marginBottom: -30,
            },
          },
          onConfirm: () => {
            reset({ point: undefined });
          },
        },
        centered: true,
      });
    }
    if (
      onConfirm &&
      checkPointData &&
      values.point <= checkPointData.maxPoints
    ) {
      onConfirm(values.point);
    }
  };

  return (
    <>
      <Modal
        centered
        classNames={{
          content: classes.couponListContent,
        }}
        onClose={onClose}
        opened={opened}
        size={630}
        {...rest}
      >
        <Title className={classes.title} order={3}>
          ポイント利用
        </Title>
        <Box className={classes.couponSearchContent}>
          <Title
            color="blackOlive"
            fw={{ base: 400, sm: 700 }}
            fz={{ base: 14, sm: 18 }}
            lh={{ base: '20px', sm: '26px' }}
            mb={{ base: 16, sm: 24 }}
            order={4}
          >
            最大利用可能ポイント:{' '}
            <Text component="span" fw={700}>
              {helpers.numberFormat(checkPointAmount?.maxPoints || 0)}
            </Text>
          </Title>
          <Box
            component="form"
            display="flex"
            onSubmit={handleSubmit(handleSubmitForm)}
          >
            <Stack spacing={24} w="100%">
              <NumberField
                control={control}
                description={
                  <>
                    {!errors.point && (
                      <Text
                        c="gray.6"
                        className={classes.descriptionText}
                        fz={{ base: 12, sm: 14 }}
                      >
                        100単位から利用できます。
                        <br />
                        {`最低決済金額は¥${totalPoints?.exchangeRate.using.pointMultiple}です。`}
                      </Text>
                    )}
                    <Text
                      c="gray.8"
                      fz={{ base: 12, sm: 14 }}
                      mt={{ base: 16, sm: 24 }}
                    >
                      {helpers.numberFormat(
                        exchangePointToPrice(
                          watchPoint,
                          totalPoints?.exchangeRate.using.amount,
                          totalPoints?.exchangeRate.using.point,
                        ),
                      )}
                      円 相当
                    </Text>
                  </>
                }
                disabled={checkPointAmount?.maxPoints === 0}
                fz={16}
                max={checkPointAmount?.maxPoints || 0}
                name="point"
                step={100}
                w="100%"
              />
              <Group grow mt={{ sm: 6 }}>
                <Button
                  color="grey"
                  onClick={onClose}
                  size="lg"
                  variant="outline"
                >
                  キャンセル
                </Button>
                <Button
                  disabled={checkPointAmount?.maxPoints === 0}
                  size="lg"
                  type="submit"
                >
                  適用する
                </Button>
              </Group>
            </Stack>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

export default ApplyPointModal;
