import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  couponListContent: {
    padding: '56px 0 30px',
    backgroundColor: theme.white,
    [theme.fn.smallerThan('sm')]: {
      padding: 0,
    },
  },
  title: {
    textAlign: 'center',
    fontSize: 30,
    lineHeight: '45px',
    position: 'relative',
    paddingBottom: 24,
    [theme.fn.smallerThan('sm')]: {
      textAlign: 'left',
      fontSize: 18,
      lineHeight: '27px',
      padding: '18px 20px 14px',
      '&#detail': {
        boxShadow: '0px 2px 2px 0px rgba(0, 0, 0, 0.16)',
      },
    },
  },
  couponSearchContent: {
    backgroundColor: theme.colors.antiFlashWhite[0],
    padding: '16px 30px',
    border: `1px solid ${theme.colors.azureishWhite[0]}`,
    input: {
      fontSize: 16,
      padding: '0 16px',
      '&::placeholder': {
        color: theme.colors.quickSilver[0],
      },
    },
    [theme.fn.smallerThan('sm')]: {
      padding: '16px 20px',
      input: {
        fontSize: 14,
        padding: '0 12px',
      },
    },
  },
  couponEmpty: {
    padding: '56px 0 26px',
    textAlign: 'center',
    [theme.fn.smallerThan('sm')]: {
      padding: '108px 0 0',
    },
  },
  couponListWrapper: {
    marginTop: 16,
    padding: '0 30px',
    '& > *:not(:last-of-type)': {
      marginBottom: 16,
    },
    [theme.fn.smallerThan('sm')]: {
      padding: '0 20px',
      marginTop: 16,
    },
  },
  couponItemWrapper: {
    boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.16)',
    borderRadius: '3px',
    cursor: 'pointer',
    position: 'relative',
    '&:hover .coupon-title': {
      textDecoration: 'underline',
    },
  },
  couponItemType: {
    flex: '0 0 54px',
    borderTopLeftRadius: 3,
    borderBottomLeftRadius: 3,
    writingMode: 'vertical-rl',
    textOrientation: 'upright',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    wordSpacing: '-5px',
    borderRight: `1px dashed ${theme.colors.platinum[0]}`,
    [theme.fn.smallerThan('sm')]: {
      flex: '0 0 36px',
    },
  },
  couponItemContent: {
    flex: '1 1 auto',
    padding: 16,
    border: `1px solid ${theme.colors.platinum[0]}`,
    borderLeft: 0,
    borderTopRightRadius: 3,
    borderBottomRightRadius: 3,
    [theme.fn.smallerThan('sm')]: {
      padding: 12,
    },
  },
  couponDetailWrapper: {
    padding: '0 30px',
    [theme.fn.smallerThan('sm')]: {
      padding: '0 20px',
    },
  },
  couponDetailContent: {
    borderRadius: 4,
    textAlign: 'center',
    padding: 24,
    marginBottom: 24,
    position: 'relative',
    [theme.fn.smallerThan('sm')]: {
      marginTop: 20,
    },
  },
  btnGroup: {
    marginTop: 42,
    backgroundColor: 'white',
    padding: '0 30px',
    [theme.fn.smallerThan('sm')]: {
      width: '100%',
      padding: '12px 20px 20px',
      position: 'absolute',
      margin: 0,
      bottom: 0,
    },
  },
}));
export default useStyles;
