import { <PERSON>, <PERSON><PERSON>, Di<PERSON>r, Flex, Text, Title } from '@mantine/core';
import dayjs from 'dayjs';
import type { ICoupon } from 'models/booking';
import type { IMenuItem } from 'models/therapist';
import Image from 'next/image';
import React, { useEffect, useMemo } from 'react';
import helpers, { eventLog } from 'utils/helpers';

import useStyles from './styles';

const CouponDetail = ({
  data,
  onConfirm,
  onCancel,
  therapistId,
  menus,
}: {
  data: ICoupon | null;
  onConfirm: () => void;
  onCancel: () => void;
  therapistId: string;
  menus: IMenuItem[];
}) => {
  const { classes } = useStyles();

  const menuPriceList = useMemo(() => {
    const menusObj: Record<string, string> = {};
    menus.forEach((menu, index) => {
      const listCounting = Math.floor(index / 3) + 1;
      if (menusObj[`menu_price_list_${listCounting}`]) {
        menusObj[`menu_price_list_${listCounting}`] =
          `${menusObj[`menu_price_list_${listCounting}`]},` +
          `${menu._id}:${menu.selectedOption?.price}`;
      } else {
        menusObj[
          `menu_price_list_${listCounting}`
        ] = `${menu._id}:${menu.selectedOption?.price}`;
      }
    });
    return menusObj;
  }, [menus]);
  useEffect(() => {
    if (data) {
      eventLog('view_coupon_detail', {
        coupon_code: data.code,
        therapist_id: therapistId,
        ...menuPriceList,
      });
    }
  }, [data, menuPriceList, therapistId]);

  const isReferrerCoupon = ['Invitation', 'Incentive'].includes(
    data?.type || '',
  );

  return (
    <>
      <Title className={classes.title} id="detail" order={3}>
        クーポン詳細
      </Title>
      <Box className={classes.couponDetailWrapper}>
        <Box
          bg={isReferrerCoupon ? 'nyanza' : 'water'}
          className={classes.couponDetailContent}
        >
          {isReferrerCoupon ? (
            <Title
              color="black"
              fw={700}
              fz={{ base: 14, sm: 18 }}
              lh={{ base: '20px', sm: '26px' }}
              mb={4}
            >
              {data?.type === 'Invitation'
                ? '紹介クーポン'
                : 'プレゼントクーポン'}
            </Title>
          ) : (
            <Box mb={4}>
              <Box
                alt="Therapist avatar"
                component={Image}
                h={{ base: 24, sm: 32 }}
                height={32}
                mb={{ base: 4, sm: 8 }}
                src={
                  data?.therapist?.profilePicture?.url ||
                  '/icons/icon-avatar-default-therapist.svg'
                }
                sx={{ borderRadius: '50%' }}
                w={{ base: 24, sm: 32 }}
                width={32}
              />
              <Title
                color="black"
                fw={700}
                fz={{ base: 14, sm: 18 }}
                lh={{ base: '20px', sm: '26px' }}
              >
                {data?.therapist?.nickName}からのクーポン
              </Title>
            </Box>
          )}
          <Text
            color={isReferrerCoupon ? 'avocado' : 'steelBlue'}
            fw={900}
            fz={{ base: 28, sm: 32 }}
            lh={{ base: '40px', sm: '46px' }}
            mb={8}
          >
            {data?.currency === '%'
              ? `${data.amount}${data.currency}`
              : `${data?.currency}${helpers.numberFormat(data?.amount)}`}
          </Text>
          <Text
            display="inline-block"
            fw={500}
            fz={14}
            lh="20px"
            px={12}
            py={{ base: 4, sm: 6 }}
            sx={(theme) => ({
              border: `1px solid ${
                isReferrerCoupon
                  ? theme.colors.avocado[0]
                  : theme.colors.steelBlue[0]
              }`,
            })}
          >
            {data?.code}
          </Text>
          <Text
            fw={500}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mt={8}
          >
            有効期限：
            {data?.expiredAt
              ? dayjs(data.expiredAt).format('YYYY/MM/DD')
              : 'なし'}
          </Text>
          <Text
            bg={isReferrerCoupon ? 'avocado' : 'steelBlue'}
            color="white"
            fw={700}
            fz={14}
            left={0}
            lh="20px"
            pos="absolute"
            px={10}
            py={4}
            sx={{
              borderTopLeftRadius: 3,
              borderBottomRightRadius: 12,
            }}
            top={0}
          >
            {isReferrerCoupon ? 'ホググ' : 'セラピスト'}
          </Text>
          <Box
            pos="absolute"
            right={0}
            sx={(theme) => ({
              zIndex: 1,
              [theme.fn.smallerThan('sm')]: {
                '.pc': {
                  display: 'none',
                },
              },
              [theme.fn.largerThan('sm')]: {
                '.sp': {
                  display: 'none',
                },
              },
            })}
            top={0}
          >
            <img
              alt="present"
              className="pc"
              src={
                isReferrerCoupon
                  ? '/icons/icon-present-green.png'
                  : '/icons/icon-present-blue-lg.png'
              }
            />
            <img
              alt="present"
              className="sp"
              src={
                isReferrerCoupon
                  ? '/icons/icon-present-green-sp.png'
                  : '/icons/icon-present-blue-lg-sp.png'
              }
            />
          </Box>
        </Box>
        <Box px={{ base: 16, sm: 24 }}>
          {!!data?.rules?.min && (
            <>
              <Flex align="flex-start" justify="space-between">
                <Text color="nickel" fw={400} fz={14} lh="20px">
                  クーポンを利用可能な最低金額
                </Text>
                <Text color="black" fw={400} fz={14} lh="20px">
                  ¥{helpers.numberFormat(data?.rules?.min)}
                </Text>
              </Flex>
              <Divider color="platinum" h={0.5} my={16} />
            </>
          )}
          {!!data?.availableCount && (
            <>
              <Flex align="flex-start" justify="space-between">
                <Text color="nickel" fw={400} fz={14} lh="20px">
                  利用可能クーポン数:
                </Text>
                <Text color="black" fw={400} fz={14} lh="20px">
                  {data.availableCount}枚
                </Text>
              </Flex>
              <Divider color="platinum" h={0.5} my={16} />
            </>
          )}
          {data?.description && (
            <Box>
              <Text color="nickel" fw={400} fz={14} lh="20px" mb={8}>
                必ずご確認ください
              </Text>
              <Text color="black" fw={400} fz={14} lh="20px">
                {data.description}
              </Text>
            </Box>
          )}
        </Box>
      </Box>
      <Flex className={classes.btnGroup} gap={16}>
        <Button
          color="grey"
          fullWidth
          onClick={onCancel}
          size="lg"
          variant="outline"
        >
          戻る
        </Button>
        <Button fullWidth onClick={onConfirm} size="lg">
          使用する
        </Button>
      </Flex>
    </>
  );
};

export default CouponDetail;
