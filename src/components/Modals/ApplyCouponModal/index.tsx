import IconCouponTicket from '@icons/icon-coupon-ticket-2.svg';
import type { ModalProps } from '@mantine/core';
import { Box, Button, Loader, Modal, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { TextField } from 'components/Form';
import { useFetchList, useMutate } from 'hooks';
import get from 'lodash/get';
import { authQuery } from 'models/auth';
import type { ICoupon } from 'models/booking';
import { resourceQuery } from 'models/resource';
import type { IMenuItem } from 'models/therapist';
import { useEffect, useMemo, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { eventLog } from 'utils/helpers';

import CouponDetail from './CouponDetail';
import CouponItem from './CouponItem';
import type { ApplyCouponFormValues } from './schema';
import useStyles from './styles';

interface ApplyCouponModalProps extends ModalProps {
  sumPriceWithPoint: number;
  dateBooking?: string;
  menus: IMenuItem[];
  therapistId: string;
  areaCodes: string[];
  onConfirm?: (values: ICoupon) => void;
  onReopen?: () => void;
}

const ApplyCouponModal = ({
  sumPriceWithPoint,
  dateBooking,
  therapistId,
  areaCodes,
  menus,
  onConfirm,
  onClose,
  onReopen,
  opened,
  ...rest
}: ApplyCouponModalProps) => {
  const [openedDetail, setOpenedDetail] = useState(false);
  const [selectedDetail, setSelectedDetail] = useState<ICoupon | null>(null);
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const { classes } = useStyles();
  const { control, handleSubmit, reset, watch } =
    useForm<ApplyCouponFormValues>({
      defaultValues: {
        code: '',
      },
      mode: 'onBlur',
    });
  const couponInput = watch('code', '');

  const menuPriceList = useMemo(() => {
    const menusObj: Record<string, string> = {};
    menus.forEach((menu, index) => {
      const listCounting = Math.floor(index / 3) + 1;
      if (menusObj[`menu_price_list_${listCounting}`]) {
        menusObj[`menu_price_list_${listCounting}`] =
          `${menusObj[`menu_price_list_${listCounting}`]},` +
          `${menu._id}:${menu.selectedOption?.price}`;
      } else {
        menusObj[
          `menu_price_list_${listCounting}`
        ] = `${menu._id}:${menu.selectedOption?.price}`;
      }
    });
    return menusObj;
  }, [menus]);

  const { list, isLoading } = useFetchList<ICoupon>({
    ...authQuery.couponList,
    customParams: {
      servicerId: therapistId,
      limit: 9999,
    },
    enabled: opened && !!therapistId,
  });

  const { mutateAsync: checkCouponFn, isLoading: isCheckingCoupon } = useMutate<
    {
      code: string;
      totalPrice: number;
      bookingDatetime?: string;
      servicerId?: string;
      areaCodes?: string[];
    },
    ICoupon
  >(resourceQuery.checkCoupon);

  useEffect(() => {
    if (opened) {
      reset({
        code: '',
      });
    }
  }, [opened, reset]);

  const handleSubmitForm: SubmitHandler<ApplyCouponFormValues> = async (
    values,
  ) => {
    try {
      const couponData = await checkCouponFn({
        ...values,
        areaCodes,
        bookingDatetime: dateBooking,
        servicerId: therapistId,
        totalPrice: sumPriceWithPoint,
      });
      eventLog('submit_add_coupon', {
        coupon_code: couponData.code,
        therapist_id: therapistId,
        ...menuPriceList,
        error_message: 'success',
        source: 'user_entry',
      });
      if (onConfirm) {
        onConfirm(couponData);
      }
    } catch (e) {
      eventLog('submit_add_coupon', {
        coupon_code: values.code,
        therapist_id: therapistId,
        ...menuPriceList,
        error_message: get(e, 'data.0.message') || get(e, 'error', ''),
        source: 'user_entry',
      });
    }
  };

  const handleSelectCoupon = async (
    coupon: ICoupon,
    source = 'coupon_list',
  ) => {
    try {
      const couponData = await checkCouponFn({
        code: coupon.code,
        bookingDatetime: dateBooking,
        servicerId: therapistId,
        totalPrice: sumPriceWithPoint,
      });
      eventLog('submit_add_coupon', {
        coupon_code: coupon.code,
        therapist_id: therapistId,
        ...menuPriceList,
        error_message: 'success',
        source,
      });
      if (onConfirm) {
        onConfirm(couponData);
      }
    } catch (e) {
      eventLog('submit_add_coupon', {
        coupon_code: coupon.code,
        therapist_id: therapistId,
        ...menuPriceList,
        error_message: get(e, 'data.0.message') || get(e, 'error', ''),
        source,
      });
    }
  };

  return (
    <>
      <Modal
        classNames={{
          content: classes.couponListContent,
        }}
        fullScreen={mobileScreen}
        onClose={onClose}
        opened={opened}
        size={630}
        {...rest}
      >
        <Title className={classes.title} order={3}>
          クーポン
        </Title>
        <Box className={classes.couponSearchContent}>
          <Title
            color="blackOlive"
            fw={700}
            fz={{ base: 14, sm: 16 }}
            lh={{ base: '20px', sm: '24px' }}
            mb={8}
            order={4}
          >
            クーポンを使用しましょう
          </Title>
          <Text
            color="quickSilver"
            fw={400}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mb={16}
          >
            クーポンコードを入力してご利用いただけます。
          </Text>
          <Box
            component="form"
            display="flex"
            onSubmit={handleSubmit(handleSubmitForm)}
          >
            <TextField
              control={control}
              fz={16}
              maxLength={13}
              name="code"
              placeholder="クーポンコードを入力"
              w="100%"
            />
            <Button
              disabled={isCheckingCoupon || !couponInput}
              fw={500}
              fz={{ base: 14, sm: 16 }}
              h={{ base: 40, sm: 50 }}
              ml={8}
              px={16}
              type="submit"
            >
              使用する
            </Button>
          </Box>
          <Text
            color="quickSilver"
            fw={400}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mt={8}
          >
            クーポンを利用するには、コードを入力するか、またはリストからお好きなクーポンを選んで「使用する」ボタンを押してください。
          </Text>
        </Box>
        {!isLoading && !list.length && (
          <Box className={classes.couponEmpty}>
            <IconCouponTicket />
            <Text color="sonicSilver" fz={14} lh="20px" mt={21}>
              現在、利用可能なクーポンはありません
            </Text>
          </Box>
        )}
        {!!list.length && (
          <Box className={classes.couponListWrapper}>
            <Title
              color="blackOlive"
              fw={700}
              fz={16}
              lh="24px"
              mb={16}
              order={4}
            >
              ご利用可能なクーポン
            </Title>
            {list.map((item) => {
              return (
                <CouponItem
                  data={item}
                  key={item.id || item._id}
                  onOpenDetail={() => {
                    onClose();
                    setOpenedDetail(true);
                    setSelectedDetail(item);
                  }}
                  onSelect={() => {
                    handleSelectCoupon(item);
                  }}
                />
              );
            })}
          </Box>
        )}
        {isLoading && (
          <Box pb={26} pt={56} ta="center">
            <Loader />
          </Box>
        )}
      </Modal>
      <Modal
        classNames={{
          content: classes.couponListContent,
        }}
        fullScreen={mobileScreen}
        onClose={() => {
          setOpenedDetail(false);
          if (onReopen) onReopen();
        }}
        opened={!!openedDetail}
        size={630}
      >
        <CouponDetail
          data={selectedDetail}
          menus={menus}
          onCancel={() => {
            setOpenedDetail(false);
            if (onReopen) onReopen();
          }}
          onConfirm={() => {
            if (selectedDetail)
              handleSelectCoupon(selectedDetail, 'coupon_detail');
            setOpenedDetail(false);
          }}
          therapistId={therapistId}
        />
      </Modal>
    </>
  );
};

export default ApplyCouponModal;
