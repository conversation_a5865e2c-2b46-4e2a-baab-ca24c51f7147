import { Box, Button, Flex, Text } from '@mantine/core';
import type { ICoupon } from 'models/booking';
import Image from 'next/image';
import React from 'react';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

import useStyles from './styles';

const CouponItem = ({
  data,
  onSelect,
  onOpenDetail,
}: {
  data: ICoupon;
  onSelect: () => void;
  onOpenDetail: () => void;
}) => {
  const { classes } = useStyles();
  const isReferrerCoupon = ['Invitation', 'Incentive'].includes(
    data.type || '',
  );
  return (
    <Flex className={classes.couponItemWrapper} onClick={onOpenDetail}>
      <Text
        bg={isReferrerCoupon ? 'avocado' : 'steelBlue'}
        className={classes.couponItemType}
        color="white"
        fw={700}
        fz={{ base: 12, sm: 14 }}
      >
        {isReferrerCoupon ? 'ホ グ グ' : 'セ ラ ピ ス ト'}
      </Text>
      <Box className={classes.couponItemContent}>
        <Flex align="center" mb={8} wrap="wrap">
          <Text
            color={isReferrerCoupon ? 'avocado' : 'steelBlue'}
            fw={900}
            fz={32}
            lh="38px"
          >
            {data.currency === '%'
              ? `${data.amount}${data.currency}`
              : `${data.currency}${helpers.numberFormat(data.amount)}`}
          </Text>
          {!!data.availableCount && data.availableCount > 1 && (
            <Text
              bg="maximumRed"
              color="white"
              fw={700}
              fz={{ base: 10, sm: 12 }}
              lh="12px"
              ml={{ base: 4, sm: 8 }}
              mt={5}
              px={{ base: 6, sm: 8 }}
              py={4}
              sx={{
                borderRadius: '66px',
              }}
            >
              残り{data.availableCount}枚
            </Text>
          )}
        </Flex>
        {isReferrerCoupon ? (
          <Text
            color="black"
            fw={700}
            fz={{ base: 12, sm: 16 }}
            lh={{ base: '16px', sm: '24px' }}
            mb={12}
          >
            {data.type === 'Invitation' ? '紹介クーポン' : 'プレゼントクーポン'}
          </Text>
        ) : (
          <Flex align="center" mb={12} wrap="nowrap">
            <Box
              alt="Therapist avatar"
              component={Image}
              h={{ base: 16, sm: 24 }}
              height={24}
              src={
                data.therapist?.profilePicture?.url ||
                '/icons/icon-avatar-default-therapist.svg'
              }
              sx={{ borderRadius: '50%' }}
              w={{ base: 16, sm: 24 }}
              width={24}
            />
            <Text
              color="black"
              fw={700}
              fz={{ base: 12, sm: 16 }}
              lh={{ base: '16px', sm: '24px' }}
              ml={{ base: 8, sm: 11 }}
              sx={{
                display: 'flex',
                flexWrap: 'nowrap',
                wordBreak: 'keep-all',
              }}
            >
              <Text inherit lineClamp={1}>
                {data.therapist?.nickName}
              </Text>
              からのクーポン
            </Text>
          </Flex>
        )}
        <Flex align="center" wrap="wrap">
          <Text
            color="grey"
            fw={400}
            fz={{ base: 10, sm: 12 }}
            lh={{ base: '12px', sm: '16px' }}
          >
            クーポンコード：
          </Text>
          <Text
            color="black"
            fw={400}
            fz={{ base: 10, sm: 12 }}
            lh={{ base: '12px', sm: '16px' }}
          >
            {data.code}
          </Text>
        </Flex>
        <Flex align="center" mt={8} wrap="wrap">
          <Text
            color="grey"
            fw={400}
            fz={{ base: 10, sm: 12 }}
            lh={{ base: '12px', sm: '16px' }}
          >
            有効期限：
          </Text>
          <Text
            color="black"
            fw={400}
            fz={{ base: 10, sm: 12 }}
            lh={{ base: '12px', sm: '16px' }}
          >
            {data.expiredAt
              ? dayjs(data.expiredAt).format('YYYY/MM/DD')
              : 'なし'}
          </Text>
        </Flex>
      </Box>
      <Button
        bottom={{ base: 12, sm: 16 }}
        fw={500}
        fz={{ base: 12, sm: 14 }}
        h={{ base: 28, sm: 40 }}
        lh={{ base: '16px', sm: '20px' }}
        mih={28}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onSelect();
        }}
        pos="absolute"
        px={{ base: 10, sm: 12 }}
        right={{ base: 12, sm: 16 }}
      >
        使用する
      </Button>
      <Box
        pos="absolute"
        right={0}
        sx={(theme) => ({
          zIndex: -1,
          [theme.fn.smallerThan('sm')]: {
            '.pc': {
              display: 'none',
            },
          },
          [theme.fn.largerThan('sm')]: {
            '.sp': {
              display: 'none',
            },
          },
        })}
        top={0}
      >
        <img
          alt=""
          className="pc"
          src={
            isReferrerCoupon
              ? '/icons/icon-present.png'
              : '/icons/icon-present-blue.png'
          }
        />
        <img
          alt=""
          className="sp"
          src={
            isReferrerCoupon
              ? '/icons/icon-present-sp.png'
              : '/icons/icon-present-blue-sp.png'
          }
        />
      </Box>
    </Flex>
  );
};

export default CouponItem;
