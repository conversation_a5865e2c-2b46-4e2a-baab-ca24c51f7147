import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  groupBtn: {
    marginTop: 66,
    '@media (max-width: 768px)': {
      marginTop: 40,
    },
  },

  textWrapper: {
    h3: {
      fontSize: 30,
    },
    h4: {
      fontSize: 24,
      marginTop: 30,
      marginBottom: 16,
    },
    '@media (max-width: 768px)': {
      h3: {
        fontSize: 22,
      },
      h4: {
        fontSize: 16,
        marginTop: 34,
        marginBottom: 20,
      },
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  nextBtn: {
    root: {
      width: 300,
      '@media (max-width: 768px)': {
        width: 180,
      },
    },
  },
};
