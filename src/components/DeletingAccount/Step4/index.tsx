import { Button, Flex, Group, Text, Title } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { useFetchData, useLogout } from 'hooks';
import type { IConfigurationItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

import { styles, sx } from './styles';

const Step4: React.FC = () => {
  const { logout: logoutFn } = useLogout();
  const router = useRouter();
  const { data: configurations } = useFetchData<
    IConfigurationItem[],
    {
      [key: string]: IConfigurationItem;
    }
  >({
    ...resourceQuery.getConfigs,
    select: (response) => {
      const objectKeyMap: Record<string, IConfigurationItem> = {};
      response.forEach((item) => {
        objectKeyMap[item.key] = item;
      });
      return objectKeyMap;
    },
  });

  useEffect(() => {
    const handleRouteChange = () => {
      logoutFn(false, false);
    };
    router.events.on('routeChangeStart', handleRouteChange);
    router.beforePopState(() => {
      logoutFn(false, false);
      return true;
    });
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [logoutFn, router]);

  useWindowEvent('beforeunload', () => {
    logoutFn(false, false);
  });

  return (
    <>
      <Flex direction="column" sx={sx.textWrapper}>
        <Title order={3} sx={sx.title}>
          退会申請 完了
        </Title>
        <Title order={4} sx={sx.title}>
          退会申請が完了しました
        </Title>
        <Text mb={30}>
          退会申請から30日経過するとすべてのデータが完全に削除され、申請されたアカウントでのご利用・ログインができなくなります。
          <br />
          <br />
          ・&nbsp;30日以内に再度ログインして申請を取消し、アカウントを復活させることができます。
          <br />
          ・&nbsp;30日を待たずに完全削除を希望される場合、再度ログインの上、画面案内に沿ってお手続きください。
          <br />
          ・&nbsp;完全削除された後、
          {configurations?.customerCanNotRegisterDuration?.value || 90}
          日間は同じ電話番号で登録ができなくなりますので、ご注意ください。
        </Text>
        <Text>
          また、
          HOGUGUより配信しておりますメールマガジンにつきまして、配信停止をご希望される場合は、別途手続きが必要となります。
          <br />
          お手数ですが、配信メール本文内にございます配信停止URLよりお手続きください。
        </Text>
      </Flex>
      <Group position="center" sx={sx.groupBtn}>
        <Button
          onClick={() => {
            logoutFn(false);
          }}
          size="lg"
          styles={styles.nextBtn}
        >
          ホームへ戻る
        </Button>
      </Group>
    </>
  );
};

export default Step4;
