import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  title: {
    'h3&': {
      fontSize: 30,
    },
    'h4&': {
      fontSize: 24,
      marginTop: 30,
      marginBottom: 16,
    },
    '@media (max-width: 768px)': {
      'h3&': {
        fontSize: 22,
      },
      'h4&': {
        fontSize: 16,
        marginBottom: 12,
      },
    },
  },

  groupBtn: {
    marginTop: 66,
    '@media (max-width: 768px)': {
      marginTop: 40,
    },
  },

  text: {
    marginBottom: 6,
    '@media (max-width: 768px)': {
      marginBottom: 12,
    },
  },
};

export const styles: Record<string, any> = {
  nextBtn: {
    root: {
      width: 300,
      '@media (max-width: 768px)': {
        width: 158,
      },
    },
  },

  backBtn: {
    root: {
      width: 300,
      '@media (max-width: 768px)': {
        width: 158,
        paddingLeft: 14,
        paddingRight: 14,
      },
    },
  },

  checkbox: (theme: MantineTheme) => ({
    body: {
      border: 'none',
    },
    label: {
      padding: '10px 10px 10px 40px',
      fontWeight: 'normal',
      color: theme.colors.blackOlive,
      '@media (max-width: 768px)': {
        fontSize: 14,
        padding: '8px 8px 8px 28px',
      },
    },
    input: {
      marginTop: -1,
      marginLeft: -25,
      width: 24,
      height: 24,
      '&:checked': {
        borderColor: theme.colors.blackOlive,
        backgroundColor: theme.colors.blackOlive,
      },
      '@media (max-width: 768px)': {
        width: 18,
        height: 18,
      },
    },
    icon: {
      marginTop: 8,
      marginLeft: -18,
      width: '50%',
      '@media (max-width: 768px)': {
        marginTop: 5,
        marginLeft: -21,
      },
    },
  }),

  textarea: {
    description: {
      fontSize: 12,
    },
    input: {
      marginTop: 10,
      marginBottom: 10,
      fontSize: 14,
      minHeight: 140,
      '@media (max-width: 768px)': {
        marginTop: 2,
      },
    },
  },
};
