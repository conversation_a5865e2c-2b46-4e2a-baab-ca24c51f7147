import type { InferType } from 'yup';
import { array, object, string } from 'yup';

export const schema = object({
  reasons: array().min(1, 'この項目は入力必須です。').required(),
  comment: string()
    .trim()
    .when('reasons', {
      is: (value: string[]) => value && value.includes('OTHER'),
      then: (context) => context.required(),
    })
    .max(500),
});

export type DeletingAccountFormValues = InferType<typeof schema>;
