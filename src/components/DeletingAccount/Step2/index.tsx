import { yupResolver } from '@hookform/resolvers/yup';
import type { CheckboxProps } from '@mantine/core';
import { Box, Button, Group, Text, Title } from '@mantine/core';
import { CheckboxField, TextArea } from 'components/Form';
import { useFetchData, useUser } from 'hooks';
import _ from 'lodash';
import type { IDeletionReasonItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';

import type { DeletingAccountFormValues } from './schema';
import { schema } from './schema';
import { styles, sx } from './styles';

const Step2: React.FC<{
  initialValues: DeletingAccountFormValues;
  onSubmit: (values: DeletingAccountFormValues) => void;
  backFn: () => void;
}> = ({ initialValues, onSubmit, backFn }) => {
  const { data: user } = useUser();
  const { data: reasons = [] } = useFetchData<IDeletionReasonItem[]>(
    resourceQuery.getDeletionReasons,
  );
  const reasonOptions: CheckboxProps[] = useMemo(() => {
    return reasons.map((i) => ({
      value: i.code,
      label: i.text,
    }));
  }, [reasons]);
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid },
  } = useForm<DeletingAccountFormValues>({
    defaultValues: initialValues,
    resolver: yupResolver(schema),
  });

  const formValues = watch();

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <Title color="blackOlive" order={3} sx={sx.title}>
        退会申請 アンケート
      </Title>
      <Title color="blackOlive" order={4} sx={sx.title}>
        {user?.name}様
      </Title>
      <Text color="blackOlive" sx={sx.text}>
        アカウントを削除する理由をお聞かせてください (複数選択可)
      </Text>
      <CheckboxField
        control={control}
        name="reasons"
        options={reasonOptions}
        styles={styles.checkbox}
      />
      {(formValues.reasons || []).includes('OTHER') && (
        <TextArea
          control={control}
          description={`${_.chain(formValues)
            .get('comment', '')
            .size()
            .value()}/500`}
          maxLength={500}
          maxRows={4}
          minRows={2}
          name="comment"
          placeholder="セラピストについてなにかございましたらご記入ください。"
          styles={styles.textarea}
        />
      )}
      <Group position="apart" spacing={10} sx={sx.groupBtn}>
        <Button
          color="grey"
          onClick={backFn}
          size="lg"
          styles={styles.backBtn}
          variant="outline"
        >
          前に戻る
        </Button>
        <Button
          disabled={!isValid}
          size="lg"
          styles={styles.nextBtn}
          type="submit"
        >
          次へ
        </Button>
      </Group>
    </Box>
  );
};

export default Step2;
