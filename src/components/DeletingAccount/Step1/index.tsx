import IconPhoneCircle from '@icons/icon-phone-circle.svg';
import { Button, Flex, Group, Text, Title } from '@mantine/core';
import { useFetchData, useUser } from 'hooks';
import { authQuery } from 'models/auth';
import type { IConfigurationItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import Link from 'next/link';

import { styles, sx } from './styles';

const Step1: React.FC<{
  enabled: boolean;
  onClick: () => void;
}> = ({ enabled, onClick }) => {
  const { data: user } = useUser();
  const { data } = useFetchData<{ status: string }>({
    ...authQuery.checkCanDeleteAccount,
    enabled,
  });
  const { data: configurations } = useFetchData<
    IConfigurationItem[],
    {
      [key: string]: IConfigurationItem;
    }
  >({
    ...resourceQuery.getConfigs,
    select: (response) => {
      const objectKeyMap: Record<string, IConfigurationItem> = {};
      response.forEach((item) => {
        objectKeyMap[item.key] = item;
      });
      return objectKeyMap;
    },
  });

  return data?.status ? (
    <>
      <Flex align="center" direction="column" sx={sx.textWrapper}>
        <IconPhoneCircle />
        <Title color="blackOlive" order={3} sx={sx.title}>
          未完了の予約があります
        </Title>
        <Text>
          完了していない予約があるため退会ができません。
          <br />
          予約の決済を完了させるか、予約をキャンセルしてから再度退会処理を行ってください。
        </Text>
      </Flex>
      <Group position="center" sx={sx.groupBtn}>
        <Button
          component={Link}
          href="/my-page"
          size="lg"
          styles={styles.nextBtn}
        >
          マイページ
        </Button>
      </Group>
    </>
  ) : (
    <>
      <Title color="blackOlive" order={3} sx={sx.title}>
        退会申請
      </Title>
      <Title color="blackOlive" order={4} sx={sx.title}>
        {user?.name}様
      </Title>
      <Text sx={sx.list}>
        アカウントを削除すると以下のデータが削除されます
        <br />
        •&nbsp;&nbsp;登録した個人情報のすべて
        <br />
        •&nbsp;&nbsp;予約関連情報のすべて
        <br />
        •&nbsp;&nbsp;決済履歴
      </Text>
      <Text>
        退会申請から30日経過するとすべてのデータが完全に削除され、その後
        {configurations?.customerCanNotRegisterDuration?.value || 90}
        日間は同じ電話番号で登録ができなくなりますので、ご注意ください。
        <br />
        <br />
        退会申請から30日以内であれば、申請を取消し、アカウントを復活させることができます。
      </Text>
      <Group position="apart" spacing={10} sx={sx.groupBtn}>
        <Button
          color="grey"
          component={Link}
          href="/my-page"
          size="lg"
          styles={styles.backBtn}
          variant="outline"
        >
          マイページへ戻る
        </Button>
        <Button onClick={onClick} size="lg" styles={styles.nextBtn}>
          次へ
        </Button>
      </Group>
    </>
  );
};

export default Step1;
