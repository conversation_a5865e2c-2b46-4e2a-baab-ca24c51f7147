import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  title: {
    'h3&': {
      fontSize: 30,
    },
    'h4&': {
      fontSize: 24,
      marginTop: 30,
      marginBottom: 16,
    },
    '@media (max-width: 768px)': {
      'h3&': {
        fontSize: 22,
      },
      'h4&': {
        fontSize: 16,
        marginBottom: 12,
      },
    },
  },

  list: {
    marginBottom: 30,
    '@media (max-width: 768px)': {
      marginBottom: 20,
    },
  },

  groupBtn: {
    marginTop: 66,
    '@media (max-width: 768px)': {
      marginTop: 40,
    },
  },

  textWrapper: {
    textAlign: 'center',
    h3: {
      marginTop: 30,
      marginBottom: 18,
    },
    '@media (max-width: 768px)': {
      h3: {
        fontSize: 20,
        marginTop: 24,
        marginBottom: 12,
      },
    },
  },
};

export const styles: Record<string, any> = {
  nextBtn: {
    root: {
      width: 300,
      '@media (max-width: 768px)': {
        width: 158,
      },
    },
  },

  backBtn: {
    root: {
      width: 300,
      '@media (max-width: 768px)': {
        width: 158,
        paddingLeft: 14,
        paddingRight: 14,
      },
    },
  },
};
