import { Title } from '@mantine/core';
import { OtpVerifyForm } from 'components/Auth';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';
import { useAuthSignInWithPhoneNumber, useUser } from 'hooks';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { FIREBASE_AUTH_ERRORS } from 'utils/constants';
import { auth, generateRecaptcha } from 'utils/firebase';
import notification from 'utils/notification';

import { sx } from './styles';

const Step3: React.FC<{
  isLoading: boolean;
  onSubmit: () => void;
  backFn: () => void;
}> = ({ onSubmit, backFn, isLoading }) => {
  const { data: user } = useUser();
  const [isVerifying, setIsVerifying] = useState(false);

  const { mutateAsync: sendOtp, data: confirmationResult } =
    useAuthSignInWithPhoneNumber(auth);

  const sendOTP = async () => {
    try {
      generateRecaptcha();
      await sendOtp({
        phoneNumber: user!.phone,
        appVerifier: window.recaptchaVerifier,
      });
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] ||
        'システムエラーです。申し訳ございません。1つ前の画面に戻り、電話番号を再度送信してください。';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
      throw e;
    }
  };

  useEffect(() => {
    if (user?.phone) sendOTP();
  }, [user]);

  const handleSubmit = async (values: OtpVerifyFormValues) => {
    try {
      setIsVerifying(true);
      const result = await confirmationResult?.confirm(values.code);
      if (result) {
        onSubmit();
      }
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] || 'Something went wrong...';
      notification.show({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <>
      <Title color="blackOlive" order={3} sx={sx.title}>
        退会
      </Title>
      <OtpVerifyForm
        description="退会申請を行うため、SMSで受信した6桁の認証コードを入力してください"
        initialValues={{ code: '' }}
        isLoading={isLoading || isVerifying}
        label="退会申請を行います"
        onCancel={backFn}
        onResendOtp={async () => {
          await sendOTP();
        }}
        onSubmit={handleSubmit}
        type="deleteAccount"
      />
    </>
  );
};

export default Step3;
