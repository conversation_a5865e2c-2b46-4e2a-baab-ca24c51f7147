import CloseIcon from '@icons/icon-close.svg';
import {
  ActionIcon,
  Box,
  Divider,
  Flex,
  Portal,
  Text,
  Transition,
} from '@mantine/core';
import { useScrollLock } from '@mantine/hooks';
import Image from 'next/image';
import React, { Fragment } from 'react';
import { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';

import useStyles from './styles';

const ImageView = ({
  opened,
  title = '地図で確認',
  src,
  onClose,
}: {
  opened: boolean;
  title?: React.ReactNode;
  src?: string;
  onClose: () => void;
}) => {
  const { classes } = useStyles();
  useScrollLock(opened);

  return (
    <Transition
      duration={400}
      mounted={opened}
      timingFunction="ease"
      transition="fade"
    >
      {(styles) => (
        <Portal>
          <Box className={classes.portalWrapper} style={styles}>
            <Box className={classes.portalHeader}>
              <Text color="white" fz={14} lh="20px">
                {title}
              </Text>
              <ActionIcon
                className={classes.closeBtn}
                onClick={onClose}
                size={40}
                variant="transparent"
              >
                <CloseIcon />
              </ActionIcon>
            </Box>
            <TransformWrapper
              centerZoomedOut
              maxScale={3}
              minScale={1}
              velocityAnimation={{
                disabled: true,
              }}
            >
              {({ zoomIn, zoomOut }) => (
                <Fragment>
                  <TransformComponent
                    contentStyle={{
                      width: '100%',
                      height: '100%',
                    }}
                    wrapperStyle={{
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    <Image
                      alt=""
                      fill
                      src={src || '/images/map.png'}
                      style={{
                        objectFit: 'contain',
                      }}
                    />
                  </TransformComponent>
                  <Flex className={classes.control} direction="column">
                    <ActionIcon
                      className="zoom-in-btn"
                      onClick={() => zoomIn()}
                      size={44}
                    >
                      +
                    </ActionIcon>
                    <Divider color="gainsboro" mx={8} />
                    <ActionIcon onClick={() => zoomOut()} size={44}>
                      –
                    </ActionIcon>
                  </Flex>
                </Fragment>
              )}
            </TransformWrapper>
          </Box>
        </Portal>
      )}
    </Transition>
  );
};

export default ImageView;
