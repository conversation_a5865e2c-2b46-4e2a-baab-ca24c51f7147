import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  portalWrapper: {
    position: 'fixed',
    width: '100%',
    height: '100%',
    zIndex: 201,
    backgroundColor: 'rgba(74, 80, 78, 0.8)',
    top: 0,
    left: 0,
    display: 'flex',
    flexDirection: 'column',
  },
  portalHeader: {
    backgroundColor: '#101211',
    textAlign: 'center',
    position: 'relative',
    padding: '0 8px',
    display: 'flex',
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeBtn: {
    position: 'absolute',
    right: 8,
    svg: {
      width: 16,
      height: 16,
    },
  },
  control: {
    position: 'fixed',
    right: 24,
    bottom: 24,
    backgroundColor: theme.white,
    borderRadius: 8,
    [theme.fn.smallerThan('sm')]: {
      right: 16,
      bottom: 16,
    },
    button: {
      fontSize: 24,
      lineHeight: 1,
      color: theme.black,
      backgroundColor: theme.white,
      borderRadius: 0,
      borderBottomRightRadius: 8,
      borderBottomLeftRadius: 8,
      '&:not(:last-of-type)': {
        borderRadius: 0,
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
      },
    },
  },
}));

export default useStyles;
