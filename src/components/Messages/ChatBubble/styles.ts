import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  chatBubbleWrapper: (theme) => ({
    display: 'flex',
    width: '100%',
    justifyContent: 'flex-end',
    padding: '0 59px 0 0',
    marginBottom: 32,
    '@media (max-width: 768px)': {
      padding: '0 25px 0 0',
      marginBottom: 24,
    },
    '.therapist-avatar': {
      borderRadius: '50%',
      '@media (max-width: 768px)': {
        width: 44,
        height: 44,
      },
    },
    '.bubble-wrapper': {
      display: 'flex',
      flexDirection: 'column',
      gap: 8,
      textAlign: 'right',
      position: 'relative',
    },
    '.message-text': {
      padding: '13px 24px',
      lineHeight: 1.5,
      fontSize: 16,
      fontWeight: 'bold',
      letterSpacing: '0.48px',
      color: theme.colors.blackOlive[0],
      backgroundColor: theme.colors.platinum[0],
      borderRadius: 6,
      position: 'relative',
      maxWidth: '30vw',
      '@media (max-width: 768px)': {
        padding: '10px 18px',
        fontSize: 14,
        maxWidth: '60vw',
      },
      '&:after': {
        content: '""',
        display: 'block',
        width: 0,
        height: 0,
        position: 'absolute',
        right: -12,
        top: 12,
        borderStyle: 'solid',
        borderWidth: '6px 0 6px 12px',
        borderColor: `transparent transparent transparent ${theme.colors.platinum[0]}`,
        '@media (max-width: 768px)': {
          right: -6,
          top: 12,
        },
      },
    },
    '.timestamp': {
      lineHeight: 1.08,
      fontSize: 14,
      letterSpacing: '0.42px',
      color: theme.colors.blackOlive[0],
      position: 'absolute',
      width: 150,
      bottom: -24,
      right: 0,
      '@media (max-width: 768px)': {
        fontSize: 12,
        bottom: -18,
      },
    },
    '&[data-end=true]': {
      marginBottom: 105,
      '@media (max-width: 768px)': {
        marginBottom: 72,
      },
    },

    // Bubble send by therapist
    '&[data-therapist=true]': {
      justifyContent: 'flex-start',
      gap: 30,
      padding: '0 0 0 50px',
      '@media (max-width: 768px)': {
        padding: '0 0 0 20px',
        gap: 15,
      },
      '.message-text': {
        color: theme.white,
        backgroundColor: theme.colors.queenBlue[6],
        '&:after': {
          left: -12,
          borderWidth: '6px 12px 6px 0',
          borderColor: `transparent ${theme.colors.queenBlue[6]} transparent transparent`,
        },
      },
      '.bubble-wrapper': {
        textAlign: 'left',
      },
      '.timestamp': {
        left: 0,
      },
      '&[data-start=true]': {
        '.bubble-wrapper': {
          marginTop: 12,
          '@media (max-width: 768px)': {
            marginTop: 10,
          },
        },
      },
      '&[data-start=false]': {
        padding: '0 0 0 143px',
        '@media (max-width: 768px)': {
          padding: '0 0 0 79px',
        },
      },
    },
    '&:first-of-type': {
      marginBottom: 0,
    },

    // Bubble loading
    '&[data-loading=true]': {
      marginBottom: 4,
      '.message-text': {
        height: 50,
        width: 97,
        color: theme.colors.blackOlive[0],
        backgroundColor: theme.colors.platinum[0],
      },
      '&[data-therapist=true]': {
        '.message-text': {
          '&:after': {
            borderColor: `transparent ${theme.colors.platinum[0]} transparent transparent`,
          },
        },
      },
      '&[data-end=true]': {
        marginBottom: 105,
        '@media (max-width: 768px)': {
          marginBottom: 72,
        },
      },
      '&[data-start=false][data-end=true]': {
        '.message-text': {
          width: 179,
        },
      },
      '&[data-start=false][data-end=false]': {
        '.message-text': {
          width: 262,
          height: 98,
        },
      },
      '&:first-of-type': {
        marginBottom: 0,
      },
    },
  }),
};
