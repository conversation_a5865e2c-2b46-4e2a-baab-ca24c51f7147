import { Box, Skeleton, Text } from '@mantine/core';
import type { IMessageItem } from 'hooks/types';
import Image from 'next/image';
import React from 'react';
import Emoji from 'react-emoji-render';
import dayjs from 'utils/dayjs';

import { sx } from './styles';

const ChatBubble = ({
  data,
  therapistAvatar,
  therapistId,
  isEndGroup,
  isStartGroup,
  loading = false,
}: {
  data?: IMessageItem;
  therapistId?: string;
  therapistAvatar?: string;
  isStartGroup?: boolean;
  isEndGroup?: boolean;
  loading?: boolean;
}) => {
  if (loading) {
    return (
      <Box
        data-end={isEndGroup}
        data-loading={true}
        data-start={isStartGroup}
        data-therapist={data?.senderType === 'therapist'}
        sx={sx.chatBubbleWrapper}
      >
        {data?.senderType === 'therapist' && isStartGroup && (
          <Skeleton circle className="therapist-avatar" h={64} w={64} />
        )}
        <Box className="bubble-wrapper">
          <Box className="message-text" />
        </Box>
      </Box>
    );
  }
  return (
    <Box
      data-end={isEndGroup}
      data-start={isStartGroup}
      data-therapist={data?.senderType === 'therapist'}
      sx={sx.chatBubbleWrapper}
    >
      {data?.senderType === 'therapist' && isStartGroup && (
        <Image
          alt="Therapist avatar"
          className="therapist-avatar"
          height={64}
          src={therapistAvatar || '/icons/icon-avatar-default-therapist.svg'}
          width={64}
        />
      )}
      <Box className="bubble-wrapper">
        <Text className="message-text">
          <Emoji text={data?.content || ''} />
        </Text>
        <Text className="timestamp">
          {data?.senderType === 'customer' &&
            data?.readByUsers?.includes(therapistId || '') && <b>既読 </b>}
          {dayjs(data?.created?.toDate()).format('YYYY-MM-DD HH:mm')}
        </Text>
      </Box>
    </Box>
  );
};

export default ChatBubble;
