import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

export const schema = object({
  profilePicture: string(),
  name: string()
    .trim()
    .max(30)
    .required('登録名を入力してください。')
    .test(
      '名前が無効です。',
      '名前が無効です。',
      (value) =>
        !REGEX.EMOJI.test(value) && !REGEX.UNIQUE_CHARACTER.test(value),
    ),
  birthday: string().required(),
  phone: string(),
  gender: string().nullable().required(),
  email: string()
    .trim()
    .matches(REGEX.EMAIL, {
      excludeEmptyString: true,
      message: 'メールアドレスをお確めの上、入力してください。',
    })
    .required('メールアドレスを入力してください。'),
  invitationCode: string().trim().strict().nullable(),
});

export type CompleteProfileFormValues = InferType<typeof schema>;
