import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Text } from '@mantine/core';
import { ProfileFields } from 'components/Profile';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';

import type { CompleteProfileFormValues } from './schema';
import { schema } from './schema';
import { sx } from './styles';

const CompleteProfileForm: React.FC<{
  initialValues: CompleteProfileFormValues;
  onSubmit: SubmitHandler<CompleteProfileFormValues>;
  loading?: boolean;
  isCompleteProfile?: boolean;
}> = ({ initialValues, onSubmit, loading }) => {
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid },
  } = useForm<CompleteProfileFormValues>({
    defaultValues: initialValues,
    mode: 'onBlur',
    resolver: yupResolver(schema),
  });

  const formValues = watch();

  // useEffect(() => {
  //   const confirmationMessage = '加えた変更は保存されない可能性があります。';
  //   const beforeUnloadHandler = (e: BeforeUnloadEvent) => {
  //     (e || window.event).returnValue = confirmationMessage;
  //     return confirmationMessage; // Gecko + Webkit, Safari, Chrome etc.
  //   };
  //   const beforeRouteHandler = (url: string) => {
  //     // eslint-disable-next-line no-alert
  //     if (router.pathname !== url && !window.confirm(confirmationMessage)) {
  //       // to inform NProgress or something ...
  //       router.events.emit('routeChangeError');
  //       // tslint:disable-next-line: no-string-throw
  //       throw Error(`Route change to "${url}" was aborted`);
  //     }
  //   };
  //   if (isDirty && isCompleteProfile) {
  //     window.addEventListener('beforeunload', beforeUnloadHandler);
  //     router.events.on('routeChangeStart', beforeRouteHandler);
  //   } else {
  //     window.removeEventListener('beforeunload', beforeUnloadHandler);
  //     router.events.off('routeChangeStart', beforeRouteHandler);
  //   }
  //   return () => {
  //     window.removeEventListener('beforeunload', beforeUnloadHandler);
  //     router.events.off('routeChangeStart', beforeRouteHandler);
  //   };
  // }, [isCompleteProfile, isDirty, router]);

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={sx.completeProfileFormWrapper}
    >
      <Text color="blackOlive" sx={sx.title} weight="bold">
        プロフィールの登録
      </Text>
      <Text color="blackOlive" mb={42} size={16} sx={sx.subTitle}>
        {'入力後に「次へ」を押してください。\n内容はいつでも変更できます。'}
      </Text>
      <ProfileFields
        control={control}
        formValues={formValues}
        isCompleteProfile
      />
      <Button
        disabled={!isValid}
        loading={loading}
        size="lg"
        sx={sx.submitBtn}
        type="submit"
      >
        次へ
      </Button>
    </Box>
  );
};

export default CompleteProfileForm;
