import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  completeProfileFormWrapper: {
    display: 'flex',
    flexDirection: 'column',
  },
  title: {
    marginBottom: 16,
    fontSize: 24,
    '@media (max-width: 768px)': {
      marginBottom: 12,
      fontSize: 22,
    },
  },
  subTitle: {
    marginBottom: 42,
    fontSize: 16,
    whiteSpace: 'normal',
    '@media (max-width: 768px)': {
      marginBottom: 25,
      fontSize: 14,
      whiteSpace: 'pre-line',
    },
  },
  submitBtn: {
    maxWidth: 300,
    width: '100%',
    margin: '65px auto 0',
    '@media (max-width: 768px)': {
      marginTop: 30,
      maxWidth: 180,
    },
  },
};
