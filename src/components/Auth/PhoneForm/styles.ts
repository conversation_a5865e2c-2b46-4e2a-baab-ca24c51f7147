import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  formWrapper: {
    display: 'flex',
    flexDirection: 'column',
  },
  submitBtn: {
    maxWidth: 300,
    width: '100%',
    margin: '65px auto 0',
    '@media (max-width: 768px)': {
      maxWidth: 180,
      marginTop: 20,
    },
  },
  extraStyle: {
    '.mantine-TextInput-label': {
      fontSize: 30,
      '@media (max-width: 768px)': {
        fontSize: 22,
      },
    },
  },
  termsSubTitle: {
    fontSize: 16,
    marginTop: 18,
    marginBottom: 16,
    '@media (max-width: 768px)': {
      fontSize: 14,
      marginTop: 12,
      marginBottom: 14,
    },
  },
  termsCheck: {
    '.mantine-Checkbox-body': {
      position: 'relative',
      marginTop: 80,
      '@media (max-width: 768px)': {
        marginTop: 54,
      },
    },
    '.mantine-Checkbox-inner': {
      position: 'absolute',
      top: 14,
      left: 20,
      width: 22,
      height: 22,
      '@media (max-width: 768px)': {
        width: 18,
        height: 18,
        left: 10,
        top: 15,
      },
    },
    '.mantine-Checkbox-input': {
      width: 22,
      height: 22,
      borderRadius: 0,
      cursor: 'pointer',
      '&:checked': {
        backgroundColor: '#3C3C3C',
        borderColor: '#3C3C3C',
      },
      '@media (max-width: 768px)': {
        width: 18,
        height: 18,
      },
    },
    '.mantine-Checkbox-label': {
      fontSize: 18,
      backgroundColor: 'white',
      boxShadow: '0px 1px 2px #00000029',
      padding: '15px 20px 15px 62px',
      cursor: 'pointer',
      borderRadius: '3px',
      a: {
        color: '#43749A',
        textDecoration: 'underline',
      },
      '@media (max-width: 768px)': {
        fontSize: 12,
        paddingLeft: 38,
      },
    },
    '.mantine-Checkbox-labelWrapper': {
      flexGrow: 1,
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  textFieldStyles: {
    label: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 20,
      color: '#3C3C3C',
      '@media (max-width: 768px)': {
        fontSize: 22,
      },
    },
    description: {
      fontSize: 16,
      marginTop: 26,
      color: '#3C3C3C',
      '@media (max-width: 768px)': {
        fontSize: 14,
        marginTop: 14,
        marginBottom: 10,
      },
      a: {
        fontWeight: 'bold',
        textDecoration: 'underline',
        color: '#43749A',
      },
    },
  },
};
