import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, Checkbox, Text } from '@mantine/core';
import { TextField } from 'components/Form';
import { useMutate } from 'hooks';
import type { CheckPhonePayload, ICheckPhone } from 'models/auth';
import { authQuery } from 'models/auth';
import Link from 'next/link';
import { useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import helpers from 'utils/helpers';
import notification from 'utils/notification';

import type { PhoneFormValues } from './schema';
import { schema } from './schema';
import { styles, sx } from './styles';

const PhoneForm: React.FC<{
  initialValues?: PhoneFormValues;
  description?: React.ReactNode;
  label: string;
  placeholder: string;
  onSubmit: (values: ICheckPhone) => void;
  loading?: boolean;
  isLogin?: boolean;
}> = ({
  initialValues,
  label,
  description,
  placeholder,
  onSubmit,
  loading,
  isLogin,
}) => {
  const [checked, setChecked] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<PhoneFormValues>({
    defaultValues: initialValues,
    mode: 'onBlur',
    resolver: yupResolver(schema),
  });

  const { mutateAsync: checkPhoneFn, isLoading: isCheckingPhone } = useMutate<
    CheckPhonePayload,
    ICheckPhone
  >(authQuery.checkPhone);

  const handleSubmitForm: SubmitHandler<PhoneFormValues> = (values) => {
    const oPhone = helpers.transfromPhone(values.phoneNumber);
    // check phone
    checkPhoneFn(
      { ...oPhone, action: isLogin ? 'login' : 'register' },
      {
        onSuccess: (data) => {
          if (data?.error) {
            notification.show({
              type: 'error',
              message: data.error,
            });
            return;
          }
          onSubmit({
            ...data,
            ...oPhone,
          });
        },
      },
    );
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(handleSubmitForm)}
      sx={sx.formWrapper}
    >
      <TextField
        control={control}
        description={description}
        label={label}
        maxLength={13}
        name="phoneNumber"
        placeholder={placeholder}
        styles={styles.textFieldStyles}
        sx={isLogin ? sx.extraStyle : {}}
        type="tel"
      />
      {!isLogin ? (
        <>
          <Checkbox
            checked={checked}
            label={
              <>
                <Link
                  href={`${process.env.NEXT_PUBLIC_LP_URL}/terms-of-use/index.html`}
                  rel="noreferrer"
                  target="_blank"
                >
                  利用規約
                </Link>
                、
                <Link
                  href={`${process.env.NEXT_PUBLIC_LP_URL}/privacy.html`}
                  rel="noreferrer"
                  target="_blank"
                >
                  プライバシーポリシー
                </Link>
                に同意します。
              </>
            }
            onChange={(event) => setChecked(event.currentTarget.checked)}
            sx={sx.termsCheck}
          />
          <Text color="blackOlive" sx={sx.termsSubTitle}>
            ご登録には、利用規約、プライバシポリシーに同意いただく必要がございます。
          </Text>
        </>
      ) : (
        <></>
      )}
      <Button
        disabled={(isLogin ? false : !checked) || !isValid}
        loading={loading || isCheckingPhone}
        size="lg"
        sx={sx.submitBtn}
        type="submit"
      >
        次へ
      </Button>
    </Box>
  );
};

export default PhoneForm;
