import { yupResolver } from '@hookform/resolvers/yup';
import IconChevronRight from '@icons/icon-chevron-right.svg';
import { Box, Button, Flex, Text } from '@mantine/core';
import { TextField } from 'components/Form';
import { useCountdown } from 'hooks';
import React, { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { get, useForm } from 'react-hook-form';
import { FIREBASE_AUTH_ERRORS, SMS_COUNTDOWN } from 'utils/constants';
import { eventLog } from 'utils/helpers';

import type { OtpVerifyFormValues } from './schema';
import { schema } from './schema';
import { styles, sx } from './styles';

const OtpVerifyForm: React.FC<{
  initialValues: OtpVerifyFormValues;
  onSubmit: SubmitHandler<OtpVerifyFormValues>;
  isLogin?: boolean;
  onCancel?: () => void;
  onResendOtp: (type: 'call' | 'sms') => Promise<void>;
  isLoading?: boolean;
  label?: string;
  description?: React.ReactNode;
  type?: string;
}> = ({
  initialValues,
  onSubmit,
  onCancel,
  onResendOtp,
  isLogin = false,
  isLoading = false,
  label = '認証コードの入力',
  description = 'SMSまたは音声案内で受け取った6桁の認証コードを入力してください。',
  type = '',
}) => {
  const {
    firstEncounter,
    count,
    start: startCount,
    isDone,
  } = useCountdown({
    countStart: SMS_COUNTDOWN,
  });
  const {
    control,
    handleSubmit,
    formState: { isDirty, isValid },
  } = useForm<OtpVerifyFormValues>({
    defaultValues: initialValues,
    mode: 'onBlur',
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    startCount();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleResendOtp = async (via: 'call' | 'sms') => {
    try {
      if (isDone) {
        await onResendOtp(via);
        startCount();
        eventLog('resend_otp', {
          type: via === 'call' ? 'voice' : 'sms',
          status: 'success',
        });
      }
    } catch (e) {
      const errorCode = get(e, 'code', '');
      const errorMessage =
        FIREBASE_AUTH_ERRORS[errorCode] ||
        'システムエラーです。申し訳ございません。1つ前の画面に戻り、電話番号を再度送信してください。';
      eventLog('resend_otp', {
        type: via === 'call' ? 'voice' : 'sms',
        status: via === 'call' ? get(e, 'error') : errorMessage,
      });
    }
  };

  const minute = Math.trunc(count / 60);
  const second = count % 60;

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={sx.otpVerifyFormWrapper}
    >
      <TextField
        control={control}
        description={description}
        inputWrapperOrder={['label', 'description', 'input', 'error']}
        label={label}
        maxLength={6}
        name="code"
        placeholder="認証コード"
        styles={styles.textFieldStyles}
        sx={isLogin ? sx.extraStyles : {}}
        type="number"
      />
      <Flex
        align={{ base: 'center', sm: 'flex-end' }}
        direction="column"
        mt={{ base: 14, sm: 18 }}
        w="100%"
      >
        {!isDone && (
          <Text color="blackOlive" mb={16} size={16} sx={sx.countDown}>
            {minute}:{second < 10 ? '0' : ''}
            {second} 後にコードを再送できます
          </Text>
        )}
        {!firstEncounter && (
          <>
            <Text
              color={isDone ? 'queenBlue' : 'philippine'}
              data-disabled={!isDone}
              mb={{ base: 24, sm: 16 }}
              onClick={() => handleResendOtp('sms')}
              sx={sx.resendBtn}
              variant="link"
            >
              認証コードを再送信
              <IconChevronRight />
            </Text>
            {!type && (
              <Text
                color={isDone ? 'queenBlue' : 'philippine'}
                data-disabled={!isDone}
                onClick={() => handleResendOtp('call')}
                sx={sx.resendBtn}
                variant="link"
              >
                自動音声案内でコードを受け取る
                <IconChevronRight />
              </Text>
            )}
          </>
        )}
      </Flex>
      <Flex
        gap={16}
        justify={onCancel ? 'space-between' : 'center'}
        mt={{ base: 32, sm: 58 }}
      >
        {type.includes('Account') ? (
          <>
            <Button onClick={onCancel} size="lg" sx={sx.cancelBtn}>
              前に戻る
            </Button>
            <Button
              color="marigold"
              disabled={!isValid || !isDirty}
              loading={isLoading}
              size="lg"
              sx={sx.otpSubmitBtn}
              type="submit"
            >
              {type === 'deleteAccount' ? '申請' : 'アカウント復元'}
            </Button>
          </>
        ) : (
          <>
            {onCancel && (
              <Button
                color="dark"
                onClick={onCancel}
                size="lg"
                sx={sx.cancelBtn}
                variant="outline"
              >
                戻って再送信
              </Button>
            )}
            <Button
              disabled={!isValid || !isDirty}
              loading={isLoading}
              size="lg"
              sx={sx.otpSubmitBtn}
              type="submit"
            >
              {isLogin ? '確認する' : '次へ'}
            </Button>
          </>
        )}
      </Flex>
    </Box>
  );
};

export default OtpVerifyForm;
