import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  otpVerifyFormWrapper: {
    display: 'flex',
    flexDirection: 'column',
  },
  resendOtpWrapper: {
    marginTop: 18,
    flexWrap: 'wrap',
    '@media (max-width: 768px)': {
      marginTop: 14,
      '.mantine-Text-root': {
        fontSize: 13,
      },
    },
  },
  resendBtn: (theme) => ({
    cursor: 'pointer',
    textDecoration: 'underline',
    textUnderlineOffset: '8px',
    fontSize: 16,
    lineHeight: '24px',
    '&[data-disabled=true]': {
      cursor: 'not-allowed',
    },
    [theme.fn.smallerThan('sm')]: {
      fontSize: 14,
      lineHeight: '20px',
    },
    svg: {
      marginLeft: 8,
    },
  }),
  countDown: {
    '@media (max-width: 768px)': {
      fontSize: 14,
      flex: '1 1 100%',
      textAlign: 'center',
    },
  },
  otpSubmitBtn: {
    maxWidth: 300,
    width: '100%',
    '@media (max-width: 768px)': {
      maxWidth: 180,
    },
  },
  cancelBtn: {
    maxWidth: 300,
    width: '100%',
    '@media (max-width: 768px)': {
      maxWidth: 180,
    },
  },
  extraStyles: {
    '.mantine-TextInput-label': {
      fontSize: 30,
      '@media (max-width: 768px)': {
        fontSize: 22,
      },
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  textFieldStyles: {
    label: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 16,
      color: '#3C3C3C',
      '@media (max-width: 768px)': {
        fontSize: 22,
        marginBottom: 12,
      },
    },
    description: {
      fontSize: 16,
      marginTop: 0,
      marginBottom: 24,
      color: '#3C3C3C',
      whiteSpace: 'normal',
      '@media (max-width: 768px)': {
        fontSize: 14,
        whiteSpace: 'pre-line',
        marginBottom: 20,
      },
    },
  },
};
