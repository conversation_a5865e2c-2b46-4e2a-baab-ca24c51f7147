import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  notiTabWrapper: {
    '@media (max-width: 768px)': {
      borderRadius: 4,
      border: 'solid 1px #bdccd3',
      backgroundColor: 'white',
      marginTop: 16,
      '&[data-ispage="true"]': {
        border: 'none',
        marginLeft: -16,
        marginRight: -16,
        marginTop: 8,
      },
    },
  },

  skeleton: {
    '@media (max-width: 768px)': {
      padding: '10px 16px',
    },
  },

  groupDescription: {
    '@media (max-width: 768px)': {
      '> *': {
        maxWidth: '50%',
      },
    },
  },
};

export const styles: Record<string, any> = {
  notiTab: (theme: MantineTheme) => ({
    root: {
      '@media (max-width: 768px)': {
        marginTop: 0,
      },
    },
    tab: {
      height: 64,
    },
    tabsList: {
      marginTop: 30,
      marginBottom: 30,
      '@media (max-width: 768px)': {
        margin: 0,
        padding: 16,
      },
    },
    panel: {
      padding: 10,
      borderRadius: 4,
      backgroundColor: 'white',
      maxHeight: 700,
      overflow: 'auto',
      '&[data-empty="true"]': {
        background: 'none',
        fontSize: 16,
        color: theme.colors.sonicSilver,
        svg: {
          marginTop: 20,
          marginBottom: 20,
        },
        '@media (max-width: 768px)': {
          fontSize: 14,
          marginTop: 14,
          svg: {
            width: 120,
            height: 'auto',
          },
        },
      },
      '@media (max-width: 768px)': {
        padding: 0,
      },
    },
  }),

  pageNotiTab: {
    root: {
      '[data-ispage="true"] &': {
        '@media (max-width: 768px)': {
          border: 'none',
        },
      },
    },
    panel: {
      '[data-ispage="true"] &': {
        maxHeight: '100%',
      },
    },
  },

  goHomeBtn: {
    root: {
      marginTop: 40,
      height: 60,
      fontSize: 18,
      minWidth: 300,
      '@media (max-width: 768px)': {
        display: 'none',
      },
    },
  },

  seeMoreBtn: {
    root: {
      fontSize: 14,
      marginTop: 7,
      marginBottom: 7,
      textDecoration: 'underline',
      textUnderlineOffset: 2,
      '&:hover': {
        backgroundColor: 'transparent',
      },
      '@media (min-width: 769px)': {
        display: 'none',
      },
    },
  },

  markReadAllBtn: {
    root: {
      boxShadow: 'none !important',
      fontSize: 16,
      padding: 0,
      '&:hover': {
        backgroundColor: 'transparent',
      },
      '&:disabled': {
        background: 'none',
      },
      '@media (max-width: 768px)': {
        fontSize: 14,
        '&:not([data-ispage="true"])': {
          display: 'none',
        },
      },
    },
  },
};
