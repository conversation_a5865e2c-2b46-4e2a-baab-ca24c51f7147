import IconNotiEmpty from '@icons/noti/icon-noti-empty.svg';
import { Box, Button, Flex, Group, Tabs, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useMutate, useTotalNotification, useUser } from 'hooks';
import { merge } from 'lodash';
import type { INotificationItem } from 'models/notification';
import { notificationQuery } from 'models/notification';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import request from 'utils/request';

import NotificationItem from '../NotificationItem';
import ItemSkeleton from './ItemSkeleton';
import { styles, sx } from './styles';

const NOTIFICATION_LIMIT = 15;

const NotificationList: React.FC<{}> = () => {
  const router = useRouter();
  const { group: initGroup } = router.query;
  const isPage = router.pathname === '/my-page/notifications';
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const { data: user } = useUser();
  const { ref, inView } = useInView();
  const { mutateAsync: markReadAllFn } = useMutate<unknown, unknown>(
    notificationQuery.markReadAllNotification,
  );
  const { refetch: reloadSumNotiFn, data: summaryNotification } =
    useTotalNotification();

  const [group, setGroup] = useState(
    typeof initGroup === 'string' ? initGroup : 'bookings',
  );

  // TODO: Recreate custom hook that can be reuse
  const {
    fetchNextPage,
    isLoading: isFetchingNotifications,
    data: notificationData,
    hasNextPage,
    remove: removeNotifications,
  } = useInfiniteQuery<INotificationItem[]>({
    queryKey: ['currentUser', 'notification-list', { group }],
    queryFn: async ({ pageParam = 1 }) => {
      const { data: result } = await request({
        url: notificationQuery.getNotificationList.apiUrl,
        data: {
          page: pageParam,
          limit: NOTIFICATION_LIMIT,
          group,
        },
        method: notificationQuery.getNotificationList.method,
      });
      return result;
    },
    getNextPageParam: (lastPage, allPage) => {
      if ((lastPage?.length || 0) >= NOTIFICATION_LIMIT) {
        return allPage.length + 1;
      }
      return undefined;
    },
  });
  const notificationPages =
    !isPage && mobileScreen
      ? [notificationData?.pages[0]?.slice(0, 2) || []]
      : notificationData?.pages || [];

  useEffect(() => {
    return () => {
      removeNotifications();
    };
  }, [removeNotifications]);

  const handleChangeTab = (value: string) => {
    removeNotifications();
    setGroup(value);
  };

  const reloadFn = () => {
    removeNotifications();
    reloadSumNotiFn();
  };

  const markReadAll = () => {
    if (!summaryNotification?.total) return;
    markReadAllFn(
      {},
      {
        onSuccess: () => {
          reloadFn();
        },
      },
    );
  };

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNotifications) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, inView, isFetchingNotifications]);

  const renderTabPanel = () => {
    return (
      <Tabs.Panel
        data-empty={notificationPages[0]?.length === 0}
        pt="xs"
        value={group}
      >
        {notificationPages.map((notifications) => {
          return notifications.map((notification) => (
            <NotificationItem
              key={notification._id}
              large
              notification={notification}
              reloadFn={reloadFn}
            />
          ));
        })}
        {!hasNextPage &&
          !isFetchingNotifications &&
          notificationPages[0]?.length === 0 && (
            <Flex align="center" direction="column">
              {(isPage || !mobileScreen) && <IconNotiEmpty />}
              <Text>
                {group === 'bookings'
                  ? '予約/セラピストの新着情報はありません。'
                  : 'キャンペーン/運営の新着情報はありません。'}
              </Text>
            </Flex>
          )}
        {(hasNextPage || isFetchingNotifications) &&
          (isPage || !mobileScreen || isFetchingNotifications) && (
            <Box ref={hasNextPage ? ref : undefined}>
              <ItemSkeleton />
              {notificationPages?.length === 0 && (
                <>
                  <ItemSkeleton />
                  <ItemSkeleton />
                  <ItemSkeleton />
                </>
              )}
            </Box>
          )}
      </Tabs.Panel>
    );
  };

  return (
    <>
      <Title order={3}>マイページ</Title>
      <Title order={4}>{user?.name}さんへの新着情報</Title>
      <Flex align="center" justify="space-between" sx={sx.groupDescription}>
        <small>過去60日間のお知らせ履歴を表示しています。</small>
        <Button
          data-ispage={isPage}
          onClick={markReadAll}
          styles={styles.markReadAllBtn}
          variant="subtle"
        >
          ✔︎すべて既読にする
        </Button>
      </Flex>
      <Box data-ispage={isPage} sx={sx.notiTabWrapper}>
        <Tabs
          onTabChange={handleChangeTab}
          styles={(theme) =>
            merge({}, styles.notiTab(theme), isPage ? styles.pageNotiTab : {})
          }
          unstyled
          value={group}
        >
          <Tabs.List>
            <Tabs.Tab value="bookings">予約/セラピスト</Tabs.Tab>
            <Tabs.Tab value="others">キャンペーン/運営</Tabs.Tab>
          </Tabs.List>

          {renderTabPanel()}
        </Tabs>
        <Group position="center">
          {!isPage && (
            <Button
              component={Link}
              href={process.env.NEXT_PUBLIC_LP_DOMAIN || '/'}
              styles={styles.goHomeBtn}
            >
              ホームヘ戻る
            </Button>
          )}
          {!isPage && (
            <Button
              component={Link}
              href={{
                pathname: '/my-page/notifications',
                query: {
                  group,
                },
              }}
              styles={styles.seeMoreBtn}
              variant="subtle"
            >
              もっと見る
            </Button>
          )}
        </Group>
      </Box>
    </>
  );
};

export default NotificationList;
