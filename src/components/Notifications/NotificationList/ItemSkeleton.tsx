import { Flex, Skeleton } from '@mantine/core';
import React from 'react';

import { sx } from './styles';

const ItemSkeleton = () => {
  return (
    <Flex align="center" columnGap={12} px={10} py={20} sx={sx.skeleton}>
      <Skeleton circle height={40} sx={{ minWidth: 40 }} />
      <Flex direction="column" rowGap={12} sx={{ width: '100%' }}>
        <Skeleton height={8} radius={0} width="50%" />
        <Skeleton height={8} radius={0} width="25%" />
      </Flex>
    </Flex>
  );
};

export default ItemSkeleton;
