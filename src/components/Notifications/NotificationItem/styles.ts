import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {};

export const styles: Record<string, any> = {
  notiItem: (theme: MantineTheme) => ({
    root: {
      width: '100%',
      minHeight: 85,
      padding: 10,
      fontWeight: 'normal',
      '&:not([data-read="true"])': {
        backgroundColor: theme.colors.ghostWhite,
        fontWeight: 'bold',
      },
      '& + * + &': {
        marginTop: 2,
      },
      '@media (max-width: 768px)': {
        '&[data-large="true"]': {
          padding: '10px 16px',
        },
      },
    },
    label: {
      fontSize: 14,
      wordBreak: 'normal',
      '@media (max-width: 768px)': {
        fontSize: 12,
        '[data-large="true"] &': {
          fontSize: 14,
        },
      },
    },
    icon: {
      alignSelf: 'center',
      paddingTop: 0,
      '@media (max-width: 768px)': {
        svg: {
          width: 36,
          height: 'auto',
          '[data-large="true"] &': {
            width: 40,
          },
        },
      },
    },
    description: {
      fontWeight: 'normal',
      fontSize: 12,
    },
  }),
};
