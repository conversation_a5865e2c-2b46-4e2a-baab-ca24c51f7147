import IconArrived from '@icons/noti/icon-arrived.svg';
import IconBookingCanceled from '@icons/noti/icon-booking-canceled.svg';
import IconBookingDone from '@icons/noti/icon-booking-done.svg';
import IconBookingUnconfirmed from '@icons/noti/icon-booking-unconfirmed.svg';
import IconChargeFailed from '@icons/noti/icon-charge-failed.svg';
import IconCoupon from '@icons/noti/icon-coupon.svg';
import IconDefault from '@icons/noti/icon-default.svg';
import IconOther from '@icons/noti/icon-other.svg';
import IconRemindReview from '@icons/noti/icon-remind-review.svg';
import { NavLink, Text } from '@mantine/core';
import { useMutate } from 'hooks';
import type { INotificationItem } from 'models/notification';
import { notificationQuery } from 'models/notification';
import { useRouter } from 'next/router';
import { FormattedMessage } from 'react-intl';
import dayjs from 'utils/dayjs';

import { styles } from './styles';

const NotificationItem: React.FC<{
  large?: boolean;
  notification: INotificationItem;
  reloadFn: () => void;
}> = ({ large, notification, reloadFn }) => {
  const router = useRouter();
  const { mutateAsync: markReadBookingNotiFn } = useMutate<
    { bookingId: string },
    { status: boolean }
  >(notificationQuery.markReadBookingNotification);
  const { mutateAsync: markReadNotiFn } = useMutate<
    { notificationId: string },
    { status: boolean }
  >(notificationQuery.markReadNotification);

  const renderIcon = () => {
    switch (notification.type) {
      case 'AdminCancelBooking':
      case 'AutoCancelStandardBooking':
      case 'TherapistDenyListingBooking':
      case 'TherapistDiscardBooking':
        return <IconBookingCanceled />;
      case 'RemindCustomerReview':
        return <IconRemindReview />;
      case 'AdminCompleteBooking':
      case 'TherapistFinishBooking':
      case 'TherapistFinishBookingWithoutTreatment':
        return <IconBookingDone />;
      case 'TherapistCheckIn':
        return <IconArrived />;
      case 'CustomerPaymentFailed':
        return <IconChargeFailed />;
      case 'RemindCustomerNextAppointmentBookings':
      case 'RemindCustomerUnconfirmBookingAfterDay':
      case 'RemindCustomerUnconfirmBookingBeforeHour':
      case 'RemindCustomerBookingWillStart':
        return <IconBookingUnconfirmed />;
      case 'RemindCustomerMakeBookingAfterWeek':
      case 'RemindCustomerMakeBookingAfterMonth':
      case 'RemindCustomerUseAppAfterHalfMonth':
      case 'RemindCustomerUseAppAfterMonth':
      case 'NewsAnnouncement':
      case 'GiveCouponToCustomerNotUsedService':
        return <IconDefault />;
      case 'CustomerIncentiveCouponWasIncreased':
      case 'GivenInvitationCouponToCustomer':
        return <IconCoupon />;
      default:
        return <IconOther />;
    }
  };

  const handleCLick = () => {
    let url = '';
    if (notification.data.bookingId) {
      url = `/booking/${notification.data.bookingId}`;
      if (notification.type === 'RemindCustomerReview')
        url += '/review?ref=NotificationList';
    }
    if (notification.type === 'CustomerIncentiveCouponWasIncreased') {
      url = '/my-page/coupons';
    }
    if (notification.read && url) {
      router.push(url);
      return;
    }
    if (notification.data.bookingId) {
      markReadBookingNotiFn(
        { bookingId: notification.data.bookingId },
        {
          onSuccess: () => {
            router.push(url);
          },
        },
      );
      return;
    }
    markReadNotiFn(
      { notificationId: notification._id },
      {
        onSuccess: () => {
          reloadFn();
          if (url) router.push(url);
        },
      },
    );
  };

  return (
    <NavLink
      data-large={large}
      data-read={notification.read}
      description={dayjs(notification.createdAt).format('YYYY/MM/DD HH:mm')}
      icon={renderIcon()}
      label={
        <FormattedMessage
          defaultMessage={notification.message}
          id={notification._id}
          values={{
            ...notification.data,
            dateTime_booking:
              notification.data.dateTime_booking &&
              dayjs(notification.data.dateTime_booking).format(
                'YYYY年MM月DD日 HH:mm',
              ),
            time_arrivalTime:
              notification.data.time_arrivalTime &&
              dayjs(notification.data.time_arrivalTime).format(
                'YYYY年MM月DD日 HH:mm',
              ),
          }}
        />
      }
      onClick={handleCLick}
      rightSection={notification.read ? null : <Text className="blue-dot" />}
      styles={styles.notiItem}
    />
  );
};

export default NotificationItem;
