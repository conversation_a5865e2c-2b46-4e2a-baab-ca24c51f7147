const Shadow = ({ icon }: { icon: React.ReactNode }) => {
  if (icon) {
    return (
      <div style={{ zIndex: 0 }}>
        {icon}
        <div
          style={{
            opacity: 0.77,
            filter: 'blur(11px)',
            width: 48,
            height: 26,
            position: 'absolute',
            backgroundColor: '#203d54',
            bottom: 0,
            left: '50%',
            transform: 'translate(-50%, 0)',
            zIndex: -1,
          }}
        />
      </div>
    );
  }

  return null;
};

export default Shadow;
