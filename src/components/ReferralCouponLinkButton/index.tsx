import IconChevronRightWhite from '@icons/icon-chevron-right-wt.svg';
import IconPresentCoupon from '@icons/icon-present-coupon.svg';
import { NavLink } from '@mantine/core';
import Link from 'next/link';

import Shadow from './Shadow';
import { styles, sx } from './styles';

const REFERRAL_COUPON_PATH = '/my-page/coupons';

const ReferralCouponLinkButton: React.FC = () => {
  return (
    <NavLink
      component={Link}
      description="2000円クーポンをお友達にプレゼント!さらにご紹介していただいたお友達がはじめてホググご利用で、あなたにも2000円クーポンをプレゼント!!"
      href={REFERRAL_COUPON_PATH}
      icon={<Shadow icon={<IconPresentCoupon />} />}
      label={'お友達紹介プログラム'}
      rightSection={<IconChevronRightWhite />}
      styles={styles.couponNavLink}
      sx={sx.navLink}
    ></NavLink>
  );
};

export default ReferralCouponLinkButton;
