import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  navLink: {
    width: 315,
    borderRadius: '8px',
    padding: '16px 14px 16px 16px',
    '@media (max-width: 768px)': {
      width: '100%',
    },
  },
};
export const styles: Record<string, any> = {
  couponNavLink: (theme: MantineTheme) => ({
    root: {
      backgroundColor: theme.colors.queenBlue[6],
      ':hover': {
        backgroundColor: theme.colors.queenBlue[6],
      },
    },
    icon: {
      width: 87,
      marginRight: theme.spacing.sm,
      padding: 0,
      position: 'relative',
      alignSelf: 'center',
    },
    label: {
      fontSize: theme.fontSizes.md,
      color: theme.white,
      lineHeight: 1,
    },
    description: {
      marginTop: 8,
      color: theme.white,
      fontWeight: 500,
      fontSize: theme.fontSizes.sm,
      opacity: 0.7,
      lineHeight: 1.33,
      // letterSpacing: '0.36px',
    },
    rightSection: {
      display: 'none',
      '@media (max-width: 768px)': {
        display: 'unset',
      },
    },
  }),
};
