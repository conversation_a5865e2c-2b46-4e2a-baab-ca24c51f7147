import type { Sx } from '@mantine/core';
import { createStyles } from '@mantine/core';

export const sx: Record<string, Sx> = {
  profileContentWrapper: {
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      alignItems: 'center',
      gap: 32,
    },
  },

  avatarWrapper: {
    flex: '0 1 350px',
    img: {
      cursor: 'pointer',
      objectFit: 'cover',
      objectPosition: 'center',
      borderRadius: '50%',
    },
    '@media (max-width: 768px)': {
      flex: '1 1 auto',
      textAlign: 'center',
      img: {
        width: 120,
        height: 120,
      },
    },
  },

  fileInputText: {
    marginTop: 16,
    cursor: 'pointer',
    textDecoration: 'underline',

    svg: {
      marginLeft: 8,
    },
    '@media (max-width: 768px)': {
      fontSize: 13,
    },
  },
};

const useStyles = createStyles((theme) => ({
  invitationCodeWrapper: {
    background: theme.colors.water,
    borderRadius: '3px',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    display: 'table',
    padding: 20,
    marginTop: 16,
    [theme.fn.smallerThan('sm')]: {
      padding: '16px 12px',
      marginTop: 0,
    },
  },
}));

export default useStyles;
