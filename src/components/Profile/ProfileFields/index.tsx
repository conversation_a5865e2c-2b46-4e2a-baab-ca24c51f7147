import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconDropdown from '@icons/icon-dropdown.svg';
import { Box, Flex, Text } from '@mantine/core';
import type { CompleteProfileFormValues } from 'components/Auth/CompleteProfileForm/schema';
import {
  ChipField,
  DatePickerField,
  FileUpload,
  TextField,
} from 'components/Form';
import Image from 'next/image';
import { useRef } from 'react';
import { GENDER, REGEX } from 'utils/constants';
import dayjs from 'utils/dayjs';

import useStyles, { sx } from './styles';

const GENDER_AVATAR = [
  '/icons/icon-avatar-other.svg',
  '/icons/icon-avatar-male.svg',
  '/icons/icon-avatar-female.svg',
];

const ProfileFields: React.FC<{
  formValues: CompleteProfileFormValues;
  control: any;
  isCompleteProfile?: boolean;
}> = ({ formValues, control, isCompleteProfile }) => {
  const fileInputRef = useRef<HTMLButtonElement>(null);
  const { classes } = useStyles();

  return (
    <>
      <Flex sx={sx.profileContentWrapper}>
        <Box sx={sx.avatarWrapper}>
          <Image
            alt="avatar"
            height={200}
            onClick={() => {
              fileInputRef?.current?.click();
            }}
            src={
              formValues.profilePicture ||
              GENDER_AVATAR[Number(formValues.gender) || 0] ||
              ''
            }
            width={200}
          />
          <Text
            color="queenBlue"
            onClick={() => {
              fileInputRef?.current?.click();
            }}
            size={16}
            sx={sx.fileInputText}
          >
            プロフィール画像を変更
            <IconChevronRight />
          </Text>
          <FileUpload
            accept="image/*"
            control={control}
            innerRef={fileInputRef}
            multiple={false}
            name="profilePicture"
            sx={{ display: 'none' }}
          />
        </Box>
        <Flex direction="column" gap={24} sx={{ flex: '1 1 auto' }}>
          <TextField
            control={control}
            label="登録名"
            maxLength={30}
            name="name"
            placeholder="山田 太郎"
          />
          <ChipField
            allowUnselect
            control={control}
            label="性別"
            name="gender"
            options={['1', '2', '0'].map((key) => ({
              value: key,
              children: GENDER[key],
            }))}
          />
          <DatePickerField
            control={control}
            defaultLevel="year"
            label="生年月日"
            maxDate={dayjs().subtract(18, 'year').endOf('d').toDate()}
            minDate={dayjs('1900/01/01', 'YYYY/MM/DD').toDate()}
            name="birthday"
            placeholder="選択してください"
            rightSection={<IconDropdown />}
            size="lg"
            sx={{
              '.mantine-Input-rightSection': {
                pointerEvents: 'none',
              },
            }}
          />
          <TextField
            control={control}
            disabled
            label="電話番号"
            name="phone"
            placeholder="山田 太郎"
          />
          <TextField
            control={control}
            description="迷惑メール対策でドメイン指定受信を設定されている方は、@hogugu.comを受信するように設定してください。"
            label="メールアドレス"
            name="email"
            placeholder="<EMAIL>"
            transformValue={(value) => {
              const jpRemove = value.replace(REGEX.SPECIAL_CHARACTER, '');
              const emojiRemove = jpRemove.replace(REGEX.EMOJI, '');
              return emojiRemove;
            }}
          />
          {isCompleteProfile && (
            <Box className={classes.invitationCodeWrapper} mih={80}>
              <Box
                display="table-cell"
                pr={{ base: 8, sm: 30 }}
                sx={{
                  verticalAlign: 'middle',
                }}
                w="50%"
              >
                <Text
                  color="#070203"
                  fw="bold"
                  fz={{ base: 16, sm: 20 }}
                  lh={1}
                  mb={{ base: 10, sm: 12 }}
                >
                  紹介コード
                </Text>
                <Text
                  color="#3c3c3c"
                  fz={{ base: 12, sm: 14 }}
                  lh={{ base: 1.33, sm: 1 }}
                >
                  お友達から受け取った招待コードを入力してください
                </Text>
              </Box>
              <Box
                display="table-cell"
                sx={(theme) => ({
                  verticalAlign: 'middle',
                  [theme.fn.smallerThan('sm')]: {
                    verticalAlign: 'bottom',
                  },
                })}
                w="50%"
              >
                <TextField
                  control={control}
                  maxLength={10}
                  name="invitationCode"
                  transformValue={(value) =>
                    value.replace(/[^a-zA-Z0-9]/gi, '')
                  }
                />
              </Box>
            </Box>
          )}
        </Flex>
      </Flex>
    </>
  );
};

export default ProfileFields;
