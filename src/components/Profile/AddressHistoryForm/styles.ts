import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  addressHistoryFormWrapper: {
    position: 'relative',
    padding: '56px 30px 0px',
    height: '100%',
    '@media (max-width: 768px)': {
      overflow: 'auto',
      padding: '60px 20px 102px',
    },
  },

  title: {
    fontSize: 30,
    lineHeight: '45px',
    textAlign: 'center',
    '@media (max-width: 768px)': {
      textAlign: 'left',
      position: 'fixed',
      zIndex: 1,
      backgroundColor: 'white',
      top: 0,
      left: 0,
      width: '100%',
      boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
      height: 60,
      display: 'flex',
      alignItems: 'center',
      fontSize: 18,
      lineHeight: '27px',
      padding: '0 42px 0 20px',
    },
  },

  subTitle: {
    fontSize: 16,
    lineHeight: '24px',
    marginTop: 16,
    marginBottom: 10,
    textAlign: 'center',
    '@media (max-width: 768px)': {
      fontSize: 14,
      lineHeight: '20px',
      textAlign: 'left',
      marginTop: 20,
      marginBottom: 8,
    },
  },

  highlightText: {
    color: '#db1e0e',
    fontSize: 14,
    lineHeight: '20px',
    textAlign: 'center',
    marginBottom: 24,
    '@media (max-width: 768px)': {
      fontSize: 12,
      lineHeight: '16px',
      textAlign: 'left',
      marginBottom: 16,
    },
  },

  groupBtn: {
    position: 'sticky',
    bottom: 0,
    left: 0,
    margin: 0,
    padding: '30px 0',
    backgroundColor: 'white',
    '@media (max-width: 768px)': {
      position: 'fixed',
      boxShadow: '0 -4px 8px 0 rgba(0, 0, 0, 0.06)',
      width: '100%',
      padding: '20px',
    },
  },

  optionalBadge: {
    display: 'inline-block',
    marginLeft: 8,
    padding: '2px 6px',
    borderRadius: 12,
    fontSize: 12,
    color: '#43749A',
    border: '1px solid #43749A',
  },

  hotelInfoBlock: {
    marginTop: 8,
    padding: '12px 18px',
    backgroundColor: '#F0F6F9',
    borderRadius: 2,
    gap: 8,
  },
  hotelInfoText: {
    flexGrow: 1,
    color: '#225277',
    strong: {
      color: '#db1e0e',
      fontWeight: 'normal',
    },
  },
};
