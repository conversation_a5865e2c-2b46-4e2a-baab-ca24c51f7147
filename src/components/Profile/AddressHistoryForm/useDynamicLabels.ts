import { useMemo } from 'react';

import { DEFAULT_LABELS, FIELD_LABELS } from './constants';
import type { BuildingType } from './schema';

type FieldLabels = {
  [key: string]: {
    label: string;
    placeholder?: string;
  };
};

const useDynamicLabels = (buildingType: BuildingType | ''): FieldLabels => {
  return useMemo<FieldLabels>(() => {
    if (!buildingType) {
      return DEFAULT_LABELS;
    }

    return FIELD_LABELS[buildingType as BuildingType] || DEFAULT_LABELS;
  }, [buildingType]);
};

export default useDynamicLabels;
