import { useFetchData } from 'hooks';
import type { ICityItem, IPrefectureItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import { useMemo } from 'react';

const mappingOptions = (data?: IPrefectureItem[] | ICityItem[]) =>
  (data || []).map((item) => ({
    label: item.name,
    value: item.areaCode,
  }));

const useAreas = ({
  prefectureCode,
  cityCode,
  wardCode,
  districtCode,
}: {
  prefectureCode?: string;
  cityCode?: string;
  wardCode?: string;
  districtCode?: string;
}) => {
  const { data: prefectures } = useFetchData<IPrefectureItem[]>({
    ...resourceQuery.getAllAreas,
  });
  const prefectureOptions = useMemo(
    () => mappingOptions(prefectures),
    [prefectures],
  );
  const selectedPrefecture = useMemo(
    () => prefectures?.find((i) => i.areaCode === prefectureCode),
    [prefectureCode, prefectures],
  );

  const cityOptions = useMemo(
    () => mappingOptions(selectedPrefecture?.children),
    [selectedPrefecture?.children],
  );
  const selectedCity = useMemo(
    () => selectedPrefecture?.children?.find((i) => i.areaCode === cityCode),
    [cityCode, selectedPrefecture?.children],
  );

  const wardOptions = useMemo(
    () => mappingOptions(selectedCity?.children),
    [selectedCity?.children],
  );
  const selectedWard = useMemo(
    () => selectedCity?.children?.find((i) => i.areaCode === wardCode),
    [wardCode, selectedCity?.children],
  );

  const districtOptions = useMemo(
    () => mappingOptions(selectedWard?.children),
    [selectedWard?.children],
  );
  const selectedDistrict = useMemo(
    () => selectedWard?.children?.find((i) => i.areaCode === districtCode),
    [districtCode, selectedWard?.children],
  );

  return {
    prefectureOptions,
    selectedPrefecture,
    cityOptions,
    selectedCity,
    wardOptions,
    selectedWard,
    districtOptions,
    selectedDistrict,
  };
};

export default useAreas;
