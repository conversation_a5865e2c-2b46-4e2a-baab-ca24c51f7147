import { yupResolver } from '@hookform/resolvers/yup';
import type { Sx } from '@mantine/core';
import { Box, Button, Flex, Text, Title } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { SelectField, TextField } from 'components/Form';
import ImageView from 'components/ImageView';
import { merge } from 'lodash';
import type { AddressHistoryItem } from 'models/address';
import { useEffect, useMemo, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';

import { BUILDING_TYPE_OPTIONS } from './constants';
import LabelWithMap from './LabelWithMap';
import OptionalBadge from './OptionalBadge';
import type { AddressHistoryFormValues, BuildingType } from './schema';
import { schema, validationRules } from './schema';
import { sx } from './styles';
import useAreas from './useAreas';
import useDynamicLabels from './useDynamicLabels';
import WarningMessage from './WarningMessage';

const AddressHistoryForm: React.FC<{
  initialValues?: AddressHistoryFormValues;
  onSubmit: SubmitHandler<AddressHistoryItem>;
  loading?: boolean;
  backFn: () => void;
  customSx?: Sx;
}> = ({ initialValues, onSubmit, loading, backFn, customSx }) => {
  const [mapOpened, mapHandlers] = useDisclosure(false);
  const [imageUrl, setImageUrl] = useState('');
  const [hasWard, setHasWard] = useState(false);
  const [hasDistrict, setHasDistrict] = useState(false);
  // State to track building type separately
  const [buildingType, setBuildingType] = useState<BuildingType | undefined>(
    initialValues?.buildingType as BuildingType | undefined,
  );

  // Create resolver with the separate building type state
  const resolver = useMemo(
    () => yupResolver(schema(hasWard, hasDistrict, buildingType)),
    [hasWard, hasDistrict, buildingType],
  );

  const { control, handleSubmit, watch, setValue } =
    useForm<AddressHistoryFormValues>({
      defaultValues: initialValues,
      mode: 'onBlur',
      resolver,
    });

  const formValues = watch();

  // Update building type state when form value changes
  useEffect(() => {
    if (formValues.buildingType !== buildingType) {
      setBuildingType(formValues.buildingType as BuildingType | undefined);
    }
  }, [formValues.buildingType, buildingType]);

  // Use our custom hook to get dynamic labels based on the selected building type
  const dynamicLabels = useDynamicLabels(formValues.buildingType);

  // Check if building type is selected
  const isBuildingTypeSelected = !!formValues.buildingType;
  const selectedBuildingType = formValues.buildingType as BuildingType;

  // Determine which fields are required based on the selected building type
  const isNameplateRequired =
    isBuildingTypeSelected && validationRules[selectedBuildingType]?.nameplate;
  const isBuildingDetailsRequired =
    isBuildingTypeSelected &&
    validationRules[selectedBuildingType]?.buildingDetails;
  const isAccessMethodRequired =
    isBuildingTypeSelected &&
    validationRules[selectedBuildingType]?.accessMethod;

  const {
    prefectureOptions,
    selectedPrefecture,
    cityOptions,
    selectedCity,
    wardOptions,
    selectedWard,
    districtOptions,
    selectedDistrict,
  } = useAreas({
    prefectureCode: formValues.prefecture,
    cityCode: formValues.city,
    wardCode: formValues.ward,
    districtCode: formValues.district,
  });

  useEffect(() => {
    // handle change prefecture
    if (selectedPrefecture?.areaCode !== selectedCity?.parent) {
      setValue('city', '');
      setValue('ward', '');
      setValue('district', '');
    }
  }, [selectedCity?.parent, selectedPrefecture?.areaCode, setValue]);

  useEffect(() => {
    // handle change city
    if (selectedCity?.areaCode !== selectedWard?.parent) {
      setValue('ward', '');
      setValue('district', '');
    }
  }, [selectedCity?.areaCode, selectedWard?.parent, setValue]);

  useEffect(() => {
    // handle change ward
    if (selectedWard?.areaCode) {
      setValue('district', '');
    }
  }, [selectedWard?.areaCode, setValue]);

  useEffect(() => {
    setHasWard(!!wardOptions.length);
  }, [wardOptions.length]);

  useEffect(() => {
    setHasDistrict(!!districtOptions.length);
  }, [districtOptions.length]);

  const handleOpenMap = (url = '') => {
    setImageUrl(url);
    mapHandlers.open();
  };

  const handleOnSubmit = (values: AddressHistoryFormValues) => {
    const areaCodes = [
      values.prefecture,
      values.city,
      selectedWard?.areaCode,
      selectedDistrict?.areaCode,
    ].filter((i) => !!i) as string[];
    const address = [
      selectedPrefecture?.name,
      selectedCity?.name,
      selectedWard?.name || values.ward,
      selectedDistrict?.name || values.district,
      values.address,
    ].filter((i) => !!i) as string[];
    onSubmit({
      address: address.join(', ').replace('23区内,', ''),
      buildingType: values.buildingType,
      nameplate: values.nameplate,
      buildingDetails: values.buildingDetails,
      accessMethod: values.accessMethod,
      areaCodes,
    });
  };

  // Get selected building type to determine when to show the warning
  const isHotelSelected = formValues.buildingType === 'HOTEL';

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(handleOnSubmit)}
      sx={merge({}, sx.addressHistoryFormWrapper, customSx)}
    >
      <Title className="title" order={3} sx={sx.title}>
        エリアから入力
      </Title>
      <Text className="description" color="blackOlive" sx={sx.subTitle}>
        番地・建物名など正確な訪問先を必ず記入してください
      </Text>
      <Text className="note" sx={sx.highlightText}>
        ※選択肢に表示されない都道府県・市区町村は現在のサービスご提供エリア外となります。
      </Text>
      <Flex direction="column" gap={{ base: 24, sm: 30 }}>
        <SelectField
          control={control}
          data={prefectureOptions}
          label="都道府県"
          name="prefecture"
          placeholder="選択してください"
        />
        <SelectField
          control={control}
          data={cityOptions}
          label="市区町村1"
          name="city"
          placeholder="選択してください"
        />
        {wardOptions.length ? (
          <SelectField
            control={control}
            data={wardOptions}
            label={
              selectedCity?.image?.url ? (
                <LabelWithMap
                  label="市区町村2"
                  onClick={() => handleOpenMap(selectedCity?.image?.url)}
                />
              ) : (
                '市区町村2'
              )
            }
            labelProps={{
              display: 'block',
            }}
            name="ward"
            placeholder="ご入力ください"
          />
        ) : (
          <TextField
            control={control}
            label={
              selectedCity?.image?.url ? (
                <LabelWithMap
                  label="市区町村2"
                  onClick={() => handleOpenMap(selectedCity?.image?.url)}
                />
              ) : (
                '市区町村2'
              )
            }
            labelProps={{
              display: 'block',
            }}
            maxLength={255}
            name="ward"
            placeholder="ご入力ください"
          />
        )}
        {!!districtOptions.length && (
          <SelectField
            control={control}
            data={districtOptions}
            label={
              selectedWard?.image?.url ? (
                <LabelWithMap
                  label="地域"
                  onClick={() => handleOpenMap(selectedWard?.image?.url)}
                />
              ) : (
                '地域'
              )
            }
            labelProps={{
              display: 'block',
            }}
            name="district"
            placeholder="選択してください"
          />
        )}
        <TextField
          control={control}
          label="番地"
          maxLength={255}
          name="address"
          placeholder="六本木○丁目○番地○○"
        />

        <Box>
          <SelectField
            control={control}
            data={BUILDING_TYPE_OPTIONS}
            label="建物の種類"
            name="buildingType"
            placeholder="選択してください"
          />
          {/* Display warning message for hotels */}
          <WarningMessage isShow={isHotelSelected} />
        </Box>

        {/* Only show these fields if building type is selected */}
        {isBuildingTypeSelected && (
          <>
            <TextField
              control={control}
              label={
                <Flex align="center">
                  {dynamicLabels.nameplate?.label}
                  {!isNameplateRequired && <OptionalBadge />}
                </Flex>
              }
              maxLength={255}
              name="nameplate"
              placeholder={dynamicLabels.nameplate?.placeholder}
            />
            <TextField
              control={control}
              label={
                <Flex align="center">
                  {dynamicLabels.buildingDetails?.label}
                  {!isBuildingDetailsRequired && <OptionalBadge />}
                </Flex>
              }
              maxLength={255}
              name="buildingDetails"
              placeholder={dynamicLabels.buildingDetails?.placeholder}
            />
            <TextField
              control={control}
              label={
                <Flex align="center">
                  {dynamicLabels.accessMethod?.label}
                  {!isAccessMethodRequired && <OptionalBadge />}
                </Flex>
              }
              maxLength={255}
              name="accessMethod"
              placeholder={dynamicLabels.accessMethod?.placeholder}
            />
          </>
        )}
      </Flex>
      <Flex
        align="center"
        className="btn-group"
        gap={{ base: 16, sm: 20 }}
        justify="space-between"
        sx={sx.groupBtn}
      >
        <Button
          color="grey"
          fullWidth
          loading={loading}
          onClick={backFn}
          size="lg"
          sx={sx.backBtn}
          variant="outline"
        >
          {'戻る'}
        </Button>
        <Button fullWidth loading={loading} size="lg" type="submit">
          {'登録する'}
        </Button>
      </Flex>
      <ImageView
        onClose={mapHandlers.close}
        opened={mapOpened}
        src={imageUrl}
      />
    </Box>
  );
};

export default AddressHistoryForm;
