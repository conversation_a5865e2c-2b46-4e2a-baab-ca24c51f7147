import * as yup from 'yup';

import { BUILDING_TYPES } from './constants';

export type BuildingType = keyof typeof BUILDING_TYPES;

export type AddressHistoryFormValues = {
  prefecture: string;
  city: string;
  ward: string;
  district: string;
  address: string;
  buildingType: BuildingType | '';
  nameplate?: string;
  buildingDetails?: string;
  accessMethod?: string;
};

// Map of validation rules by building type
export const validationRules = {
  HOUSE: {
    nameplate: false, // optional
    buildingDetails: false, // optional
    accessMethod: false, // optional
  },
  APARTMENT: {
    nameplate: true, // required
    buildingDetails: true, // required
    accessMethod: false, // optional
  },
  OFFICE: {
    nameplate: true, // required
    buildingDetails: true, // required
    accessMethod: false, // optional
  },
  HOTEL: {
    nameplate: true, // required
    buildingDetails: true, // required
    accessMethod: false, // optional
  },
  OTHER: {
    nameplate: true, // required
    buildingDetails: false, // optional
    accessMethod: false, // optional
  },
};

export const schema = (
  hasWard = false,
  hasDistrict = false,
  buildingType?: BuildingType,
) =>
  yup.object().shape({
    prefecture: yup.string().required('都道府県を選択してください'),
    city: yup.string().required('市区町村1を選択してください'),
    ward: hasWard
      ? yup.string().required('市区町村2を選択してください')
      : yup.string().nullable(),
    district: hasDistrict
      ? yup.string().required('地域を選択してください')
      : yup.string().nullable(),
    address: yup.string().required('番地を入力してください'),
    buildingType: yup
      .string()
      .oneOf([...Object.keys(BUILDING_TYPES), ''])
      .required('建物の種類を選択してください'),
    nameplate:
      buildingType && validationRules[buildingType]?.nameplate
        ? yup.string().required('この項目は必須です')
        : yup.string().nullable(),
    buildingDetails:
      buildingType && validationRules[buildingType]?.buildingDetails
        ? yup.string().required('この項目は必須です')
        : yup.string().nullable(),
    accessMethod:
      buildingType && validationRules[buildingType]?.accessMethod
        ? yup.string().required('この項目は必須です')
        : yup.string().nullable(),
  });
