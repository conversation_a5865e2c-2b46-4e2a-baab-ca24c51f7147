import IconWarning from '@icons/icon-hotel-warning.svg';
import { Box, Flex, Text } from '@mantine/core';

import { sx } from './styles';

interface WarningMessageProps {
  isShow?: boolean;
  message?: string;
}

const WarningMessage: React.FC<WarningMessageProps> = ({
  isShow = false,
  message,
}) => {
  if (!isShow) return null;

  return (
    <Flex align="flex-start" gap={12} sx={sx.hotelInfoBlock}>
      <Box sx={{ flexShrink: 0 }}>
        <IconWarning size={20} />
      </Box>
      <Text size="sm" sx={sx.hotelInfoText}>
        {message || (
          <>
            施設にセラピストが入室できなかった場合でも、キャンセル料金は発生します。
            <strong>
              必ずお客様ご自身でセラピストの入室が可能かどうかを事前にお確かめください。
            </strong>
          </>
        )}
      </Text>
    </Flex>
  );
};

export default WarningMessage;
