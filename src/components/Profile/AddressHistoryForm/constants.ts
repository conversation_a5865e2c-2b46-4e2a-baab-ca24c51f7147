export const BUILDING_TYPES = {
  HOUSE: '一戸建',
  APARTMENT: 'マンション',
  OFFICE: 'オフィス',
  HOTEL: 'ホテル（ラブホテルは不可）',
  OTHER: 'その他',
};

export const BUILDING_TYPE_OPTIONS = Object.entries(BUILDING_TYPES).map(
  ([value, label]) => ({
    value,
    label,
  }),
);

export const FIELD_LABELS = {
  HOUSE: {
    nameplate: {
      label: '表札',
    },
    buildingDetails: {
      label: 'その他の詳細',
      placeholder: '建物の外観など',
    },
    accessMethod: {
      label: '訪問先までのアクセス方法',
      placeholder: '最寄駅など',
    },
  },
  APARTMENT: {
    nameplate: {
      label: '建物名',
    },
    buildingDetails: {
      label: '部屋番号',
    },
    accessMethod: {
      label: '訪問先までのアクセス方法',
      placeholder: '最寄駅など',
    },
  },
  OFFICE: {
    nameplate: {
      label: '訪問先(会社名)',
    },
    buildingDetails: {
      label: '階数',
    },
    accessMethod: {
      label: '訪問先までのアクセス方法',
      placeholder: '最寄駅など',
    },
  },
  HOTEL: {
    nameplate: {
      label: 'ホテル名',
    },
    buildingDetails: {
      label: '部屋番号',
    },
    accessMethod: {
      label: 'お部屋までのアクセス方法',
      placeholder: 'セキュリティの有無など',
    },
  },
  OTHER: {
    nameplate: {
      label: '訪問先',
    },
    buildingDetails: {
      label: '部屋番号/階数',
    },
    accessMethod: {
      label: '訪問先までのアクセス方法',
      placeholder: '最寄駅など',
    },
  },
};

// Default labels when no building type is selected
export const DEFAULT_LABELS = {
  nameplate: {
    label: '表札',
  },
  buildingDetails: {
    label: '建物詳細',
  },
  accessMethod: {
    label: 'アクセス方法',
  },
};
