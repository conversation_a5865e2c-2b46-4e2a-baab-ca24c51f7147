import IconMap from '@icons/icon-map.svg';
import { Anchor, Flex } from '@mantine/core';

const LabelWithMap = ({
  label,
  onClick,
}: {
  label: string;
  onClick: () => void;
}) => {
  return (
    <Flex align="center" gap={8} justify="space-between" w="100%" wrap="wrap">
      {label}
      <Anchor
        color="queenBlue"
        display="flex"
        onClick={onClick}
        sx={{
          alignItems: 'center',
          gap: 8,
          wordBreak: 'break-all',
        }}
      >
        <IconMap />
        <span>地図で地域ごとの詳細エリアを確認</span>
      </Anchor>
    </Flex>
  );
};

export default LabelWithMap;
