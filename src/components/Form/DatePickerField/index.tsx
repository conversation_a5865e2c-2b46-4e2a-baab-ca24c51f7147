import type { DatePickerInputProps } from '@mantine/dates';
import { DatePickerInput } from '@mantine/dates';
import { get } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles } from './styles';

interface DatePickerFieldProps<TFormValues extends FieldValues>
  extends DatePickerInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
}

const DatePickerField = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  ...props
}: DatePickerFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <DatePickerInput
      error={get(error, 'message')}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      onChange={(value) => {
        field.onChange(value);
      }}
      styles={styles.inputStyles}
      value={field.value || null}
      valueFormat="YYYY/MM/DD"
      withAsterisk={required}
      {...props}
    />
  );
};

export default DatePickerField;
