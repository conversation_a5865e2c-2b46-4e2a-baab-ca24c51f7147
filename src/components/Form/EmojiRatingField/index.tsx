import { Box, Flex, Text } from '@mantine/core';
import Image from 'next/image';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { getOptionButtonSx, sx } from './styles';

interface EmojiRatingFieldProps<TFormValues extends FieldValues> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label: string;
}

const EmojiRatingField = <TFormValues extends FieldValues>({
  name,
  control,
  label,
}: EmojiRatingFieldProps<TFormValues>) => {
  const {
    field: { value, onChange },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
  };

  return (
    <Flex align="center" gap={56} sx={sx.categoryWrapper}>
      <Text sx={sx.categoryLabel}>{label}</Text>
      <Flex gap={32} sx={sx.optionsWrapper}>
        {/* Good Option */}
        <Flex
          align="center"
          direction="column"
          gap={8}
          onClick={() => handleOptionSelect('GOOD')}
          sx={getOptionButtonSx()}
        >
          <Box sx={sx.emojiContainer}>
            <Image
              alt="良かった"
              height={32}
              src={
                value === 'GOOD'
                  ? '/icons/rating-happy-active.svg'
                  : '/icons/rating-happy-inactive.svg'
              }
              width={32}
            />
          </Box>
          <Text sx={sx.optionText}>良かった</Text>
        </Flex>

        {/* Bad Option */}
        <Flex
          align="center"
          direction="column"
          gap={8}
          onClick={() => handleOptionSelect('BAD')}
          sx={getOptionButtonSx()}
        >
          <Box sx={sx.emojiContainer}>
            <Image
              alt="残念だった"
              height={32}
              src={
                value === 'BAD'
                  ? '/icons/rating-sad-active.svg'
                  : '/icons/rating-sad-inactive.svg'
              }
              width={32}
            />
          </Box>
          <Text sx={sx.optionText}>残念だった</Text>
        </Flex>
      </Flex>
      {error && (
        <Text color="red" size="sm" sx={sx.errorText}>
          {error.message}
        </Text>
      )}
    </Flex>
  );
};

export default EmojiRatingField;
