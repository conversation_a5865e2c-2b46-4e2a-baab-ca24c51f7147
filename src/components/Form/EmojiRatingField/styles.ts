import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  categoryWrapper: {
    padding: '0 8px',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    '@media (max-width: 768px)': {
      gap: 30,
    },
  },
  categoryLabel: {
    fontSize: 14,
    color: '#3C3C3C',
    textAlign: 'center',
    width: 56,
    flexShrink: 0,
  },
  optionsWrapper: {
    alignItems: 'center',
  },
  emojiContainer: {
    width: 32,
    height: 32,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionText: {
    fontSize: 10,
    lineHeight: 1.2,
    textAlign: 'center',
    color: '#767676',
    width: 60,
  },
  errorText: {
    marginTop: 8,
    fontSize: 14,
  },
};

export const getOptionButtonSx = (): Sx => ({
  cursor: 'pointer',
  alignItems: 'center',
  padding: '16px 8px 0',
  width: 48,
  backgroundColor: 'transparent',
  '&:hover': {
    backgroundColor: 'transparent',
  },
});
