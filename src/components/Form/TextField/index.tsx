import type { TextInputProps } from '@mantine/core';
import { TextInput } from '@mantine/core';
import { merge } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { PatternFormat } from 'react-number-format';

import { styles } from './styles';

interface TextFieldProps<TFormValues extends FieldValues>
  extends TextInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
  format?: string;
  transformValue?: (value: string) => string;
}

const TextField = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  styles: customStyles,
  format,
  type,
  defaultValue,
  transformValue,
  ...props
}: TextFieldProps<TFormValues>) => {
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  if (format) {
    return (
      <PatternFormat
        {...field}
        customInput={TextInput}
        error={error?.message}
        format={format}
        getInputRef={ref}
        inputWrapperOrder={['label', 'input', 'error', 'description']}
        styles={(theme: any, params: any, context: any) =>
          merge(
            {},
            styles.inputStyles,
            typeof customStyles === 'function'
              ? customStyles(theme, params, context)
              : customStyles,
          )
        }
        value={field.value as any}
        withAsterisk={required}
        {...props}
      />
    );
  }

  return (
    <TextInput
      {...field}
      defaultValue={defaultValue}
      error={error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      onChange={(event) => {
        const { value } = event.target;
        field.onChange(transformValue ? transformValue(value) : value);
      }}
      ref={ref}
      styles={(theme, params, context) =>
        merge(
          {},
          styles.inputStyles,
          typeof customStyles === 'function'
            ? customStyles(theme, params, context)
            : customStyles,
        )
      }
      type={type}
      value={field.value || ''}
      withAsterisk={required}
      {...props}
    />
  );
};

export default TextField;
