import type { CSSObject } from '@emotion/react';
import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  requiredMark: {
    color: '#e8a62d',
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  inputWrapperStyles: {
    label: {
      padding: '0 15px 5px',
      fontSize: 16,
      display: 'flex',
      alignItems: 'center',
      gap: 10,
      fontWeight: 'bold',
      color: '#727272',
      svg: {
        color: '#000000',
      },
    },
  },
  inputStyles: {
    input: {
      cursor: 'pointer',
      padding: '0 15px',
      height: 60,
      fontSize: 16,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      span: {
        svg: {
          marginLeft: 20,
        },
        '&:first-of-type': {
          flexGrow: 1,
        },
        '&:not(:first-of-type)': {
          paddingLeft: 16,
          borderLeft: '2px solid #DDDDDD',
          marginLeft: 16,
        },
      },
    },
  },
};
