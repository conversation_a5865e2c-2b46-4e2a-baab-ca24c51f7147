import IconDropdown from '@icons/icon-dropdown.svg';
import type { InputWrapperProps } from '@mantine/core';
import { Box, Input, Text } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { get } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import dayjs from 'utils/dayjs';

import { styles, sx } from './styles';

interface DateTimePickerProps<TFormValues extends FieldValues>
  extends Partial<InputWrapperProps> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  placeholder?: string;
  required?: boolean;
}

const DateTimePicker = <TFormValues extends FieldValues>({
  name,
  control,
  label,
  placeholder = '',
  required = false,
}: DateTimePickerProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOnClickInput = () => {
    openContextModal({
      modal: 'DateTimePickerModal',
      size: 830,
      innerProps: {
        initialData: field.value,
        onConfirm: (date: string) => field.onChange(date),
      },
    });
  };

  const renderInputContent = (): React.ReactNode => {
    if (field.value) {
      return (
        <>
          <Box component="span">
            {`${dayjs(field.value).format('LL (ddd)')}`} <IconDropdown />
          </Box>
          <Box component="span">
            {`${dayjs(field.value).format('HH:mm')}`} <IconDropdown />
          </Box>
        </>
      );
    }
    return (
      <Text color="gray" component="span">
        {placeholder}
      </Text>
    );
  };

  return (
    <Input.Wrapper
      error={get(error, 'message')}
      label={
        <>
          {label}
          {required && (
            <Box component="span" sx={sx.requiredMark}>
              ※必須
            </Box>
          )}
        </>
      }
      styles={styles.inputWrapperStyles}
      withAsterisk={false}
    >
      <Input
        component="button"
        onClick={handleOnClickInput}
        ref={field.ref}
        styles={styles.inputStyles}
        type="button"
      >
        {renderInputContent()}
      </Input>
    </Input.Wrapper>
  );
};

export default DateTimePicker;
