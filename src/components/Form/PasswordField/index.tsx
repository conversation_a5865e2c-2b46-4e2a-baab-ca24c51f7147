import type { PasswordInputProps } from '@mantine/core';
import { PasswordInput } from '@mantine/core';
import { get } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

interface PasswordFieldProps<TFormValues extends FieldValues>
  extends PasswordInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
}

const PasswordField = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  ...props
}: PasswordFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <PasswordInput
      {...props}
      {...field}
      error={get(error, 'message')}
      withAsterisk={required}
    />
  );
};

export default PasswordField;
