import type { SelectProps } from '@mantine/core';
import { Select } from '@mantine/core';
import { merge } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles } from './styles';

interface SelectFieldProps<TFormValues extends FieldValues>
  extends SelectProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
}

const SelectField = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  data = [],
  styles: customStyles,
  ...props
}: SelectFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Select
      {...field}
      data={data}
      error={error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      onChange={(value: string) => {
        field.onChange(value);
      }}
      styles={(theme, params, context) =>
        merge(
          {},
          styles.inputStyles,
          typeof customStyles === 'function'
            ? customStyles(theme, params, context)
            : customStyles,
        )
      }
      value={field.value || ''}
      withAsterisk={required}
      {...props}
    />
  );
};

export default SelectField;
