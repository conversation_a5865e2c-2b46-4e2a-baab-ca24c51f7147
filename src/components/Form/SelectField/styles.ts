import type { CSSObject } from '@mantine/core';

export const styles: Record<string, Record<string, CSSObject>> = {
  inputStyles: {
    label: {
      fontSize: '20px',
      fontWeight: 'bold',
      marginBottom: '6px',
      '@media (max-width: 768px)': {
        fontSize: '16px',
        marginBottom: '8px',
      },
    },
    wrapper: {
      margin: 0,
    },
    icon: {
      width: 50,
    },
    input: {
      height: 50,
      padding: '0 20px',
      fontSize: 18,
      border: '1px solid #DDDDDD',
      '@media (max-width: 768px)': {
        height: 40,
        fontSize: 13,
        padding: '0 10px',
      },
      '&::placeholder': {
        color: '#767676',
      },
      '&[data-with-icon]': {
        paddingLeft: 50,
        '@media (max-width: 768px)': {
          paddingLeft: 40,
        },
      },
      '&[data-invalid]': {
        color: 'black',
        '&::placeholder': {
          color: '#767676',
        },
      },
      '&[data-disabled]': {
        borderColor: '#DDDDDD',
        color: '#767676',
        backgroundColor: '#F4F4F4',
      },
    },
    description: {
      marginTop: 8,
      color: '#3C3C3C',
      fontSize: 14,
      lineHeight: 1.5,
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
    error: {
      marginTop: 8,
      fontSize: 14,
    },

    rightSection: {
      '@media (max-width: 768px)': {
        width: 22,
      },
      svg: {
        display: 'none',
      },
      '&:after': {
        content: '""',
        display: 'block',
        position: 'absolute',
        left: 0,
        top: '50%',
        marginTop: -4,
        borderColor: '#070203 transparent transparent transparent',
        borderWidth: 8,
        borderStyle: 'solid',
        '@media (max-width: 768px)': {
          borderWidth: 5,
          marginTop: -2,
        },
      },
    },

    item: {
      fontSize: 14,
    },
  },
};

export default styles;
