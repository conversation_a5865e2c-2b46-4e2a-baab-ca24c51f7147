import type { CSSObject } from '@mantine/core';

export const styles: Record<string, Record<string, CSSObject>> = {
  inputStyles: {
    root: {
      '@media (max-width: 768px)': {
        height: 55,
      },
    },
    symbolBody: {
      cursor: 'pointer',
      marginTop: 12,
      marginBottom: 30,
      svg: {
        width: 50,
        height: 50,
      },
      '@media (max-width: 768px)': {
        svg: {
          width: 30,
          height: 30,
        },
      },
    },
  },
};

export default styles;
