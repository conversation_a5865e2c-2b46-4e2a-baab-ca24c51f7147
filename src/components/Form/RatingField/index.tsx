import type { RatingProps } from '@mantine/core';
import { Rating } from '@mantine/core';
import { merge } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles } from './styles';

interface RatingFieldProps<TFormValues extends FieldValues>
  extends RatingProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
}

const RatingField = <TFormValues extends FieldValues>({
  name,
  control,
  styles: customStyles,
  defaultValue,
  ...props
}: RatingFieldProps<TFormValues>) => {
  const {
    field: { ref, ...field },
  } = useController({
    name,
    control,
  });

  return (
    <Rating
      {...field}
      defaultValue={defaultValue}
      onChange={(value: number) => {
        field.onChange(value);
      }}
      ref={ref}
      styles={(theme, params, context) =>
        merge(
          {},
          styles.inputStyles,
          typeof customStyles === 'function'
            ? customStyles(theme, params, context)
            : customStyles,
        )
      }
      value={field.value || 0}
      {...props}
    />
  );
};

export default RatingField;
