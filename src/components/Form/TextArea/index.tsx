import type { TextareaProps } from '@mantine/core';
import { Textarea } from '@mantine/core';
import { merge } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles } from './styles';

interface TextAreaProps<TFormValues extends FieldValues> extends TextareaProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
}

const TextArea = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  styles: customStyles,
  defaultValue,
  ...props
}: TextAreaProps<TFormValues>) => {
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <Textarea
      {...field}
      defaultValue={defaultValue}
      error={error?.message}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      onChange={(event: any) => {
        field.onChange(event.target.value);
      }}
      ref={ref}
      styles={(theme, params, context) =>
        merge(
          {},
          styles.inputStyles(theme),
          typeof customStyles === 'function'
            ? customStyles(theme, params, context)
            : customStyles,
        )
      }
      value={field.value || ''}
      withAsterisk={required}
      {...props}
    />
  );
};

export default TextArea;
