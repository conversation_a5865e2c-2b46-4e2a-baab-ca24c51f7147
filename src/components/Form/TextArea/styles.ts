import type { MantineTheme } from '@mantine/core';

export const styles: Record<string, any> = {
  inputStyles: (theme: MantineTheme) => ({
    root: {
      width: '100%',
    },
    input: {
      border: 'solid 1px #ddd',
      padding: '28px 20px',
      fontSize: 18,
      marginBottom: 18,
      minHeight: 280,
      '&::placeholder': {
        color: theme.colors.sonicSilver,
      },
      '@media (max-width: 768px)': {
        fontSize: 14,
        marginBottom: 10,
        minHeight: 140,
        padding: '14px 10px',
      },
    },
    description: {
      fontSize: 16,
      textAlign: 'right',
      color: '#070203',
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
    error: {
      fontSize: 14,
      marginTop: -8,
      marginBottom: 5,
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
  }),
};

export default styles;
