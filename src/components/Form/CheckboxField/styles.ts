import type { CSSObject } from '@emotion/react';

export const styles: Record<string, Record<string, CSSObject>> = {
  checkboxGroupStyles: {
    label: {
      padding: '0 15px 5px',
      fontSize: 16,
      display: 'flex',
      alignItems: 'center',
      gap: 10,
      fontWeight: 'bold',
      color: '#727272',
      svg: {
        color: '#000000',
      },
    },
    error: {
      fontSize: 14,
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
  },
  checkboxStyles: {
    root: {
      '&[data-checked=true] > div': {
        borderColor: '#43749A',
      },
    },
    body: {
      position: 'relative',
      borderRadius: 6,
      border: '1px solid #ddd',
      alignItems: 'center',
      width: '100%',
    },
    labelWrapper: {
      color: '#727272',
      display: 'flex',
      width: '100%',
    },
    label: {
      cursor: 'pointer',
      padding: '21px 10px 21px 53px',
      flex: '1 1 auto',
      fontSize: 16,
      fontWeight: 'bold',
      color: '#727272',
    },
    input: {
      border: 'solid 1.5px #b2b2b2',
    },
    inner: {
      left: 25,
      position: 'absolute',
    },
  },
};
