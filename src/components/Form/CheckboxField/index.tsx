import type {
  CheckboxGroupProps,
  CheckboxProps,
  FlexProps,
} from '@mantine/core';
import { Checkbox, Flex } from '@mantine/core';
import { isEmpty, merge } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { get, useController } from 'react-hook-form';

import { styles } from './styles';

interface CheckboxFieldProps<TFormValues extends FieldValues>
  extends Partial<CheckboxGroupProps> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  options: CheckboxProps[];
  selectedAllValue?: string;
  layoutProps?: FlexProps;
}

const CheckboxField = <TFormValues extends FieldValues>({
  name,
  control,
  options,
  selectedAllValue,
  layoutProps,
  styles: customStyles,
  ...rest
}: CheckboxFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOnCheck = (values: string[]) => {
    if (
      typeof selectedAllValue === 'string' &&
      (isEmpty(values) ||
        (!get(field, 'value', []).includes(selectedAllValue) &&
          values.includes(selectedAllValue)))
    ) {
      field.onChange([selectedAllValue]);
    } else {
      field.onChange(values.filter((value) => value !== selectedAllValue));
    }
  };

  return (
    <Checkbox.Group
      error={get(error, 'message')}
      styles={styles.checkboxGroupStyles}
      {...field}
      onChange={handleOnCheck}
      {...rest}
    >
      <Flex direction="column" gap={5} {...layoutProps}>
        {options.map((props, index) => (
          <Checkbox
            key={index}
            styles={(theme, params, context) =>
              merge(
                {},
                styles.checkboxStyles,
                typeof customStyles === 'function'
                  ? customStyles(theme, params, context)
                  : customStyles,
              )
            }
            wrapperProps={{
              'data-checked': get(field, 'value', []).includes(props.value),
            }}
            {...props}
          />
        ))}
      </Flex>
    </Checkbox.Group>
  );
};

export default CheckboxField;
