import type { NumberInputProps } from '@mantine/core';
import { TextInput } from '@mantine/core';
import { merge } from 'lodash';
import { useCallback, useMemo, useRef } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import helpers from 'utils/helpers';

import { styles } from './styles';

interface NumberFieldProps<TFormValues extends FieldValues>
  extends Omit<NumberInputProps, 'onChange'> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
  format?: string;
  step?: number;
  transformValue?: (value: string) => string;
}

const NumberField = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  styles: customStyles,
  type,
  step = 1,
  ...props
}: NumberFieldProps<TFormValues>) => {
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const inputRef = useRef<HTMLInputElement | null>(null);
  const match = useMemo(() => step.toString().match(/0+$/) || [], [step]);
  const handleCursor = useCallback(
    (num: number): void => {
      const formatted = helpers.numberFormat(num) || '';
      setTimeout(() => {
        if (inputRef.current) {
          const position = Math.max(0, formatted.length - match[0].length);
          inputRef.current.setSelectionRange(position, position);
        }
      }, 0);
    },
    [step],
  );

  // format value to always end with "00" and ensure minimum value of $step
  const formatValue = (val: string): string => {
    const newValue = val.replace(/\D/g, ''); // remove non-numeric characters
    if (newValue === '') {
      return '';
    }
    const num = parseInt(newValue, 10);
    // if input is less than ${step}, ensure it ends with "00"
    if (num < step) {
      return `${num.toString().replace(/0*$/, '')}${match[0]}`;
    }
    // ensure multiple of ${step}
    if (num % step !== 0) {
      return (Math.round(num / step) * step).toString();
    }
    return newValue;
  };

  // when the user types, handleOnChange ensures the value is formatted and updates the field
  const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const val = formatValue(e.currentTarget.value);
    const numericValue = parseInt(val, 10);
    field.onChange(numericValue || '');
    handleCursor(numericValue);
  };

  // when the user presses backspace after a comma, handleKeyDown removes the character before the comma and updates the value accordingly
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Backspace' && inputRef.current) {
      const { selectionStart, value } = inputRef.current;
      if (
        selectionStart &&
        value[selectionStart - 1] === ',' &&
        selectionStart > 1
      ) {
        e.preventDefault(); // Prevent the default backspace behavior
        const newValue = (
          value.slice(0, selectionStart - 2) + value.slice(selectionStart)
        ).replace(/\D/g, '');
        const numericValue = parseInt(newValue, 10);
        field.onChange(numericValue);
        handleCursor(numericValue);
      }
    }
  };

  return (
    <TextInput
      {...field}
      error={error?.message}
      inputMode="numeric"
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      maxLength={10}
      onChange={handleOnChange}
      onKeyDown={handleKeyDown}
      pattern="[0-9,]*"
      ref={(node) => {
        inputRef.current = node;
        ref(node);
      }}
      styles={(theme, params, context) =>
        merge(
          {},
          styles.inputStyles,
          typeof customStyles === 'function'
            ? customStyles(theme, params, context)
            : customStyles,
        )
      }
      type={type}
      value={helpers.numberFormat(field.value)}
      withAsterisk={required}
      {...props}
    />
  );
};

export default NumberField;
