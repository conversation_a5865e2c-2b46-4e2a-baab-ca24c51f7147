import type { CSSObject } from '@emotion/react';

export const styles: Record<string, Record<string, CSSObject>> = {
  inputWrapperStyles: {
    label: {
      fontSize: '20px',
      fontWeight: 'bold',
      marginBottom: '6px',
      '@media (max-width: 768px)': {
        fontSize: '16px',
        marginBottom: '8px',
      },
    },
    error: {
      marginTop: 8,
      fontSize: 14,
    },
  },
  chipStyles: {
    root: {
      flex: '0 1 130px',
      '@media (max-width: 768px)': {
        flex: '1 1 95px',
      },
    },
    iconWrapper: {
      display: 'none',
    },
    input: {
      display: 'none',
    },
    label: {
      display: 'flex',
      width: '100%',
      height: 50,
      borderRadius: 3,
      fontSize: 18,
      fontWeight: 'bold',
      justifyContent: 'center',
      color: '#727272',
      padding: '0 16px',
      border: 'solid 1.5px #ddd',
      '@media (max-width: 768px)': {
        height: 40,
        fontSize: 13,
      },
      '&[data-checked=true]': {
        padding: '0 16px',
        color: '#43749a',
        border: 'solid 1.5px #43749a',
        '@media (max-width: 768px)': {
          borderWidth: 2,
        },
      },
    },
  },
};
