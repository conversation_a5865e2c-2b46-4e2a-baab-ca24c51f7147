import type {
  ChipGroupProps,
  ChipProps,
  FlexProps,
  InputWrapperProps,
} from '@mantine/core';
import { Chip, Flex, Input } from '@mantine/core';
import type { Key } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles } from './styles';

interface ChipFieldProps<TFormValues extends FieldValues>
  extends ChipGroupProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  options: ChipProps[];
  allowUnselect?: boolean;
  layoutProps?: FlexProps;
  inputWrapperProps?: Partial<InputWrapperProps>;
}

const ChipField = <TFormValues extends FieldValues>({
  name,
  control,
  label,
  options,
  multiple = false,
  layoutProps,
  allowUnselect = false, // For single mode
  inputWrapperProps,
  ...rest
}: ChipFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  // Chip can't be unselect on single mode (multiple can!) so there gotta be diffence onChange handle
  return (
    <Input.Wrapper
      error={error?.message}
      label={label}
      styles={styles.inputWrapperStyles}
      {...inputWrapperProps}
    >
      <Chip.Group
        multiple={multiple}
        onChange={(value) => {
          if (multiple || !allowUnselect) {
            field.onChange(value);
          }
        }}
        value={field.value}
        {...rest}
      >
        <Flex direction="row" gap={8} {...layoutProps}>
          {options.map((props) => {
            return (
              <Chip
                key={props.value as Key}
                onClick={(e) => {
                  if (!multiple && allowUnselect) {
                    const { value, checked } = e.target as HTMLInputElement;
                    if (checked) {
                      if (value === field.value) {
                        field.onChange(null);
                      } else {
                        field.onChange(value);
                      }
                    }
                  }
                }}
                styles={styles.chipStyles}
                {...props}
              />
            );
          })}
        </Flex>
      </Chip.Group>
    </Input.Wrapper>
  );
};

export default ChipField;
