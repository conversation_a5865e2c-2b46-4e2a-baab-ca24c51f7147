import type { CSSObject } from '@mantine/core';

export const styles: Record<string, Record<string, CSSObject>> = {
  inputStyles: {
    label: {
      fontSize: '20px',
      fontWeight: 'bold',
      marginBottom: '6px',
      '@media (max-width: 768px)': {
        fontSize: '16px',
        marginBottom: '8px',
      },
    },
    wrapper: {
      margin: 0,
    },
    icon: {
      width: 50,
    },
    input: {
      minHeight: 50,
      height: 50,
      padding: '0 20px',
      fontSize: 18,
      border: '1px solid #DDDDDD',
      '@media (max-width: 768px)': {
        height: 40,
        minHeight: 40,
        fontSize: 13,
        padding: '0 10px',
      },
      '&[data-with-icon]': {
        paddingLeft: 50,
      },
    },
    placeholder: {
      color: '#767676 !important',
    },
    description: {
      marginTop: 8,
      color: '#3C3C3C',
      fontSize: 14,
    },
    error: {
      marginTop: 8,
      fontSize: 14,
    },
  },
};
