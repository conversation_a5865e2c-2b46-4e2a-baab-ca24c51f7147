import type { MonthPickerInputProps } from '@mantine/dates';
import { MonthPickerInput } from '@mantine/dates';
import { get } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles } from './styles';

interface MonthPickerFieldProps<TFormValues extends FieldValues>
  extends MonthPickerInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
}

const MonthPickerField = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  ...props
}: MonthPickerFieldProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  return (
    <MonthPickerInput
      error={get(error, 'message')}
      inputWrapperOrder={['label', 'input', 'error', 'description']}
      onChange={(value) => {
        field.onChange(value);
      }}
      styles={styles.inputStyles}
      value={field.value || null}
      valueFormat="YYYY/MM"
      withAsterisk={required}
      {...props}
    />
  );
};

export default MonthPickerField;
