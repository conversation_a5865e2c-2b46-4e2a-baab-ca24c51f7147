import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  cropImageModalWrapper: {
    padding: '40px 30px 30px',
    backgroundColor: '#FFFFFF',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    position: 'relative',
    '@media(max-width: 768px)': {
      padding: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      height: '100%',
    },
  },
  title: {
    marginBottom: 50,
    '@media(max-width: 768px)': {
      marginBottom: 0,
      marginTop: 22,
      fontSize: 16,
      color: 'white',
    },
  },
  imageCrop: {
    'img[alt="crop"]': {
      pointerEvents: 'none',
    },
    '@media(max-width: 768px)': {
      flexGrow: 1,
      display: 'flex',
      alignItems: 'center',
    },
  },
  btnGroup: {
    width: '100%',
    '& > *': {
      flex: '1 1 auto',
    },
    '@media(max-width: 768px)': {
      margin: 0,
      position: 'sticky',
      bottom: 0,
      backgroundColor: 'white',
      padding: '16px 20px',
      gap: 9,
      '&[data-address-bar=true]': {
        bottom: 80,
      },
    },
  },
};

export const styles: Record<string, any> = {
  modalStyles: (theme: MantineTheme) => ({
    inner: {
      '@media(max-width: 768px)': {
        padding: '0 !important',
      },
    },
    body: {
      '@media(max-width: 768px)': {
        maxHeight: `${theme.other?.viewHeight}px !important`,
        height: theme.other?.viewHeight,
      },
    },
  }),
};
