import 'react-image-crop/dist/ReactCrop.css';

import type { ModalProps } from '@mantine/core';
import { Box, Button, Flex, Modal, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { delay } from 'lodash';
import React, { useEffect, useState } from 'react';
import type { Crop, PercentCrop } from 'react-image-crop';
import ReactCrop, { centerCrop, makeAspectCrop } from 'react-image-crop';

import { styles, sx } from './styles';

// This is to demonstate how to make and center a % aspect crop
// which is a bit trickier so we use some helper functions.
function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: 'px',
        width: 100,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

const getCropImage = (image: HTMLImageElement, percentCrop: PercentCrop) => {
  const targetX = (image.width * percentCrop.x) / 100;
  const targetY = (image.height * percentCrop.y) / 100;
  const targetWidth = (image.width * percentCrop.width) / 100;
  const targetHeight = (image.height * percentCrop.height) / 100;
  const canvas = document.createElement('canvas');
  canvas.width = 800;
  canvas.height = 800;
  const ctx = canvas.getContext('2d');
  ctx?.drawImage(
    image,
    targetX,
    targetY,
    targetWidth,
    targetHeight,
    0,
    0,
    800,
    800,
  );

  return new Promise<string>((resolve) => {
    resolve(canvas.toDataURL('image/jpeg', 0.9));
  });
};

interface CropImageModalProps extends ModalProps {
  imageSrc: string;
  onCrop: (image: string) => void;
}

const CropImageModal: React.FC<CropImageModalProps> = ({
  imageSrc,
  onCrop,
  onClose,
  opened = false,
  ...props
}) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const [src, setSrc] = useState<string>();
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PercentCrop>();

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    setCrop(centerAspectCrop(width, height, 1));
  };

  const handleOnClose = () => {
    onClose();
    delay(() => {
      setSrc(undefined);
      setCrop(undefined);
      setCompletedCrop(undefined);
    }, 1000);
  };

  const handleOnCrop = async () => {
    if (completedCrop) {
      const img = new Image();
      img.src = imageSrc;
      const croppedImageSrc = await getCropImage(img, completedCrop);

      onCrop(croppedImageSrc);
      handleOnClose();
    }
  };

  useEffect(() => {
    if (imageSrc) {
      setSrc(imageSrc);
    }
  }, [imageSrc]);

  return (
    <Modal
      fullScreen={mobileScreen}
      onClose={handleOnClose}
      opened={opened}
      size={600}
      styles={styles.modalStyles}
      withCloseButton={false}
      {...props}
    >
      <Box sx={sx.cropImageModalWrapper}>
        <Title color="black" mb={50} order={3} size={23} sx={sx.title}>
          プロフィール画像
        </Title>
        <Box sx={sx.imageCrop}>
          <ReactCrop
            aspect={1}
            circularCrop
            crop={crop}
            keepSelection
            minHeight={100}
            minWidth={100}
            onChange={(c) => setCrop(c)}
            onComplete={(_c, p) => setCompletedCrop(p)}
          >
            <img alt="crop" draggable={false} onLoad={onImageLoad} src={src} />
          </ReactCrop>
        </Box>
        <Flex
          data-address-bar={
            opened && window.outerHeight - window.innerHeight > 100
          }
          gap={20}
          mt={32}
          sx={sx.btnGroup}
        >
          <Button
            color="dark"
            disabled={!completedCrop}
            fullWidth
            onClick={handleOnClose}
            size="lg"
            variant="outline"
          >
            キャンセル
          </Button>
          <Button
            disabled={!completedCrop}
            fullWidth
            onClick={handleOnCrop}
            size="lg"
          >
            変更する
          </Button>
        </Flex>
      </Box>
    </Modal>
  );
};

export default CropImageModal;
