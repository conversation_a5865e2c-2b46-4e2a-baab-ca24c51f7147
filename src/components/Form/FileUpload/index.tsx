import type { FileInputProps } from '@mantine/core';
import { FileInput } from '@mantine/core';
import { set } from 'lodash';
import React, { useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import helpers from 'utils/helpers';

import CropImageModal from './CropImageModal';

const readFile = (file: File) =>
  new Promise((resolve) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => resolve(reader.result), false);
    reader.readAsDataURL(file);
  });

interface FileUploadProps<TFormValues extends FieldValues>
  extends FileInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: React.ReactNode;
  extra?: React.ReactNode;
  required?: boolean;
  innerRef?: React.Ref<HTMLButtonElement>;
}

const FileUpload = <TFormValues extends FieldValues>({
  name,
  control,
  required = false,
  innerRef,
  ...props
}: FileUploadProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const [file, setFile] = useState<File>();
  const [imageSrc, setImageSrc] = useState('');
  const value = field.value || null;

  const hanldeOnChange = async (payload: File | null) => {
    if (!payload) {
      return;
    }
    if (!helpers.checkValidImage(payload, { maxSize: 0, type: 'image' })) {
      return;
    }

    const imageDataUrl = await readFile(payload);
    setFile(payload);
    setImageSrc(imageDataUrl as string);
  };

  const handleCropImage = async (image: string) => {
    if (file && imageSrc) {
      const imageFetch = await fetch(image);
      const blob = await imageFetch.blob();
      const newFile = new File([blob], file.name, {
        type: file.type,
      });
      if (!helpers.checkValidImage(newFile, { maxSize: 0, type: 'image' })) {
        return;
      }
      field.onChange(image);
    }
  };

  return (
    <>
      <CropImageModal
        imageSrc={imageSrc}
        onClose={() => {
          setFile(undefined);
          setImageSrc('');
        }}
        onCrop={handleCropImage}
        opened={!!imageSrc}
      />
      <FileInput
        {...field}
        error={error?.message}
        fileInputProps={{
          onClick: (e) => {
            set(e, 'target.value', null);
          },
        }}
        onChange={hanldeOnChange}
        ref={innerRef}
        value={value}
        withAsterisk={required}
        {...props}
      />
    </>
  );
};

export default FileUpload;
