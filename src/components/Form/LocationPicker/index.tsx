import type { InputWrapperProps } from '@mantine/core';
import { Box, Input, Text } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { get, isEmpty } from 'lodash';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import { styles, sx } from './styles';

interface LocationPickerProps<TFormValues extends FieldValues>
  extends Partial<InputWrapperProps> {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  placeholder?: string;
  required?: boolean;
}

const LocationPicker = <TFormValues extends FieldValues>({
  name,
  control,
  label,
  placeholder = '',
  required = false,
}: LocationPickerProps<TFormValues>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOnClickInput = () => {
    openContextModal({
      modal: 'LocationPickerModal',
      size: 830,
      innerProps: {
        initialData: field.value,
        onConfirm: (data: any[]) => field.onChange(data),
      },
    });
  };

  const renderInputContent = (): React.ReactNode => {
    if (!isEmpty(field.value)) {
      return field.value.map((item: any) => item.name).join(' ');
    }
    return (
      <Text color="gray" component="span">
        {placeholder}
      </Text>
    );
  };

  return (
    <Input.Wrapper
      error={get(error, 'message')}
      id={name}
      label={
        <>
          {label}
          {required && (
            <Box component="span" sx={sx.requiredMark}>
              ※必須
            </Box>
          )}
        </>
      }
      styles={styles.inputWrapperStyles}
      withAsterisk={false}
    >
      <Input
        component="button"
        name={name}
        onClick={handleOnClickInput}
        ref={field.ref}
        styles={styles.inputStyles}
        type="button"
      >
        {renderInputContent()}
      </Input>
    </Input.Wrapper>
  );
};

export default LocationPicker;
