import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconLogout from '@icons/icon-logout.svg';
import { Button, Flex, Group, Text, Title } from '@mantine/core';
import { useFetchData, useLogout } from 'hooks';
import type { IConfigurationItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import Link from 'next/link';

import { styles, sx } from './styles';

const Step1: React.FC<{
  expiration: number;
  onRevokeAccount: () => void;
}> = ({ expiration, onRevokeAccount }) => {
  const { logout: logoutFn } = useLogout();
  const { data: configurations } = useFetchData<
    IConfigurationItem[],
    {
      [key: string]: IConfigurationItem;
    }
  >({
    ...resourceQuery.getConfigs,
    select: (response) => {
      const objectKeyMap: Record<string, IConfigurationItem> = {};
      response.forEach((item) => {
        objectKeyMap[item.key] = item;
      });
      return objectKeyMap;
    },
  });

  return (
    <>
      <Title order={3}>アカウント停止中</Title>
      <Title order={4}>アカウントは停止中です</Title>
      <Text>
        ・&nbsp;30日以内であれば、アカウントを復活させることができます。
        <br />
        ・&nbsp;30日が経過するとすべてのデータが完全に削除され、アカウントを復活させることはできません。
        <br />
        ・&nbsp;完全削除された後、
        {configurations?.customerCanNotRegisterDuration?.value || 90}
        日間は同じ電話番号で登録ができなくなりますので、ご注意ください。
      </Text>
      <Flex align="center" direction="column" sx={sx.expirationWrapper}>
        <Text component="b">{expiration > 0 ? expiration : 0}日</Text>
        <Text>データが完全に削除されるまでの時間</Text>
        <Button onClick={onRevokeAccount}>アカウント復活</Button>
      </Flex>
      <Title order={4}>アカウントをすぐに削除しますか？</Title>
      <Text>
        以下のリンクをクリックすると、すべてのデータが完全に削除されます。この操作は元に戻すことができません。ご注意ください。
      </Text>
      <Button
        color="queenBlue"
        component={Link}
        href="/my-page/delete-account-immediately"
        rightIcon={<IconChevronRight />}
        styles={styles.immediateBtn}
        variant="link"
      >
        アカウントをすぐに削除
      </Button>
      <Group position="center" sx={sx.btnGroup}>
        <Button
          bg="white"
          color="blackOlive"
          leftIcon={<IconLogout />}
          onClick={() => logoutFn()}
          styles={styles.logoutBtn}
          variant="outline"
        >
          ログアウト
        </Button>
      </Group>
    </>
  );
};

export default Step1;
