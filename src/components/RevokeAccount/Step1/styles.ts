import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  expirationWrapper: {
    background: 'white',
    padding: '38px 20px 42px',
    boxShadow: ' 0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    marginTop: 40,
    marginBottom: 40,
    b: {
      fontSize: 30,
      marginBottom: 16,
      '@media (max-width: 768px)': {
        fontSize: 24,
      },
    },
    button: {
      width: 300,
      height: 60,
      fontSize: 18,
      marginTop: 30,
    },
    '@media (max-width: 768px)': {
      padding: '24px 15px',
      marginTop: 20,
      marginBottom: 30,
      marginLeft: 10,
      marginRight: 10,
      button: {
        width: 180,
        height: 44,
        fontSize: 16,
        marginTop: 24,
      },
    },
  },
};

export const styles: Record<string, any> = {
  logoutBtn: {
    root: {
      maxWidth: 600,
      height: 60,
      width: '100%',
      fontSize: 18,
      marginTop: 65,
      '@media (max-width: 768px)': {
        marginTop: 40,
        height: 40,
        fontSize: 14,
        svg: {
          width: 14,
          height: 'auto',
        },
      },
    },
    rightIcon: {
      marginLeft: 8,
    },
  },

  immediateBtn: {
    root: {
      padding: 0,
      fontSize: 16,
      borderRadius: 0,
      marginTop: 16,
      height: 'auto',
      color: '#327eb9',
      boxShadow: 'none !important',
      '&:hover': {
        backgroundImage: 'none',
      },
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
    },
    label: {
      padding: '2px 0',
      borderBottom: '1px solid #327eb9',
    },
    rightIcon: {
      marginLeft: 6,
      '@media (max-width: 768px)': {
        marginLeft: 2,
      },
    },
  },
};
