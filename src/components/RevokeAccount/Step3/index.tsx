import IconSuccess from '@icons/icon-success.svg';
import { Button, Flex, Group, Text, Title } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { useUser } from 'hooks';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

import { styles, sx } from './styles';

const Step3: React.FC = () => {
  const { refetch } = useUser();
  const router = useRouter();

  useEffect(() => {
    const handleRouteChange = async () => {
      await refetch();
    };
    router.events.on('routeChangeStart', handleRouteChange);
    router.beforePopState(() => {
      refetch();
      return true;
    });
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [refetch, router]);
  useWindowEvent('beforeunload', async () => {
    refetch();
  });

  return (
    <>
      <Flex align="center" direction="column" sx={sx.textWrapper}>
        <IconSuccess sx={sx.icon} />
        <Title order={4} sx={sx.title}>
          アカウント復元しました！
        </Title>
        <Text>
          24時間いつでもプロのリラクゼーションサービスがご利用いただけます。
        </Text>
      </Flex>
      <Group position="center" sx={sx.groupBtn}>
        <Button
          component={Link}
          href={process.env.NEXT_PUBLIC_LP_DOMAIN || '/'}
          size="lg"
          styles={styles.nextBtn}
        >
          ホームへ戻る
        </Button>
      </Group>
    </>
  );
};

export default Step3;
