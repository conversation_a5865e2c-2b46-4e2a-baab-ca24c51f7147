import { Title } from '@mantine/core';
import { OtpVerifyForm } from 'components/Auth';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';

import { sx } from './styles';

const Step2: React.FC<{
  isLoading: boolean;
  onSubmit: (values: OtpVerifyFormValues) => void;
  sendOTP: () => Promise<void>;
  backFn: () => void;
}> = ({ isLoading, onSubmit, backFn, sendOTP }) => (
  <>
    <Title color="blackOlive" order={3} sx={sx.title}>
      アカウント停止中
    </Title>
    <OtpVerifyForm
      description="アカウントを復元させるため、SMSで受信した6桁の認証コードを入力してください"
      initialValues={{ code: '' }}
      isLoading={isLoading}
      label="アカウントは停止中です"
      onCancel={backFn}
      onResendOtp={sendOTP}
      onSubmit={onSubmit}
      type="revokeAccount"
    />
  </>
);

export default Step2;
