import { Flex, Skeleton } from '@mantine/core';
import React from 'react';

import { sx } from './styles';

const ReviewSkeleton = () => {
  return (
    <Flex direction="column" gap={20} sx={sx.reviewCellWrapper}>
      <Flex align="center" gap={25}>
        <Skeleton circle height={50} w={50} />
        <Flex direction="column" gap={12} sx={{ flex: 1 }}>
          <Skeleton circle height={14} w="50%" />
          <Skeleton circle height={14} w="30%" />
        </Flex>
      </Flex>
      <Flex direction="column" gap={12}>
        <Skeleton circle height={14} w="30%" />
        <Skeleton circle height={14} w="100%" />
      </Flex>
      <Flex direction="column" gap={12}>
        <Skeleton circle height={14} w="30%" />
        <Skeleton circle height={14} w="100%" />
      </Flex>
    </Flex>
  );
};

export default ReviewSkeleton;
