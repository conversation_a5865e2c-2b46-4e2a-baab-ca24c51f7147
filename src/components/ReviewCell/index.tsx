import IconComment from '@icons/icon-comment.svg';
import IconDocument from '@icons/icon-document.svg';
import { Box, Group, Rating, Text } from '@mantine/core';
import _isEmpty from 'lodash/isEmpty';
import type { IReviewItem } from 'models/therapist';
import Image from 'next/image';
import { Fragment, useState } from 'react';
import TruncateMarkup from 'react-truncate-markup';
import { GENDER } from 'utils/constants';
import helpers from 'utils/helpers';

import { sx } from './styles';

interface ReviewCellProps {
  info?: IReviewItem;
}

const GENDER_AVATAR = [
  '/icons/icon-avatar-other.svg',
  '/icons/icon-avatar-male.svg',
  '/icons/icon-avatar-female.svg',
];

const ReviewCell: React.FC<ReviewCellProps> = ({ info }) => {
  const [seeMore, setSeeMore] = useState(false);
  return (
    <Box sx={sx.reviewCellWrapper}>
      <Group align="start" position="apart" spacing={0}>
        <Box sx={sx.reviewerInfoWrapper}>
          <Image
            alt="avatar"
            height={50}
            src={
              GENDER_AVATAR[info?.customerGender || 0] ||
              '/icons/icon-avatar-female.svg'
            }
            width={50}
          />
          <Box sx={sx.reviewerInfo}>
            <span>{`${info?.bookingAddress}の${
              GENDER[info?.customerGender || 0]
            }`}</span>
            <br />
            <span>{helpers.timestampFormat(info?.createdAt)}</span>
          </Box>
        </Box>
        <Rating
          fractions={5}
          readOnly
          size="md"
          sx={sx.reviewerRating}
          value={info?.rating || 0}
        />
      </Group>
      {!_isEmpty(info?.menus) && (
        <Text sx={sx.reviewMenus}>
          <span>
            <IconDocument />
            メニュー：
          </span>
          <span>
            {info?.menus.map((menu) => {
              return (
                <Fragment key={menu.id}>
                  {menu.title} {menu.duration}分
                  <br />
                </Fragment>
              );
            })}
          </span>
        </Text>
      )}
      {info?.comment?.therapist?.trim() && (
        <Text sx={sx.reviewerComment}>
          <span className="title">
            <IconComment />
            コメント：
          </span>
          {!seeMore ? (
            <TruncateMarkup
              ellipsis={
                <span>
                  …{' '}
                  <span
                    className="see-more-text"
                    onClick={() => setSeeMore(true)}
                  >
                    もっと見る
                  </span>
                </span>
              }
              lines={3}
            >
              <span>{info.comment.therapist}</span>
            </TruncateMarkup>
          ) : (
            <span>{info.comment.therapist}</span>
          )}
        </Text>
      )}
    </Box>
  );
};

export default ReviewCell;
