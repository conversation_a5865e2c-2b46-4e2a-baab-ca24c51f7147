import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  reviewCellWrapper: {
    backgroundColor: '#ffffff',
    padding: 20,
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    '@media (max-width: 768px)': {
      padding: '14px 20px 20px',
    },
  },
  reviewerInfoWrapper: {
    flexShrink: 0,
    display: 'flex',
    alignItems: 'center',
    img: {
      borderRadius: '100%',
      objectFit: 'cover',
      marginRight: 10,
      '@media (max-width: 768px)': {
        width: 46,
        height: 46,
      },
    },
  },
  reviewerInfo: {
    fontSize: 16,
    fontWeight: 'normal',
    color: '#4d4d4d',
    lineHeight: 1.4,
    '@media (max-width: 768px)': {
      fontSize: 12,
    },
    'span:first-of-type': {
      color: '#000000',
      fontSize: 18,
      fontWeight: 'bold',
      '@media (max-width: 768px)': {
        fontSize: 15,
      },
    },
  },
  reviewerRating: {
    marginTop: 5,
    alignSelf: 'flex-start',
  },
  reviewMenus: {
    marginTop: 20,
    fontSize: 16,
    lineHeight: 1.4,
    color: '#3c3c3c',
    '@media (max-width: 768px)': {
      fontSize: 14,
    },
    'span:first-of-type': {
      color: '#43749a',
      fontSize: 20,
      fontWeight: 'bold',
      display: 'flex',
      alignItems: 'center',
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
      svg: {
        width: 16,
        height: 16,
        marginRight: 6,
      },
    },
  },
  reviewerComment: {
    marginTop: 20,
    fontSize: 16,
    lineHeight: 1.4,
    color: '#7c7c7c',
    '@media (max-width: 768px)': {
      fontSize: 14,
      marginTop: 16,
    },
    '.title': {
      color: '#43749a',
      fontSize: 20,
      fontWeight: 'bold',
      display: 'flex',
      alignItems: 'center',
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
      svg: {
        width: 16,
        height: 16,
        marginTop: 4,
        marginRight: 6,
      },
    },
    '.see-more-text': {
      color: '#43749a',
      fontWeight: 'bold',
      cursor: 'pointer',
    },
  },
};
