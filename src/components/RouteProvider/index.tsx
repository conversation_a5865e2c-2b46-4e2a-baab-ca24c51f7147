import { openContextModal } from '@mantine/modals';
import { doc, runTransaction } from 'firebase/firestore';
import { useFetchData, useFirebaseUser, useUser } from 'hooks';
import useTwilioDevice from 'hooks/useTwilioDevice';
import { get } from 'lodash';
import type { IOnGoingBooking } from 'models/booking';
import { bookingQuery } from 'models/booking';
import { useRouter } from 'next/router';
import { db } from 'utils/firebase';
import { setUserId } from 'utils/helpers';
import notification from 'utils/notification';

// Apply any hooks that need to run on global
const RouteProvider = () => {
  const { data: currentUserData } = useUser({
    onSuccess: async (response) => {
      setUserId(response._id);
      try {
        if (!response.isCompletedProfile) return;
        const customerDoc = doc(db, 'users', response._id);
        await runTransaction(db, async (transaction) => {
          const userDataSnap = await transaction.get(customerDoc);
          // Update user data on firebase
          if (!userDataSnap.exists()) {
            transaction.set(customerDoc, {
              userId: response._id,
              displayName: response.name,
              userType: 'customer',
            });
          }
        });
      } catch (error) {
        notification.show({
          type: 'error',
          message: get(error, 'message', ''),
        });
      }
    },
  });
  const { pathname } = useRouter();

  // Init twilio device
  useTwilioDevice();

  useFirebaseUser({ userId: currentUserData?._id });

  useFetchData<IOnGoingBooking>({
    ...bookingQuery.getOnGoingBooking,
    onSuccess: (response) => {
      if (
        response.bookingNotTreatment !== null &&
        !response.bookingNotTreatment?.isViewed &&
        ![
          '/booking/[id]',
          '/my-page/revoke-account',
          '/my-page/delete-account-immediately',
        ].includes(pathname)
      ) {
        openContextModal({
          modal: 'UntreatedBookingModal',
          size: 630,
          innerProps: {
            bookingDetail: response.bookingNotTreatment,
          },
        });
      }
    },
    enabled: !!currentUserData && !currentUserData?.requestingDeleteAccount,
  });

  return null;
};

export default RouteProvider;
