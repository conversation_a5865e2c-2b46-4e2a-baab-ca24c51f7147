import { Group, SimpleGrid } from '@mantine/core';
import Pagination from 'components/Pagination';
import { times } from 'lodash';
import type { IBookingItem } from 'models/booking';

import BookingItem from '../BookingItem';
import BookingSkeleton from '../BookingItem/BookingSkeleton';
import { sx } from './styles';

const BookingHistory: React.FC<{
  bookings: IBookingItem[];
  page: number;
  lastPage: number;
  loading?: boolean;
}> = ({ bookings, page, lastPage, loading }) => {
  if (loading) {
    return (
      <Group sx={sx.bookingHistoryGroup}>
        <SimpleGrid
          breakpoints={[
            { minWidth: 769, cols: 2, spacing: 20 },
            { maxWidth: 768, cols: 1, spacing: 15 },
          ]}
          sx={sx.bookingHistoryGrid}
        >
          {times(4).map((index) => (
            <BookingSkeleton key={index} />
          ))}
        </SimpleGrid>
      </Group>
    );
  }

  return (
    <>
      <Group sx={sx.bookingHistoryGroup}>
        <SimpleGrid
          breakpoints={[
            { minWidth: 769, cols: 2, spacing: 20 },
            { maxWidth: 768, cols: 1, spacing: 15 },
          ]}
          sx={sx.bookingHistoryGrid}
        >
          {bookings.map((booking) => (
            <BookingItem booking={booking} key={booking._id} />
          ))}
        </SimpleGrid>
      </Group>
      <Pagination page={Number(page)} perPage={20} total={lastPage} />
    </>
  );
};

export default BookingHistory;
