import IconComment from '@icons/icon-comment.svg';
import IconParking from '@icons/icon-parking.svg';
import { Box, Textarea } from '@mantine/core';
import type { ICreateBooking } from 'hooks/types';
import React from 'react';

import { sx } from './styles';

interface MemoNoteProps {
  booking?: ICreateBooking;
  onChange: (value: Partial<ICreateBooking>) => void;
}

const MemoNote: React.FC<MemoNoteProps> = ({ booking, onChange }) => {
  return (
    <Box sx={sx.memoNoteWrapper}>
      <Textarea
        autosize
        label={
          <>
            <IconComment />
            施術内容について
          </>
        }
        maxLength={255}
        maxRows={4}
        minRows={2}
        name="customerNote"
        onChange={(e) => {
          onChange({
            customerNote: e.target.value,
          });
        }}
        placeholder="「肩が凝っている」など、セラピストへ事前に伝えたいことがありましたらご記入ください"
        sx={sx.textField}
        value={booking?.customerNote}
      />
      <Textarea
        autosize
        label={
          <>
            <IconParking />
            訪問先について
          </>
        }
        maxLength={255}
        maxRows={4}
        minRows={2}
        name="parkingNote"
        onChange={(e) => {
          onChange({
            parkingNote: e.target.value,
          });
        }}
        placeholder="建物名やホテル名のご記入がない場合、リクエスト承認されない可能性があります。"
        sx={sx.textField}
        value={booking?.parkingNote}
      />
    </Box>
  );
};

export default MemoNote;
