import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  memoNoteWrapper: (_theme: MantineTheme) => ({
    backgroundColor: '#ffffff',
    padding: '40px 30px',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    gap: 40,
    '@media (max-width: 768px)': {
      padding: '15px 10px',
      gap: 15,
    },
    '&.memo-note-detail': {
      padding: '38px 20px',
      gap: 36,
      '@media (max-width: 768px)': {
        padding: '15px 10px',
        gap: 10,
        '.row-title': {
          fontSize: 14,
        },
        '.row-content': {
          fontSize: 12,
        },
      },
      svg: {
        marginTop: 4,
        width: 20,
        height: 20,
        flexShrink: 0,
        '@media (max-width: 768px)': {
          width: 14,
          height: 14,
        },
      },
      '.item-row': {
        fontSize: 24,
        padding: '0 20px',
      },
    },
  }),
  textField: {
    '.mantine-Textarea-label': {
      fontSize: 18,
      display: 'flex',
      alignItems: 'center',
      gap: 20,
      fontWeight: 'bold',
      paddingLeft: 12,
      marginBottom: 12,
      '@media (max-width: 768px)': {
        fontSize: 14,
        gap: 9,
        marginBottom: 8,
      },
      svg: {
        width: 20,
        height: 20,
        '@media (max-width: 768px)': {
          width: 14,
          height: 14,
        },
      },
    },
    '.mantine-Textarea-input': {
      fontSize: 14,
      border: '1px solid #ddd',
      borderRadius: 3,
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
  },
};
