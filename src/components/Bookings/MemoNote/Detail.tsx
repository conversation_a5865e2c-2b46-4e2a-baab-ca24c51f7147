import IconComment from '@icons/icon-comment.svg';
import IconParking from '@icons/icon-parking.svg';
import { Box, Divider, Flex, Skeleton, Text } from '@mantine/core';
import React from 'react';

import { sx } from './styles';

const MemoNoteDetail: React.FC<{
  customerNote?: string;
  parkingNote?: string;
  loading?: boolean;
}> = ({ customerNote, parkingNote, loading }) => {
  if (loading) {
    return (
      <Box className="memo-note-detail" sx={sx.memoNoteWrapper}>
        <Flex className="item-row" direction="column" gap={12}>
          <Skeleton h={14} w="50%" />
          <Skeleton h={14} w="100%" />
        </Flex>
        <Divider />

        <Flex className="item-row" direction="column" gap={12}>
          <Skeleton h={14} w="50%" />
          <Skeleton h={14} w="100%" />
        </Flex>
      </Box>
    );
  }
  return (
    <Box className="memo-note-detail" sx={sx.memoNoteWrapper}>
      <Flex className="item-row" gap={20}>
        <IconComment />
        <Flex direction="column" gap={8}>
          <Text className="row-title" color="black" size={18} weight="bold">
            施術内容について
          </Text>
          <Text className="row-content" color="blackOlive" size={16}>
            {customerNote || 'データなし'}
          </Text>
        </Flex>
      </Flex>
      <Divider />
      <Flex className="item-row" gap={20}>
        <IconParking />
        <Flex direction="column" gap={8}>
          <Text className="row-title" color="black" size={18} weight="bold">
            訪問先について
          </Text>
          <Text className="row-content" color="blackOlive" size={16}>
            {parkingNote || 'データなし'}
          </Text>
        </Flex>
      </Flex>
    </Box>
  );
};

export default MemoNoteDetail;
