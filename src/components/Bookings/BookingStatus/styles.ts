import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  bookingStatusWrapper: {
    backgroundColor: '#fffaef',
    padding: '24px 20px',
    '@media (max-width: 768px)': {
      padding: '16px 20px',
    },
    '&.booking-status-done': {
      backgroundColor: '#ffffff',
      '@media (max-width: 768px)': {
        '.booking-status-text': {
          marginBottom: 20,
        },
      },
    },
    '&.booking-status-confirmed': {
      backgroundColor: '#fafff5',
    },
    '&.booking-status-arrived': {
      backgroundColor: '#f5f2ff',
    },
    '&.booking-status-canceled': {
      backgroundColor: '#fff7f7',
      '.booking-status-title': {
        margin: 0,
      },
    },
    '&.booking-status-payment-error': {
      backgroundColor: '#fff7f7',
    },
    '&.booking-status-therapist-canceled': {
      backgroundColor: '#fff7f7',
      '@media (max-width: 768px)': {
        '.booking-status-sub-title': {
          marginBottom: 22,
        },
        '.rebooking-btn': {
          maxWidth: '100%',
        },
      },
    },
    '.booking-status-title': {
      display: 'flex',
      alignItems: 'center',
      gap: 20,
      marginBottom: 20,
      svg: {
        width: 40,
        height: 40,
        flexShrink: 0,
      },
      '@media (max-width: 768px)': {
        fontSize: 18,
        gap: 10,
        marginBottom: 8,
        svg: {
          width: 22,
          height: 22,
        },
      },
    },
    '.booking-status-sub-title': {
      '@media (max-width: 768px)': {
        fontSize: 15,
        marginBottom: 4,
      },
    },
    '.booking-status-text': {
      '@media (max-width: 768px)': {
        fontSize: 13,
      },
    },
    '.booking-status-link': {
      textDecoration: 'underline',
      display: 'flex',
      gap: 20,
      alignItems: 'center',
      cursor: 'pointer',
      textUnderlineOffset: 5,
      '@media (max-width: 768px)': {
        fontSize: 13,
      },
    },
    '.booking-therapist-cancel': {
      '@media (max-width: 768px)': {
        fontSize: 13,
        marginBottom: 4,
      },
    },
    '.booking-cancel-note': {
      padding: '26px 20px',
      backgroundColor: 'white',
      '@media (max-width: 768px)': {
        fontSize: 13,
        padding: '16px 10px',
        marginBottom: 20,
      },
    },
    '.rebooking-btn': {
      borderWidth: 2,
      display: 'block',
      fontSize: 22,
      maxWidth: 390,
      height: 60,
      width: '100%',
      margin: '0 auto',
      '@media (max-width: 768px)': {
        maxWidth: 200,
        height: 35,
        fontSize: 12,
        padding: 0,
      },
    },
  },
};
