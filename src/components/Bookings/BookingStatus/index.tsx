import IconCard from '@icons/icon-card-1.svg';
import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconError from '@icons/icon-error-1.svg';
import IconSuccess from '@icons/icon-success-1.svg';
import IconWaringTriangle from '@icons/icon-warning-1.svg';
import IconWarning from '@icons/icon-warning-circle.svg';
import { Box, Button, Container, Flex, Text, Title } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import Link from 'next/link';
import React from 'react';
import { BOOKING_REASONS } from 'utils/constants';

import { sx } from './styles';

const BookingStatus: React.FC<{
  status?: string;
  reason?: string;
  cancellingNote?: string;
  therapistName?: string;
  therapistIsBusy?: boolean;
  isPaymentError?: boolean;
  onStartBooking?: () => void;
  onOpenPayment?: () => void;
}> = ({
  status,
  reason,
  therapistName,
  therapistIsBusy,
  isPaymentError,
  cancellingNote,
  onStartBooking,
  onOpenPayment,
}) => {
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  if (!status) return null;

  if (isPaymentError) {
    return (
      <Box
        className={`booking-status-payment-error`}
        sx={sx.bookingStatusWrapper}
      >
        <Container p={0} size={1140}>
          <Title
            className="booking-status-title"
            color="#db1e0e"
            order={3}
            size={24}
          >
            <IconCard />
            決済エラー
          </Title>
          <Text
            className="booking-status-link"
            color="#225277"
            onClick={onOpenPayment}
            size={16}
            tabIndex={0}
          >
            こちらから、未決済分のお支払いを完了してください
            <IconChevronRight />
          </Text>
        </Container>
      </Box>
    );
  }

  if (status === 'CANCELED') {
    if (
      reason === BOOKING_REASONS.THERAPIST_DENY_BOOKING ||
      reason === BOOKING_REASONS.THERAPIST_CANCEL_BOOKING
    ) {
      return (
        <Box
          className={`booking-status-therapist-canceled`}
          sx={sx.bookingStatusWrapper}
        >
          <Container p={0} size={1140}>
            <Title
              className="booking-status-title"
              color="#db1e0e"
              order={3}
              size={24}
            >
              <IconError />
              セラピストキャンセル済
            </Title>
            <Text
              className="booking-status-sub-title"
              color="blackOlive"
              mb={24}
              size={18}
              weight="bold"
            >
              予約が成立しませんでした
            </Text>
            <Text
              className="booking-therapist-cancel"
              color="blackOlive"
              mb={18}
              size={16}
            >
              <b>{therapistName}</b>からメッセージが届いています
            </Text>
            {cancellingNote && (
              <Text
                className="booking-cancel-note"
                color="#525252"
                mb={36}
                size={16}
              >
                {cancellingNote}
              </Text>
            )}
            <Button
              bg="white"
              className="rebooking-btn"
              onClick={onStartBooking}
              variant="outline"
            >
              このセラピストを再度予約する
            </Button>
          </Container>
        </Box>
      );
    }
    return (
      <Box
        className={`booking-status-${status.toLowerCase()}`}
        sx={sx.bookingStatusWrapper}
      >
        <Container p={0} size={1140}>
          <Title
            className="booking-status-title"
            color="#db1e0e"
            order={3}
            size={24}
          >
            <IconError />
            キャンセル
          </Title>
        </Container>
      </Box>
    );
  }

  if (status === 'DONE') {
    return (
      <Flex direction="column" gap={mobileScreen ? 16 : 30}>
        {reason === 'finishWithoutTreatment' && (
          <Box sx={sx.bookingStatusWrapper}>
            <Container p={0} size={1140}>
              <Title
                className="booking-status-title"
                color="marigold"
                order={3}
                size={24}
              >
                <IconWaringTriangle />
                決済完了のお知らせ
              </Title>
              <Text
                className="booking-status-text"
                color="blackOlive"
                size={16}
              >
                セラピストは、施術終了時刻まで現地にて待機しておりましたが、お客様とご連絡が取れませんでした。誠に残念ながら当日キャンセルとなりますので、
                キャンセルポリシーに従い、施術料金100％発生となります旨ご了承くださいませ。
              </Text>
            </Container>
          </Box>
        )}
        <Box
          className={`booking-status-${status.toLowerCase()}`}
          sx={sx.bookingStatusWrapper}
        >
          <Container p={0} size={1140}>
            <Title
              className="booking-status-title"
              color="#43749a"
              order={3}
              size={24}
            >
              <IconCard />
              お会計完了
            </Title>
            <Text
              className="booking-status-sub-title"
              color="blackOlive"
              mb={8}
              size={20}
              weight="bold"
            >
              領収書の発行について
            </Text>
            <Text
              className="booking-status-text"
              color="blackOlive"
              mb={32}
              size={16}
            >
              {mobileScreen
                ? 'HOGUGUアプリで発行いただけます。\n発行をご希望の方は、お手数ですが、アプリより発行ください。'
                : 'HOGUGUアプリで発行いただけます。発行をご希望の方は、お手数ですが、アプリより発行ください。'}
            </Text>
            <Button
              bg="white"
              className="rebooking-btn"
              onClick={onStartBooking}
              variant="outline"
            >
              このセラピストを再度予約する
            </Button>
          </Container>
        </Box>
      </Flex>
    );
  }

  if (status === 'ARRIVED') {
    return (
      <Box
        className={`booking-status-${status.toLowerCase()}`}
        sx={sx.bookingStatusWrapper}
      >
        <Container p={0} size={1140}>
          <Title
            className="booking-status-title"
            color="#65478c"
            order={3}
            size={24}
          >
            <IconSuccess />
            施術中
          </Title>
          <Text
            className="booking-status-link"
            color="#225277"
            component={Link}
            href={`${process.env.NEXT_PUBLIC_LP_URL}/attention-for-customer.html `}
            rel="noreferrer"
            size={16}
            target="_blank"
          >
            禁止事項と疾患の対応を事前にご確認ください
            <IconChevronRight />
          </Text>
        </Container>
      </Box>
    );
  }

  if (status === 'CONFIRMED') {
    return (
      <Box
        className={`booking-status-${status.toLowerCase()}`}
        sx={sx.bookingStatusWrapper}
      >
        <Container p={0} size={1140}>
          <Title
            className="booking-status-title"
            color="#41850a"
            order={3}
            size={24}
          >
            <IconSuccess />
            確定
          </Title>
          <Text
            className="booking-status-link"
            color="#225277"
            component={Link}
            href={`${process.env.NEXT_PUBLIC_LP_URL}/attention-for-customer.html `}
            rel="noreferrer"
            size={16}
            target="_blank"
          >
            禁止事項と疾患の対応を事前にご確認ください
            <IconChevronRight />
          </Text>
        </Container>
      </Box>
    );
  }

  return (
    <Box
      className={`booking-status-${status.toLowerCase()}`}
      sx={sx.bookingStatusWrapper}
    >
      <Container p={0} size={1140}>
        <Title
          className="booking-status-title"
          color="marigold"
          order={3}
          size={24}
        >
          <IconWarning />
          リクエスト中
        </Title>
        <Text
          color="blackOlive"
          fw={400}
          fz={{ base: 12, sm: 16 }}
          lh={{ base: '16px', sm: '24px' }}
        >
          予約はまだ完了しておりません。セラピストからのリクエスト承認をお待ちください。
        </Text>
        {therapistIsBusy && (
          <Text
            color="marigold"
            fw={400}
            fz={{ base: 12, sm: 16 }}
            lh={{ base: '16px', sm: '24px' }}
            mt={4}
          >
            現在、他のお客様を施術中のため、返信に時間がかかる場合があります。
          </Text>
        )}
      </Container>
    </Box>
  );
};

export default BookingStatus;
