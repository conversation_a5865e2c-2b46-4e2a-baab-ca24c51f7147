import IconCoupon from '@icons/icon-coupon.svg';
import IconMidnight from '@icons/icon-midnight.svg';
import IconPoint from '@icons/icon-point.svg';
import GiftIcon from '@icons/icon-point-gift-2.svg';
import { Box, Flex, Skeleton, Text } from '@mantine/core';
import type { IBookingDetail } from 'models/booking';
import type { IMenuItem } from 'models/therapist';
import Image from 'next/image';
import React from 'react';
import { BOOKING_STATUSES } from 'utils/constants';
import helpers from 'utils/helpers';

import { sx } from './styles';

const MenuCouponDetail: React.FC<{
  menus?: IMenuItem[];
  extensions?: IMenuItem[];
  isPaymentError?: boolean;
  coupon?: {
    amount: number;
    code: string;
    rate?: number | null;
  };
  sumDuration?: number;
  sumPrice?: number;
  midnightFee?: number;
  loading?: boolean;
  point?: {
    used?: {
      point: number;
      discount: number;
      isUsed: boolean;
      referenceId: string;
    };
    granted?: {
      point: number;
    };
  };
  currentStatus?: IBookingDetail['currentStatus'];
}> = ({
  menus = [],
  extensions = [],
  coupon,
  sumDuration,
  sumPrice,
  loading = false,
  midnightFee,
  point,
  currentStatus,
  isPaymentError,
}) => {
  if (loading) {
    return (
      <Box className="menu-coupon-detail" sx={sx.menuCouponWrapper}>
        <Flex
          align="center"
          className="menu-item"
          gap={30}
          sx={{ ...sx.menuItem, gap: '30px !important' }}
        >
          <Skeleton h={86} sx={{ flexShrink: 0 }} w={120} />
          <Flex direction="column" gap={12} w="100%">
            <Skeleton h={14} w="80%" />
            <Skeleton h={14} w="50%" />
          </Flex>
        </Flex>
        <Flex
          align="center"
          className="menu-item"
          gap={30}
          sx={{ ...sx.menuItem, borderWidth: 0, gap: '30px !important' }}
        >
          <Skeleton h={86} sx={{ flexShrink: 0 }} w={120} />
          <Flex direction="column" gap={12} w="100%">
            <Skeleton h={14} w="80%" />
            <Skeleton h={14} w="50%" />
          </Flex>
        </Flex>
      </Box>
    );
  }
  return (
    <Box className="menu-coupon-detail" sx={sx.menuCouponWrapper}>
      {menus.map((menu) => {
        return (
          <Flex
            align="center"
            className="menu-item"
            key={menu._id}
            sx={sx.menuItem}
          >
            <Image
              alt="menu-image"
              className="menu-image"
              height={86}
              src={menu.images?.large?.url || '/images/menu-default.webp'}
              width={120}
            />
            <Text
              className="menu-name"
              color="blackOlive"
              lineClamp={2}
              size={18}
            >
              {menu.title}
            </Text>
            <Text className="menu-price" color="blackOlive" size={16}>
              {menu.selectedOption?.duration}分
              <br />
              {helpers.numberFormat(menu.selectedOption?.price)}円
            </Text>
          </Flex>
        );
      })}
      {extensions.map((menu) => {
        return (
          <Flex
            align="center"
            className="menu-item"
            key={menu._id}
            sx={sx.menuItem}
          >
            <Image
              alt="menu-image"
              className="menu-image"
              height={86}
              src={menu.images?.large?.url || '/images/menu-default.webp'}
              width={120}
            />
            <Text
              className="menu-name"
              color="blackOlive"
              lineClamp={2}
              size={18}
            >
              {menu.title}
            </Text>
            <span className="extension">延長</span>
            <Text className="menu-price" color="blackOlive" size={16}>
              {menu.selectedExtension?.duration}分
              <br />
              {helpers.numberFormat(menu.selectedExtension?.price)}円
            </Text>
          </Flex>
        );
      })}
      {!!midnightFee && (
        <Flex
          align="center"
          className="coupon-item midnight"
          gap={20}
          sx={sx.menuItem}
        >
          <IconMidnight />
          <Text className="menu-name" color="blackOlive" size={18}>
            深夜料金
          </Text>
          <Text className="menu-price" color="blackOlive" size={16}>
            {helpers.numberFormat(midnightFee)}円
          </Text>
        </Flex>
      )}
      {!!point?.used?.point && (
        <Flex
          align="center"
          className="coupon-item midnight"
          gap={16}
          sx={sx.menuItem}
        >
          <IconPoint />
          <Text className="menu-name" color="blackOlive" size={18}>
            ポイント利用（ {helpers.numberFormat(point.used.point)} ）
          </Text>
          <Text className="menu-price" color="blackOlive" size={16}>
            - {helpers.numberFormat(point.used.discount)}円
          </Text>
        </Flex>
      )}
      {coupon?.amount && coupon?.code && (
        <Flex align="start" className="coupon-item" gap={16} sx={sx.menuItem}>
          <Box>
            <IconCoupon />
          </Box>
          <Box w="100%">
            <Flex gap={20}>
              <Text className="menu-name" size={18} weight="bold">
                クーポン値引 ({coupon.code})
              </Text>
              <Text className="menu-price" color="blackOlive" size={16}>
                - {helpers.numberFormat(coupon.amount)}円
              </Text>
            </Flex>
            {coupon.rate && (
              <Text
                color="sonicSilver"
                fw={500}
                fz={{ base: 12, sm: 14 }}
                lh={1}
                mt={{ base: 8, sm: 12 }}
              >
                ご利用料金が{coupon.rate}%OFFになります
              </Text>
            )}
          </Box>
        </Flex>
      )}
      <Flex
        className="summary-content"
        justify="space-between"
        sx={sx.summaryWrapper}
      >
        <Text color="blackOlive" size={16} sx={sx.summaryText}>
          合計時間 <span>{sumDuration || 0}分</span>
        </Text>
        <Text color="blackOlive" size={16} sx={sx.summaryText}>
          合計金額（税込） <span>{helpers.numberFormat(sumPrice || 0)}円</span>
        </Text>
      </Flex>
      {(point?.granted?.point || 0) > 0 && currentStatus && (
        <Flex bg="nyanza" justify="space-between" mt={16} p="8px 16px">
          <Flex gap={3}>
            <GiftIcon />
            <Text c="avocado">
              {[
                BOOKING_STATUSES.NEW,
                BOOKING_STATUSES.PENDING,
                BOOKING_STATUSES.CONFIRMED,
                BOOKING_STATUSES.ARRIVED,
              ].includes(currentStatus?.status) ||
              (currentStatus.status === BOOKING_STATUSES.CANCELED &&
                isPaymentError)
                ? '獲得予定ポイント'
                : '獲得ポイント'}
            </Text>
          </Flex>
          <Text c="avocado" fw={700}>
            + {helpers.numberFormat(point?.granted?.point)}
          </Text>
        </Flex>
      )}
    </Box>
  );
};

export default MenuCouponDetail;
