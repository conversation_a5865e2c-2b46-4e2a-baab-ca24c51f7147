import IconAdd from '@icons/icon-add.svg';
import IconCoupon from '@icons/icon-blue-coupon.svg';
import IconChervonRight from '@icons/icon-chevron-right.svg';
import InfoIcon from '@icons/icon-info-2.svg';
import IconMidnight from '@icons/icon-midnight.svg';
import IconPoint from '@icons/icon-point.svg';
import IconRemove from '@icons/icon-remove.svg';
import {
  ActionIcon,
  Box,
  Flex,
  Stack,
  Text,
  ThemeIcon,
  UnstyledButton,
} from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { ApplyCouponModal, MenuSelectModal } from 'components/Modals';
import ApplyPointModal from 'components/Modals/ApplyPointModal';
import { useMutate, usePrevious } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import { get } from 'lodash';
import differenceBy from 'lodash/differenceBy';
import type { ICheckPointAmount, ICoupon, ITotalPoints } from 'models/booking';
import { resourceQuery } from 'models/resource';
import type { IMenuItem } from 'models/therapist';
import Image from 'next/image';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { MIDNIGHT_FEE, MIDNIGHT_TIMES } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers, { eventLog, exchangePointToPrice } from 'utils/helpers';

import { sx } from './styles';

interface MenuCouponProps {
  booking?: ICreateBooking;
  onChange: (value: Partial<ICreateBooking>) => void;
  sumDuration?: number;
  sumPrice?: number;
  promotionPrice?: number;
  checkPointAmount?: ICheckPointAmount;
  totalPoints?: ITotalPoints;
  sumPriceWithPoint?: number;
}

const MenuCoupon: React.FC<MenuCouponProps> = ({
  booking,
  onChange,
  sumDuration,
  sumPrice,
  promotionPrice,
  checkPointAmount,
  totalPoints,
  sumPriceWithPoint,
}) => {
  const [opened, setOpened] = useState(false);
  const [openedCoupon, setOpenedCoupon] = useState(false);
  const { mutateAsync: checkCouponFn } = useMutate<
    {
      code: string;
      totalPrice: number;
      bookingDatetime?: string;
      servicerId?: string;
      areaCodes?: string[];
    },
    ICoupon
  >({
    ...resourceQuery.checkCoupon,
    meta: {
      noToastError: true,
    },
  });
  const previousPrice = usePrevious(sumPriceWithPoint);

  const [openedApplyPointModal, setOpenedApplyPointModal] = useState(false);

  const verifyCoupon = useCallback(async () => {
    if (!booking?.coupon?.code || !booking.dateBooking || !booking.therapistId)
      return;
    try {
      const response = await checkCouponFn({
        code: booking.coupon.code,
        bookingDatetime: booking.dateBooking,
        servicerId: booking.therapistId,
        totalPrice: sumPriceWithPoint || 0,
        areaCodes: booking?.areaCodes || [],
      });
      eventLog('revalidate_coupon', {
        coupon_code: response.code,
        therapist_id: booking.therapistId,
        error_message: 'success',
      });
      onChange({
        coupon: response,
        couponError: '',
      });
    } catch (e) {
      eventLog('revalidate_coupon', {
        coupon_code: booking.coupon.code,
        therapist_id: booking.therapistId,
        error_message: get(e, 'data.0.message') || get(e, 'error', ''),
      });
      onChange({
        couponError: get(e, 'data.0.message') || get(e, 'error', ''),
      });
    }
  }, [
    booking?.coupon?.code,
    booking?.dateBooking,
    booking?.therapistId,
    checkCouponFn,
    onChange,
    sumPriceWithPoint,
  ]);

  const handleToggleMenuSelect = () => {
    setOpened(!opened);
  };

  const handleChangeMenus = (newMenus: IMenuItem[]) => {
    onChange({
      menus: newMenus,
    });
    const addedMenus = differenceBy(newMenus, booking?.menus || [], '_id');
    addedMenus.forEach((menu) => {
      eventLog('add_to_cart', {
        menu_id: menu._id,
        duration: menu.selectedOption?.duration,
        price: menu.selectedOption?.price,
      });
    });
  };

  const handleRemoveMenu = (menuId: string) => {
    const newMenus = booking?.menus.filter((menu) => {
      if (menu._id !== menuId) {
        eventLog('remove_from_cart', {
          menu_id: menu._id,
          duration: menu.selectedOption?.duration,
          price: menu.selectedOption?.price,
        });
        return true;
      }
      return false;
    });
    onChange({
      menus: newMenus,
      point: undefined,
    });
  };

  const menuPriceList = useMemo(() => {
    const menusObj: Record<string, string> = {};
    booking?.menus.forEach((menu, index) => {
      const listCounting = Math.floor(index / 3) + 1;
      if (menusObj[`menu_price_list_${listCounting}`]) {
        menusObj[`menu_price_list_${listCounting}`] =
          `${menusObj[`menu_price_list_${listCounting}`]},` +
          `${menu._id}:${menu.selectedOption?.price}`;
      } else {
        menusObj[
          `menu_price_list_${listCounting}`
        ] = `${menu._id}:${menu.selectedOption?.price}`;
      }
    });
    return menusObj;
  }, [booking?.menus]);
  const handleOpenApplyCoupon = () => {
    eventLog('start_add_coupon', {
      therapist_id: booking?.therapistId,
      ...menuPriceList,
    });
    setOpenedCoupon(true);
  };

  const handleRemoveCoupon = () => {
    onChange({
      coupon: undefined,
      couponError: '',
    });
  };

  const handleRemovePoint = () => {
    onChange({
      point: undefined,
      // couponError: '',
    });
  };

  const isMidnight = useMemo(() => {
    return (
      booking?.dateBooking &&
      MIDNIGHT_TIMES.includes(dayjs(booking?.dateBooking).format('HH:mm'))
    );
  }, [booking?.dateBooking]);

  // Coupon check on every menu changed
  useEffect(() => {
    if (sumPriceWithPoint !== previousPrice) {
      verifyCoupon();
    }
  }, [previousPrice, sumPriceWithPoint, verifyCoupon]);

  return (
    <Box sx={sx.menuCouponWrapper}>
      {booking?.menus?.map((detail) => {
        return (
          <Flex align="center" gap={20} key={detail._id} sx={sx.menuItem}>
            <ActionIcon
              className="icon-remove"
              onClick={() => handleRemoveMenu(detail._id)}
            >
              <IconRemove />
            </ActionIcon>
            <Image
              alt="menu-image"
              className="menu-image"
              height={86}
              src={detail.images?.large?.url || '/images/menu-default.webp'}
              width={120}
            />
            <Text className="menu-name" color="#070203" lineClamp={2} size={18}>
              {detail.title}
            </Text>
            <Text className="menu-price" color="#070203" size={16}>
              {detail.selectedOption?.duration}分
              <br />
              {helpers.numberFormat(detail.selectedOption?.price || 0)}円
            </Text>
          </Flex>
        );
      })}

      <Flex align="center" gap={20} sx={sx.menuItem}>
        <ActionIcon className="icon-add" onClick={handleToggleMenuSelect}>
          <IconAdd />
        </ActionIcon>
        <Text
          className="add-menu"
          color="queenBlue.6"
          onClick={handleToggleMenuSelect}
          size={18}
          weight="bold"
        >
          メニューを追加
          <IconChervonRight />
        </Text>
      </Flex>
      {isMidnight && (
        <Flex align="flex-start" gap={20} sx={sx.menuItem}>
          <ThemeIcon
            className="icon-add"
            color="blackOlive"
            mt={{ base: -8, sm: -4 }}
            variant="transparent"
          >
            <IconMidnight />
          </ThemeIcon>
          <Box>
            <Text color="blackOlive" fw="bold" fz={{ base: 12, sm: 18 }} lh={1}>
              深夜料金
            </Text>
            <Text
              color="sonicSilver"
              fw={500}
              fz={{ base: 12, sm: 14 }}
              lh={1.2}
              mt={{ base: 8, sm: 12 }}
            >
              深夜料金として{helpers.numberFormat(MIDNIGHT_FEE)}
              円の追加料金が加算されます
            </Text>
          </Box>
          <Text className="menu-price" color="blackOlive" size={16}>
            {helpers.numberFormat(MIDNIGHT_FEE)}円
          </Text>
        </Flex>
      )}
      {!booking?.point ? (
        <Stack spacing={8} sx={sx.menuItem}>
          <UnstyledButton
            className="menu-button"
            disabled={
              !checkPointAmount?.maxPoints || !(booking?.menus || []).length
            }
            display="flex"
            onClick={() => setOpenedApplyPointModal(true)}
          >
            <ThemeIcon className="icon-point" size={24} variant="transparent">
              <IconPoint />
            </ThemeIcon>
            <Text
              className="add-menu"
              color="queenBlue.6"
              ml={{ base: 8, sm: 20 }}
              size={18}
              weight="bold"
            >
              ポイント利用
              <IconChervonRight />
            </Text>
          </UnstyledButton>
          <Text c="gray.7" fz={{ base: 12, sm: 14 }} ml={{ base: 32, sm: 42 }}>
            最大利用可能ポイント:
            <Text c="richBlack" component="span" ml={8}>
              {(sumPrice || 0) > 0
                ? helpers.numberFormat(checkPointAmount?.maxPoints || 0)
                : 0}
            </Text>
          </Text>
        </Stack>
      ) : (
        <Box className="coupon-item" sx={sx.menuItem}>
          <Flex
            gap={{ base: 5, sm: 20 }}
            mx={{ base: -10, sm: -20 }}
            pl={{ base: 10, sm: 20 }}
            pr={{ base: 10, sm: 40 }}
            py={{ base: 10, sm: 20 }}
          >
            <ActionIcon
              className="icon-remove"
              mt={{ base: -4, sm: 0 }}
              onClick={handleRemovePoint}
            >
              <IconRemove />
            </ActionIcon>
            <Box w="100%">
              <Flex gap={20}>
                <Text className="menu-name" size={18} weight="bold">
                  ポイント利用 ({helpers.numberFormat(booking?.point || 0)})
                </Text>
                <Text className="menu-price" color="#070203" size={16}>
                  -{' '}
                  {helpers.numberFormat(
                    exchangePointToPrice(
                      booking?.point || 0,
                      totalPoints?.exchangeRate.using.amount,
                      totalPoints?.exchangeRate.using.point,
                    ),
                  )}
                  円
                </Text>
              </Flex>
            </Box>
          </Flex>
        </Box>
      )}
      {!booking?.coupon ? (
        <Stack spacing={8} sx={sx.menuItem}>
          <Flex
            align="center"
            gap={{ base: 5, sm: 20 }}
            onClick={handleOpenApplyCoupon}
          >
            <ActionIcon className="icon-point">
              <IconCoupon />
            </ActionIcon>
            <Flex align="center">
              <Text
                className="add-menu"
                color="queenBlue.6"
                size={18}
                weight="bold"
              >
                クーポンを使う
                <IconChervonRight />
              </Text>
              <ActionIcon
                className="menu-info-icon"
                ml={8}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openContextModal({
                    modal: 'AlertModal',
                    withCloseButton: false,
                    size: 630,
                    innerProps: {
                      icon: <InfoIcon />,
                      content: (
                        <Box
                          px={10}
                        >{`クーポンとポイント併用する場合、\nクーポンの「最低利用金額」条件はポイントが適用後の金額で計算されます`}</Box>
                      ),
                      hasOkBtn: true,
                      confirmText: '閉じる',
                      confirmButtonProps: {
                        sx: sx.mobileModalButton,
                      },
                    },
                    centered: true,
                  });
                }}
                size={20}
              >
                <Image
                  alt="Menu info icon"
                  height={16}
                  src="/icons/icon-info-1.svg"
                  width={16}
                />
              </ActionIcon>
            </Flex>
          </Flex>
        </Stack>
      ) : (
        <Box className="coupon-item" sx={sx.menuItem}>
          <Flex
            bg={booking.couponError ? '#FFEBEB' : 'transparent'}
            gap={{ base: 5, sm: 20 }}
            mx={{ base: -10, sm: -20 }}
            pl={{ base: 10, sm: 20 }}
            pr={{ base: 10, sm: 40 }}
            py={{ base: 10, sm: 20 }}
          >
            <ActionIcon
              className="icon-remove"
              mt={{ base: -4, sm: 0 }}
              onClick={handleRemoveCoupon}
            >
              <IconRemove />
            </ActionIcon>
            <Box w="100%">
              <Flex gap={20}>
                <Text className="menu-name" size={18} weight="bold">
                  クーポン値引 ({booking?.coupon?.code})
                </Text>
                <Text className="menu-price" color="#070203" size={16}>
                  -{' '}
                  {!booking.couponError
                    ? helpers.numberFormat(booking?.coupon?.amount || 0)
                    : 0}
                  円
                </Text>
              </Flex>
              {booking.coupon.currency === '%' && booking.coupon.rate && (
                <Text
                  color="sonicSilver"
                  fw={500}
                  fz={{ base: 12, sm: 14 }}
                  lh={1}
                  mt={{ base: 8, sm: 12 }}
                >
                  ご利用料金が{booking.coupon.rate}%OFFになります
                </Text>
              )}
            </Box>
          </Flex>
        </Box>
      )}
      <Flex justify="space-between" sx={sx.summaryWrapper}>
        <Text color="blackOlive" size={16} sx={sx.summaryText}>
          合計時間 <span>{sumDuration}分</span>
        </Text>
        <Text color="blackOlive" size={16} sx={sx.summaryText}>
          合計金額（税込）{' '}
          <span>
            {helpers.numberFormat(
              booking?.couponError ? sumPrice : promotionPrice,
            )}
            円
          </span>
        </Text>
      </Flex>
      <MenuSelectModal
        handleChangeMenus={handleChangeMenus}
        menus={booking?.menus || []}
        onClose={handleToggleMenuSelect}
        opened={opened}
        therapistId={booking?.therapistId || ''}
      />
      <ApplyCouponModal
        areaCodes={booking?.areaCodes || []}
        dateBooking={booking?.dateBooking}
        menus={booking?.menus || []}
        onClose={() => setOpenedCoupon(false)}
        onConfirm={(newCoupon: ICoupon) => {
          onChange({
            coupon: newCoupon,
          });
          setOpenedCoupon(false);
        }}
        onReopen={() => setOpenedCoupon(true)}
        opened={openedCoupon}
        sumPriceWithPoint={sumPriceWithPoint || 0}
        therapistId={booking?.therapistId || ''}
      />
      <ApplyPointModal
        checkPointAmount={checkPointAmount}
        onClose={() => setOpenedApplyPointModal(false)}
        onConfirm={(point: number) => {
          onChange({ point });
          setOpenedApplyPointModal(false);
        }}
        opened={openedApplyPointModal}
        totalPrice={sumPrice || 0}
      />
    </Box>
  );
};

export default MenuCoupon;
