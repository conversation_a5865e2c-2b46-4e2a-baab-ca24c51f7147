import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  menuCouponWrapper: (_theme: MantineTheme) => ({
    backgroundColor: '#ffffff',
    padding: '10px 20px 20px',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    '@media (max-width: 768px)': {
      padding: '0 10px',
    },
    '&.menu-coupon-detail': {
      padding: '10px 20px 20px',
      '@media (max-width: 768px)': {
        padding: '0px 12px 16px',
      },
      '.menu-item': {
        padding: '20px',
        '.menu-name': {
          marginLeft: 30,
          '@media (max-width: 768px)': {
            marginLeft: 10,
          },
        },
        '.extension': {
          flexShrink: 0,
          fontSize: 14,
          color: '#ffffff',
          backgroundColor: '#a7a7a7',
          borderRadius: 4,
          padding: '3px 12px',
          marginLeft: 16,
          '@media (max-width: 768px)': {
            fontSize: 12,
            padding: '2px 8px',
            marginLeft: 8,
          },
        },
        '@media (max-width: 768px)': {
          padding: '16px 0',
          gap: 0,
        },
      },
      '.coupon-item': {
        padding: '20px 0px',
        svg: {
          width: 24,
          height: 24,
          flexShrink: 0,
        },
        '@media (max-width: 768px)': {
          padding: '16px 0',
          svg: {
            width: 16,
            height: 16,
          },
        },
        '&.midnight': {
          padding: '20px 0px',
          '@media (max-width: 768px)': {
            padding: '16px 0',
          },
        },
      },
      '.summary-content': {
        padding: '32px 0px 0px',
        '@media (max-width: 768px)': {
          padding: '15px 0px 0px',
        },
      },
    },
  }),
  menuItem: {
    padding: '20px 20px 20px 0',
    borderBottom: '1px solid #dedede',
    '.menu-button': {
      '&[disabled]': {
        '*': {
          color: '#A6A6A6',
        },
      },
      '*': {
        color: '#43749A',
      },
      '@media (max-width: 768px)': {
        '.icon-point': {
          width: 16,
          height: 16,
          maxWidth: 16,
          maxHeight: 16,
        },
      },
    },
    '@media (max-width: 768px)': {
      padding: '10px 0',
      gap: 8,
    },
    '&.coupon-item': {
      padding: 0,
    },
    '.icon-remove, .icon-add, .icon-point': {
      flexShrink: 0,
      svg: {
        width: 22,
        height: 22,
        '@media (max-width: 768px)': {
          width: 16,
          height: 16,
        },
      },
    },
    '.menu-image': {
      flexShrink: 0,
      objectFit: 'cover',
      '@media (max-width: 768px)': {
        width: 70,
        height: 50,
      },
    },
    '.menu-name': {
      fontWeight: 'bold',
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
    '.menu-price': {
      flex: '1 1 auto',
      wordBreak: 'keep-all',
      textAlign: 'end',
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
    },
    '.add-menu': {
      textDecoration: 'underline',
      textUnderlineOffset: '5px',
      cursor: 'pointer',
      '@media (max-width: 768px)': {
        fontSize: 12,
      },
      svg: {
        marginLeft: 8,
      },
    },
    '.menu-info-icon': {
      img: {
        '@media (max-width: 768px)': {
          width: 14,
          height: 14,
        },
      },
    },
  },
  addCouponBtn: {
    borderWidth: 2,
    height: 50,
    fontSize: 16,
    maxWidth: 350,
    width: '100%',
    margin: '16px auto 0',
    '@media (max-width: 768px)': {
      height: 40,
      fontSize: 12,
    },
  },
  summaryWrapper: {
    paddingTop: '20px',
    '@media (max-width: 768px)': {
      padding: '16px 0 16px',
    },
  },
  summaryText: {
    display: 'flex',
    gap: '0 20px',
    flexWrap: 'wrap',
    '&:first-of-type': {
      justifyContent: 'flex-start',
    },
    '&:last-of-type': {
      justifyContent: 'flex-end',
    },
    '@media (max-width: 768px)': {
      gap: '0 10px',
      fontSize: 12,
    },
    span: {
      fontWeight: 'bold',
    },
  },
  mobileModalButton: {
    '@media (max-width: 768px)': {
      maxWidth: 'unset !important',
    },
  },
};
