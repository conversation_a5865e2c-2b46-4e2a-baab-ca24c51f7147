import IconChervonRight from '@icons/icon-chevron-right.svg';
import { Box, Divider, Skeleton, Text } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import React from 'react';
import { BOOKING_PROGRESS_STATUS } from 'utils/constants';
import dayjs from 'utils/dayjs';

import { sx } from './styles';

const BookingProgress: React.FC<{
  status?: string;
  timestamp?: string;
  reason?: string;
  statusHistory?: {
    status: string;
    reason: string;
    timestamp: string;
  }[];
  loading?: boolean;
}> = ({ status, timestamp, statusHistory, reason, loading = false }) => {
  const renderStatus = () => {
    if (status && BOOKING_PROGRESS_STATUS[status]) {
      const title =
        status === 'DONE' && reason === 'finishWithoutTreatment'
          ? '決済完了'
          : BOOKING_PROGRESS_STATUS[status];
      return `リクエスト${title}`;
    }
    return '---';
  };

  if (loading) {
    return (
      <Box sx={{ ...sx.bookingProgressWrapper, gap: 32 }}>
        <Skeleton h={18} w="75%" />
        <Divider />
        <Skeleton h={18} w="75%" />
      </Box>
    );
  }

  return (
    <Box
      onClick={() => {
        openContextModal({
          modal: 'BookingProgressModal',
          size: 630,
          innerProps: {
            currentStatus: status,
            statusHistory,
          },
        });
      }}
      sx={sx.bookingProgressWrapper}
      tabIndex={0}
    >
      <Text className="date-time" size={18} weight="bold">
        {timestamp ? dayjs(timestamp).format('llll') : '---'}
      </Text>
      <Divider />
      <Text className="status" size={18} weight="bold">
        {renderStatus()}
        <IconChervonRight />
      </Text>
    </Box>
  );
};

export default BookingProgress;
