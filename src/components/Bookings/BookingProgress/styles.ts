import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  bookingProgressWrapper: {
    backgroundColor: '#ffffff',
    padding: '30px 40px',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    gap: 21,
    cursor: 'pointer',
    '@media (max-width: 768px)': {
      padding: '15px 10px',
      gap: 5,
      position: 'relative',
    },
    '.mantine-Text-root': {
      display: 'flex',
      alignItems: 'center',
      '@media (max-width: 768px)': {
        fontSize: 14,
        fontWeight: 'normal',
      },
    },
    '.mantine-Divider-root': {
      margin: '0 -20px',
      '@media (max-width: 768px)': {
        margin: 0,
        borderColor: 'transparent',
      },
    },
    '.status': {
      position: 'relative',
      '@media (max-width: 768px)': {
        position: 'unset',
      },
      svg: {
        position: 'absolute',
        right: 0,
        width: 16,
        height: 16,
        '@media (max-width: 768px)': {
          top: '50%',
          right: 15,
          transform: 'translateY(-50%)',
          width: 12,
          height: 12,
        },
      },
    },
  },
};
