import IconChervonRight from '@icons/icon-chevron-right.svg';
import IconClock from '@icons/icon-clock.svg';
import IconLocation from '@icons/icon-location.svg';
import { Box, Divider, Flex, Text } from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import { AddessHistoryModal } from 'components/Modals';
import { useFetchData, useMutate } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import type { AddressHistoryItem } from 'models/address';
import type { ITherapistItem } from 'models/therapist';
import { therapistQuery } from 'models/therapist';
import React, { useState } from 'react';
import { LOCAL_STORAGE_KEY, MIDNIGHT_FEE } from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers, { formatAddress } from 'utils/helpers';

import { sx } from './styles';

interface TreatmentPlaceProps {
  booking?: ICreateBooking;
  sumDuration: number;
  onChange: (value: Partial<ICreateBooking>) => void;
  isMidnight: boolean;
}

const TreatmentPlace: React.FC<TreatmentPlaceProps> = ({
  booking,
  onChange,
  sumDuration,
  isMidnight,
}) => {
  const validDateBooking = helpers.getValidDate();
  const [addressHistories] = useLocalStorage<AddressHistoryItem[]>({
    key: LOCAL_STORAGE_KEY.ADDRESS_HISTORIES,
    defaultValue: [],
  });
  const { mutateAsync: getTherapistTimeslotsFn } = useMutate<
    Record<string, unknown>,
    { date: string; status: boolean }[]
  >(therapistQuery.getTherapistTimeslots);
  const { data: therapistDetail } = useFetchData<ITherapistItem>({
    ...therapistQuery.getTherapistDetail({ id: booking?.therapistId }),
    enabled: false,
  });
  const [opened, setOpened] = useState(false);

  const handleToggleAddressHistory = () => {
    setOpened(!opened);
  };

  const handleOpenTimeSlot = () => {
    openContextModal({
      modal: 'DateTimePickerModal',
      size: 830,
      innerProps: {
        initialData: booking?.dateBooking,
        onConfirm: (value: string) => {
          onChange({
            dateBooking: value,
          });
        },
        mapTimeOptions: async (selectedDate: Date) => {
          const timeslots = await getTherapistTimeslotsFn({
            therapistId: booking?.therapistId,
            start: dayjs(selectedDate).startOf('day').toISOString(),
            end: dayjs(selectedDate).endOf('day').toISOString(),
            duration: sumDuration,
            areaCodes: booking?.areaCodes,
          });
          if (timeslots.length !== 0) {
            return timeslots.map((slot) => ({
              label: dayjs(slot.date).format('HH:mm'),
              value: dayjs(slot.date).format('HH:mm'),
              disabled:
                !slot.status || dayjs(validDateBooking).isAfter(slot.date),
            }));
          }
          return helpers.mapTimeOptions(dayjs().subtract(1, 'd').toDate());
        },
      },
    });
  };

  const handleChangeAddress = (value: AddressHistoryItem) => {
    onChange({
      address: value.address,
      buildingType: value.buildingType,
      nameplate: value.nameplate,
      buildingDetails: value.buildingDetails,
      accessMethod: value.accessMethod,
      areaCodes: value.areaCodes,
      isGoogle: value.isGoogle,
      areaNames: '',
    });
  };

  return (
    <Box sx={sx.treatmentPlaceWrapper}>
      <Flex
        align="center"
        className="item-row"
        gap={20}
        onClick={handleOpenTimeSlot}
        tabIndex={0}
      >
        <Flex className="row-wrap" gap={20}>
          <IconClock className="icon-start" />
          <Box>
            <Text className="hover" size={18} sx={sx.title} weight="bold">
              {dayjs(booking?.dateBooking).format('llll')}
            </Text>
            {isMidnight ? (
              <Box mb={-8} mt={{ base: 4, sm: 8 }}>
                <Text
                  color="marigold"
                  fw={500}
                  fz={{ base: 12, sm: 14 }}
                  lh={1.17}
                  mb={{ base: 8, sm: 12 }}
                >
                  ご指定時間{therapistDetail?.minMenu?.duration || 60}
                  分を切ると、予約リクエストができなくなります
                </Text>
                <Text
                  color="marigold"
                  fw={500}
                  fz={{ base: 12, sm: 14 }}
                  lh={1.17}
                >
                  23:30〜5:30のご予約には別途深夜料金
                  {helpers.numberFormat(MIDNIGHT_FEE)}円がかかります
                </Text>
              </Box>
            ) : (
              <Text color="sonicSilver" mt={8} size={14} sx={sx.content}>
                ご指定時間{therapistDetail?.minMenu?.duration || 60}
                分を切ると、予約リクエストができなくなります
              </Text>
            )}
          </Box>
        </Flex>
        <IconChervonRight className="icon-end" />
      </Flex>
      <Divider my={12} />
      <Flex
        align="center"
        className="item-row"
        gap={20}
        onClick={handleToggleAddressHistory}
        tabIndex={0}
      >
        <IconLocation className="icon-start" />
        <Text className="hover" lineClamp={1} size={18} sx={sx.title}>
          {formatAddress(booking)}
        </Text>
        <IconChervonRight className="icon-end" />
      </Flex>
      <AddessHistoryModal
        handleSelectAddress={handleChangeAddress}
        initialValues={
          booking?.areaNames
            ? {
                prefecture: booking?.areaCodes[0] || '',
                city: booking?.areaCodes[1] || '',
                ward: booking?.areaCodes[2] || '',
                district: booking?.areaCodes[3] || '',
                address: '',
                buildingType: '',
              }
            : {
                prefecture: '',
                city: '',
                ward: '',
                district: '',
                address: '',
                buildingType: '',
              }
        }
        onClose={handleToggleAddressHistory}
        openForm={addressHistories.length === 0 && !!booking?.areaNames}
        opened={opened}
      />
    </Box>
  );
};

export default TreatmentPlace;
