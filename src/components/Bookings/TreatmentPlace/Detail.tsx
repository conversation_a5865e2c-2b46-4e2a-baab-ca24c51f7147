import IconClock from '@icons/icon-clock.svg';
import IconLocation from '@icons/icon-location.svg';
import { Box, Divider, Flex, Skeleton, Text } from '@mantine/core';
import type { AddressHistoryItem } from 'models/address';
import React from 'react';
import dayjs from 'utils/dayjs';
import { formatAddress } from 'utils/helpers';

import { sx } from './styles';

const TreatmentPlaceDetail: React.FC<{
  addressItem?: AddressHistoryItem;
  dateBooking?: string;
  loading?: boolean;
}> = ({ dateBooking, loading = false, addressItem }) => {
  if (loading) {
    return (
      <Box className="treatment-place-detail" sx={sx.treatmentPlaceWrapper}>
        <Box className="item-row">
          <Skeleton h={18} w="70%" />
        </Box>
        <Divider />
        <Box className="item-row">
          <Skeleton h={18} w="70%" />
        </Box>
      </Box>
    );
  }
  return (
    <Box className="treatment-place-detail" sx={sx.treatmentPlaceWrapper}>
      <Flex align="center" className="item-row" gap={20}>
        <IconClock className="icon-start" />
        <Text className="hover" size={18} sx={sx.title} weight="bold">
          {dateBooking ? dayjs(dateBooking).format('llll') : '---'}
        </Text>
      </Flex>
      <Divider />
      <Flex align="center" className="item-row" gap={20}>
        <IconLocation className="icon-start" />
        <Text className="hover" size={18} sx={sx.title} weight="bold">
          {formatAddress(addressItem)}
        </Text>
      </Flex>
    </Box>
  );
};

export default TreatmentPlaceDetail;
