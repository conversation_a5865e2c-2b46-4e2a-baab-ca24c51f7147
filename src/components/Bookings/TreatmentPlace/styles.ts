import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  treatmentPlaceWrapper: (_theme: MantineTheme) => ({
    backgroundColor: '#ffffff',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    '.item-row': {
      cursor: 'pointer',
      padding: '27px 32px',
      '@media (max-width: 768px)': {
        padding: '20px 10px',
        gap: 8,
      },
      '& > *:not(svg)': {
        flexGrow: 1,
      },
      '.row-wrap': {
        '@media (max-width: 768px)': {
          gap: 8,
          '.icon-start': {
            marginTop: 2,
          },
        },
      },
      '.icon-start': {
        width: 24,
        height: 24,
        flexShrink: 0,
        '@media (max-width: 768px)': {
          width: 16,
          height: 16,
        },
      },
      '.icon-end': {
        width: 16,
        height: 16,
        '@media (max-width: 768px)': {
          width: 12,
          height: 12,
        },
      },
      '&:hover .hover': {
        textDecoration: 'underline',
      },
    },
    '.mantine-Divider-root': {
      margin: '0 20px',
      borderColor: '#dedede',
      '@media (max-width: 768px)': {
        margin: '0 10px',
      },
    },
    '&.treatment-place-detail': {
      '@media (max-width: 768px)': {
        padding: '15px 10px 15px 15px',
      },
      '.item-row': {
        cursor: 'auto',
        padding: '27px 40px',
        '@media (max-width: 768px)': {
          alignItems: 'flex-start',
          padding: 0,
        },
        '.mantine-Text-root': {
          fontWeight: 'normal',
        },
        '&:hover .hover': {
          textDecoration: 'none',
        },
      },
      '.mantine-Divider-root': {
        '@media (max-width: 768px)': {
          borderColor: 'transparent',
          margin: '5px 0',
        },
      },
      '.icon-start': {
        '@media (max-width: 768px)': {
          marginTop: 2,
        },
      },
    },
  }),
  title: {
    '@media (max-width: 768px)': {
      fontSize: 14,
    },
  },
  content: {
    '@media (max-width: 768px)': {
      fontSize: 12,
      marginTop: 0,
    },
  },
};
