import IconChevronRight from '@icons/icon-chevron-right.svg';
import IconClock from '@icons/icon-clock.svg';
import IconMessage from '@icons/icon-comment.svg';
import IconMenu from '@icons/icon-menu.svg';
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Flex,
  List,
  Text,
} from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { useGlobalState } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import type { IBookingItem } from 'models/booking';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  BOOKING_CATEGORIES,
  BOOKING_REASONS,
  BOOKING_STATUSES,
  LOCAL_STORAGE_KEY,
} from 'utils/constants';
import dayjs from 'utils/dayjs';
import helpers, { eventLog } from 'utils/helpers';

import { styles, sx } from './styles';

const BookingItem: React.FC<{
  booking: IBookingItem;
}> = ({ booking }) => {
  const router = useRouter();
  const { firebaseUser } = useGlobalState();
  const [bookingStorage, setBooking, removeBooking] =
    useLocalStorage<ICreateBooking>({
      key: LOCAL_STORAGE_KEY.BOOKING,
    });

  const handleStartBooking = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    event.stopPropagation();
    if (bookingStorage) {
      eventLog('change_therapist', {
        therapist_id: bookingStorage.therapistId,
        therapist_username: bookingStorage?.therapistName,
        therapist_id_change: booking.therapist._id,
        therapist_username_change: booking.therapist.nickName,
      });
      removeBooking();
    }
    const areaCodes = [
      booking.prefecture?.areaCode,
      booking.city?.areaCode,
      booking.ward?.areaCode,
      booking.district?.areaCode,
    ].filter((i) => !!i) as string[];

    setBooking({
      therapistId: booking.therapist._id,
      menus: booking?.menus,
      areaCodes,
      dateBooking: helpers.getValidDate(),
      cardId: booking?.payment.extra?.cardId || '',
      address: booking?.googleAddress || booking?.address || '',
      buildingType: booking?.buildingType,
      nameplate: booking?.nameplate,
      buildingDetails: booking?.buildingDetails,
      accessMethod: booking?.accessMethod,
      isGoogle: !!booking?.googleAddress,
      customerNote: booking?.customerNote,
      parkingNote: booking?.parkingNote,
    });
    eventLog('rebook', {
      booking_id: booking._id,
      reference_page: 'history_detail',
    });
    router.push('/booking');
  };

  const renderActionBtn = () => {
    switch (true) {
      case booking.currentStatus.category === BOOKING_CATEGORIES.REQUEST:
        return (
          <Button fullWidth styles={styles.bottomBtn}>
            セラピストからの返答をお待ち下さい
          </Button>
        );
      case booking.currentStatus.category === BOOKING_CATEGORIES.CONFIRMED:
        return (
          <Button fullWidth styles={styles.bottomBtn}>
            セラピストとマッチングしました
          </Button>
        );
      case booking.currentStatus.status === BOOKING_STATUSES.CANCELED &&
        ![
          BOOKING_REASONS.THERAPIST_CANCEL_BOOKING,
          BOOKING_REASONS.THERAPIST_DENY_BOOKING,
        ].includes(booking.currentStatus.reason):
        return null;
      case booking.currentStatus.status === BOOKING_STATUSES.DONE &&
        booking.currentStatus.reason !==
          BOOKING_REASONS.FINISH_WITHOUT_TREATMENT &&
        !booking.therapist.isReviewed &&
        !booking.reviewExpired:
        return (
          <Button
            bg="white"
            fullWidth
            fz={{ base: 12, sm: 18 }}
            h={{ base: 30, sm: 50 }}
            mt={{ base: 10, sm: 20 }}
            onClick={(e: any) => {
              e.preventDefault();
              e.stopPropagation();
              router.push(`/booking/${booking?._id}/review?ref=BookingHistory`);
            }}
            variant="outline"
          >
            セラピストを評価する
          </Button>
        );
      default:
        return (
          <Button
            bg="white"
            fullWidth
            fz={{ base: 12, sm: 18 }}
            h={{ base: 30, sm: 50 }}
            mt={{ base: 10, sm: 20 }}
            onClick={handleStartBooking}
            variant="outline"
          >
            同じセラピストを予約
          </Button>
        );
    }
  };

  const renderMarker = () => {
    switch (true) {
      case [
        BOOKING_REASONS.THERAPIST_DENY_BOOKING,
        BOOKING_REASONS.THERAPIST_CANCEL_BOOKING,
      ].includes(booking.currentStatus.reason):
        return <Badge styles={styles.marker}>セラピストキャンセル</Badge>;
      case !!booking.payment.transaction?.error:
        return (
          <Badge className="red--maker" styles={styles.marker}>
            支払いエラー
          </Badge>
        );
      case booking.currentStatus.reason === BOOKING_REASONS.START_MASSAGE:
        return (
          <Badge className="purple--maker" styles={styles.marker}>
            施術中
          </Badge>
        );
      case booking.currentStatus.status === BOOKING_STATUSES.CANCELED &&
        !booking.cancellingNote?.isCharged:
        return <Badge styles={styles.marker}>キャンセル</Badge>;
      default:
        return null;
    }
  };

  return (
    <Link href={`/booking/${booking._id}`}>
      <Card sx={sx.bookingItemWrapper}>
        {renderMarker()}
        <Flex sx={sx.bookingItemBody}>
          <Avatar
            alt=""
            src={
              booking.therapist.avatar ||
              '/icons/icon-avatar-default-therapist.svg'
            }
            styles={styles.avatar}
          />
          <Flex
            direction="column"
            gap={16}
            justify="center"
            sx={sx.bookingContentGroup}
          >
            <Avatar
              alt=""
              src={
                booking.therapist.avatar ||
                '/icons/icon-avatar-default-therapist.svg'
              }
              styles={styles.smallAvatar}
            />
            <Text lineClamp={1} sx={sx.therapistName}>
              {booking.therapist.nickName}
            </Text>
            <List center styles={styles.bookingInfoList}>
              <List.Item icon={<IconClock />}>
                {dayjs(booking.dateBooking).format(
                  'YYYY年MM月DD日（ddd）HH:mm',
                )}
              </List.Item>
              <List.Item className="menu-info" icon={<IconMenu />}>
                <div className="bookingItem__menuGroup">
                  <span>{booking.menus.length}メニュー</span>
                  <span>{booking.duration}分</span>
                  <span>
                    {booking.payment.amount.toLocaleString(undefined, {
                      maximumFractionDigits: 2,
                    })}
                    円
                  </span>
                  {firebaseUser?.bookingIds?.includes(booking._id) && (
                    <ActionIcon
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        router.push({
                          pathname: '/booking/[id]',
                          query: {
                            id: booking._id,
                            chat: true,
                          },
                        });
                      }}
                      sx={sx.messageBtn}
                    >
                      <IconMessage />
                      <span className="red-dot" />
                    </ActionIcon>
                  )}
                </div>
              </List.Item>
            </List>
          </Flex>
          <Button leftIcon={<IconChevronRight />} styles={styles.detailBtn} />
        </Flex>

        {renderActionBtn()}
      </Card>
    </Link>
  );
};

export default BookingItem;
