import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  bookingItemWrapper: {
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    border: 'none',
    borderRadius: 6,
    padding: '20px !important',
    height: '100%',
    '@media (min-width: 768px) and (max-width: 1024px)': {
      padding: '16px !important',
    },
    '@media (max-width: 768px)': {
      padding: '10px !important',
    },
  },

  bookingItemBody: {
    gap: 20,
    fontSize: 20,
    fontWeight: 'bold',
    position: 'relative',
    '@media (max-width: 768px)': {
      fontSize: 16,
    },
  },

  therapistName: {
    width: '100%',
    '@media (max-width: 768px)': {
      width: 'calc(100% - 71px)',
    },
  },

  bookingContentGroup: {
    width: '100%',
    paddingRight: 16,
    '@media (max-width: 768px)': {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      alignItems: 'center',
      gap: 10,
    },
  },
  messageBtn: {
    position: 'absolute',
    right: -16,
    margin: 0,
    color: 'black',
    '@media (max-width: 768px)': {
      right: -24,
    },
    '.red-dot': {
      top: 2,
      right: 2,
      transform: 'translate(50%, -50%)',
      width: 12,
      height: 12,
      position: 'absolute',
      '@media (max-width: 768px)': {
        top: 6,
        right: 6,
        width: 8,
        height: 8,
      },
    },
    svg: {
      width: 24,
      height: 24,
      '@media (max-width: 768px)': {
        width: 14,
        height: 14,
      },
    },
  },
};

export const styles: Record<string, any> = {
  avatar: {
    root: {
      borderRadius: '50%',
      minWidth: 160,
      height: 160,
      '@media (min-width: 768px) and (max-width: 1024px)': {
        minWidth: 60,
        height: 60,
      },
      '@media (max-width: 768px)': {
        display: 'none',
      },
    },
  },

  smallAvatar: {
    root: {
      display: 'none',
      borderRadius: '50%',
      minWidth: 55,
      height: 55,
      '@media (max-width: 768px)': {
        display: 'inline-flex',
      },
    },
  },

  bookingInfoList: (theme: MantineTheme) => ({
    root: {
      fontSize: 16,
      color: theme.colors.blackOlive,
      width: '100%',
      '@media (max-width: 768px)': {
        fontSize: 14,
      },
    },
    item: {
      '&:not(:last-of-type)': {
        marginBottom: 10,
      },
    },
    itemWrapper: {
      width: '100%',
      '.bookingItem__menuGroup': {
        display: 'flex',
        gap: '0 20px',
        wordBreak: 'keep-all',
        flexWrap: 'wrap',
        alignItems: 'center',
        position: 'relative',
      },
    },
    itemIcon: {
      marginRight: 20,
      '@media (min-width: 768px) and (max-width: 1024px)': {
        marginRight: 10,
      },
      '@media (max-width: 768px)': {
        marginRight: 8,
      },
      svg: {
        width: 20,
        height: 20,
        '@media (max-width: 768px)': {
          width: 14,
          height: 14,
        },
      },
      '& + *': {
        width: '100%',
      },
    },
  }),

  detailBtn: {
    root: {
      background: 'none',
      padding: 0,
      position: 'absolute',
      right: 0,
      top: '50%',
      marginTop: -10,
      boxShadow: 'none !important',
      '&:hover': {
        background: 'none',
      },
    },
    leftIcon: {
      marginRight: 0,
      svg: {
        color: 'black',
        width: 10,
        height: 20,
        '@media (max-width: 768px)': {
          width: 6,
          height: 12,
        },
      },
    },
  },

  bottomBtn: (theme: MantineTheme) => ({
    root: {
      backgroundColor: '#f8f8f8',
      color: theme.colors.blackOlive,
      fontSize: 18,
      boxShadow: 'none',
      height: 50,
      marginTop: 20,
      '&:hover': {
        backgroundColor: '#f8f8f8',
      },
      '@media (max-width: 768px)': {
        height: 30,
        fontSize: 12,
        marginTop: 10,
      },
    },
    inner: {
      justifyContent: 'flex-start',
    },
  }),

  marker: (theme: MantineTheme) => ({
    root: {
      fontSize: 14,
      color: theme.colors.darkBlue,
      backgroundColor: theme.colors.ghostWhite,
      height: 34,
      padding: '2px 14px',
      borderRadius: 0,
      position: 'absolute',
      right: 0,
      top: 0,
      '&.purple--maker': {
        color: theme.colors.purple,
        backgroundColor: theme.colors.lightPurple,
      },
      '&.red--maker': {
        color: '#db1e0e',
        backgroundColor: '#fff7f7',
      },
      '@media (max-width: 768px)': {
        fontSize: 10,
        height: 20,
        padding: '2px 10px',
      },
    },
  }),
};
