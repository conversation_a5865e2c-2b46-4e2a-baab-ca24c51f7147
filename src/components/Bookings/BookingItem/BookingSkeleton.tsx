import { Card, Flex, Skeleton } from '@mantine/core';
import React from 'react';

import { sx } from './styles';

const BookingSkeleton = () => {
  return (
    <Card sx={sx.bookingItemWrapper}>
      <Flex align="center" gap={20}>
        <Skeleton
          circle
          h={160}
          sx={{ flexShrink: 0, borderRadius: '50%' }}
          w={160}
        />
        <Flex direction="column" gap={20} pr={20} w="100%">
          <Skeleton h={30} mb={10} w="100%" />
          <Skeleton h={14} w="70%" />
          <Skeleton h={14} w="70%" />
        </Flex>
      </Flex>
    </Card>
  );
};

export default BookingSkeleton;
