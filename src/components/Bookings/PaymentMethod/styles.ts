import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  paymentMethodWrapper: (_theme: MantineTheme) => ({
    backgroundColor: '#ffffff',
    padding: 20,
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    cursor: 'pointer',
    '@media (max-width: 768px)': {
      padding: 10,
    },
    '.card-info': {
      padding: '0 12px',
      '@media (max-width: 768px)': {
        padding: '0 12px 0 0',
        gap: 12,
      },
      svg: {
        '@media (max-width: 768px)': {
          width: 32,
          height: 32,
        },
      },
      '& > *:not(svg)': {
        flexGrow: 1,
      },
      '.icon-end': {
        width: 16,
        height: 16,
        '@media (max-width: 768px)': {
          width: 12,
          height: 12,
        },
      },
      '.card-name': {
        '@media (max-width: 768px)': {
          fontSize: 14,
        },
      },
    },
    '.add-card-btn': {
      height: 50,
      fontSize: 16,
      borderWidth: 2,
      '@media (max-width: 768px)': {
        fontSize: 13,
        height: 40,
      },
    },
    '.note': {
      marginTop: 20,
      borderRadius: 4,
      padding: '16px 20px',
      backgroundColor: '#f8f8f8',
      '@media (max-width: 768px)': {
        fontSize: 12,
        marginTop: 10,
        padding: 10,
      },
    },
    '&.payment-method-detail': {
      cursor: 'auto',
      '@media (max-width: 768px)': {
        padding: 10,
      },
      '.card-info': {
        padding: '0 10px',
        '&[data-has-note=true]': {
          padding: '0 20px',
        },
        '@media (max-width: 768px)': {
          padding: 0,
        },
      },
    },
  }),
};
