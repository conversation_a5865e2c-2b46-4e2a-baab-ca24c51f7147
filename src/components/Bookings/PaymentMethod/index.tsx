import IconChervonRight from '@icons/icon-chevron-right.svg';
import { Box, Button, Flex, Text } from '@mantine/core';
import { PaymentMethodModal } from 'components/Modals';
import { useFetchData } from 'hooks';
import type { ICreateBooking } from 'hooks/types';
import type { CardListResponse } from 'models/payment';
import { paymentQuery } from 'models/payment';
import React, { useEffect, useState } from 'react';
import type { ValidCardNiceType } from 'utils/constants';
import { CardIcon } from 'utils/constants';

import { sx } from './styles';

interface PaymentMethodProps {
  booking?: ICreateBooking;
  onChange: (value: Partial<ICreateBooking>) => void;
}

const PaymentMethod: React.FC<PaymentMethodProps> = ({ booking, onChange }) => {
  const [opened, setOpened] = useState(false);

  const handleTogglePaymentModal = () => {
    setOpened(!opened);
  };

  const { data } = useFetchData<CardListResponse>({
    ...paymentQuery.getCardList,
    staleTime: 1000 * 60 * 2,
  });

  const activeCard = (data?.cards || []).find(
    (card) => card.id === booking?.cardId,
  );

  const handleChangePayment = (id: string) => {
    handleTogglePaymentModal();
    onChange({
      cardId: id,
    });
  };

  useEffect(() => {
    if (data?.defaultCard && booking && !booking.cardId) {
      onChange({ cardId: data?.defaultCard });
    }
  }, [booking, booking?.cardId, data?.defaultCard, onChange]);

  return (
    <>
      <Box
        onClick={handleTogglePaymentModal}
        sx={sx.paymentMethodWrapper}
        tabIndex={0}
      >
        {activeCard ? (
          <Flex align="center" className="card-info" gap={20}>
            {CardIcon[activeCard?.brand as ValidCardNiceType]}
            <Text className="card-name" color="blackOlive" size={18}>
              {`下三桁 ${activeCard.last4}`}
            </Text>
            <IconChervonRight className="icon-end" />
          </Flex>
        ) : (
          <Button
            bg="white"
            className="add-card-btn"
            fullWidth
            variant="outline"
          >
            お支払い方法を登録
          </Button>
        )}
        <Text className="note" color="blackOlive" size={16}>
          予約リクエストを送ってもまだお支払いは発生しません。
          <br />
          クレジットカード決済は施術完了後に行われます。
        </Text>
      </Box>
      <PaymentMethodModal
        activeCard={booking?.cardId || ''}
        defaultCard={data?.defaultCard || ''}
        handleChangePayment={handleChangePayment}
        onClose={handleTogglePaymentModal}
        opened={opened}
      />
    </>
  );
};

export default PaymentMethod;
