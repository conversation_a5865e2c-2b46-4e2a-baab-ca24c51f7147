import IconCash from '@icons/icon-cash.svg';
import { Box, Flex, Skeleton, Text } from '@mantine/core';
import React from 'react';
import type { ValidCardNiceType } from 'utils/constants';
import { CardIcon } from 'utils/constants';
import helpers from 'utils/helpers';

import { sx } from './styles';

const PaymentMethodDetail: React.FC<{
  last4?: string;
  isCancelBookingGotError?: boolean;
  isHaveCancelBookingFee?: boolean;
  isPaymentError?: boolean;
  status?: string;
  brand?: string;
  sumPrice?: number;
  type?: 'cash' | 'gcard3d';
  loading?: boolean;
}> = ({
  last4,
  brand,
  isCancelBookingGotError,
  isHaveCancelBookingFee,
  isPaymentError,
  status,
  sumPrice,
  type,
  loading = false,
}) => {
  let noteText = '';
  if (!isPaymentError) {
    if (isHaveCancelBookingFee || isCancelBookingGotError) {
      noteText = `キャンセル料として${helpers.numberFormat(
        sumPrice,
      )}円頂戴いたしました。`;
    } else if (status === 'CANCELED') {
      noteText = 'この予約はキャンセルされたため、料金は発生していません。';
    } else if (
      status &&
      ['NEW', 'PENDING', 'CONFIRMED', 'ARRIVED'].includes(status)
    ) {
      noteText = 'クレジットカード決済は施術完了後に行われます。';
    }
  }

  if (loading) {
    return (
      <Flex
        align="center"
        className="payment-method-detail"
        gap={20}
        sx={sx.paymentMethodWrapper}
      >
        <Skeleton h={37} w={50} />
        <Skeleton h={18} w="60%" />
      </Flex>
    );
  }

  return (
    <Box className="payment-method-detail" sx={sx.paymentMethodWrapper}>
      {type === 'gcard3d' && (
        <Flex
          align="center"
          className="card-info"
          data-has-note={!!noteText}
          gap={20}
        >
          {CardIcon[brand as ValidCardNiceType]}
          <Text className="card-name" color="blackOlive" size={18}>
            {!last4 ? '---' : `下三桁 ${last4}`}
          </Text>
        </Flex>
      )}
      {type === 'cash' && (
        <Flex
          align="center"
          className="card-info"
          data-has-note={!!noteText}
          gap={20}
        >
          <IconCash />
          <Text className="card-name" color="blackOlive" size={18}>
            現金
          </Text>
        </Flex>
      )}
      {noteText && (
        <Text className="note" color="blackOlive" size={16}>
          {noteText}
        </Text>
      )}
    </Box>
  );
};

export default PaymentMethodDetail;
