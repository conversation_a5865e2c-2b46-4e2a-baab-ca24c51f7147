import IconChervonRight from '@icons/icon-chevron-right.svg';
import { Box, Button, Flex, Rating, Skeleton, Text } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useFetchData, useGlobalState } from 'hooks';
import type { IBookingDetail, IBookingReview } from 'models/booking';
import type { IConfigurationItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import type { ITherapistItem } from 'models/therapist';
import Image from 'next/image';
import Link from 'next/link';
import router from 'next/router';
import type { MouseEventHandler } from 'react';
import React from 'react';
import dayjs from 'utils/dayjs';
import { checkChattingExpired } from 'utils/helpers';

import useStyles from './styles';

const TherapistInfo: React.FC<{
  detail?: ITherapistItem;
  summaryReview?: {
    sumRating: number;
    sumReviewer: number;
  };
  review?: IBookingReview;
  bookingId?: string;
  bookingStatus?: string;
  bookingReason?: string;
  bookingStatusHistory?: IBookingDetail['statusHistory'];
  reviewExpired?: boolean;
  isOnCall?: boolean;
  onOpenChat?: () => void;
  onCall?: MouseEventHandler<HTMLButtonElement>;
  loading?: boolean;
}> = ({
  detail,
  summaryReview,
  bookingId,
  bookingStatus,
  bookingReason,
  bookingStatusHistory = [],
  reviewExpired,
  review,
  onOpenChat,
  onCall,
  isOnCall,
  loading = false,
}) => {
  const { classes } = useStyles();
  const { firebaseUser } = useGlobalState();
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const rating =
    (summaryReview?.sumRating || 0) / (summaryReview?.sumReviewer || 0) || 0;
  const canReviewTherapist =
    bookingStatus === 'DONE' && bookingReason !== 'finishWithoutTreatment';
  const isReviewed = !!review?.rating;

  const { data: configurations } = useFetchData<
    IConfigurationItem[],
    {
      [key: string]: IConfigurationItem;
    }
  >({
    ...resourceQuery.getConfigs,
    select: (response) => {
      const objectKeyMap: Record<string, IConfigurationItem> = {};
      response.forEach((item) => {
        objectKeyMap[item.key] = item;
      });
      return objectKeyMap;
    },
    enabled: canReviewTherapist,
  });

  const renderActionBtn = () => {
    const isChattingExpired = checkChattingExpired(bookingStatusHistory);

    // Show Chat button for all statuses except CANCELED
    const shouldShowChatButton =
      bookingStatus !== 'CANCELED' && !isChattingExpired;

    // Show Call button for all statuses except CANCELED and DONE
    const shouldShowCallButton =
      bookingStatus !== 'CANCELED' &&
      bookingStatus !== 'DONE' &&
      !isChattingExpired;

    // If neither button should be shown, return empty
    if (!shouldShowChatButton && !shouldShowCallButton) {
      return <></>;
    }

    return (
      <Flex className={classes.actionBtnGroup}>
        {shouldShowChatButton && (
          <Button
            bg="white"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (onOpenChat) {
                onOpenChat();
              }
            }}
            variant="outline"
          >
            チャットで連絡する
            {firebaseUser?.bookingIds?.includes(bookingId || '') && (
              <span className="red-dot" />
            )}
          </Button>
        )}
        {shouldShowCallButton && (
          <Button
            bg="white"
            disabled={isOnCall}
            onClick={onCall}
            variant="outline"
          >
            セラピストと通話する
          </Button>
        )}
      </Flex>
    );
  };

  const renderReviewContent = () => {
    if (canReviewTherapist) {
      if (reviewExpired) {
        return (
          <Text
            color="blackOlive"
            fw={400}
            fz={{ base: 12, sm: 14 }}
            lh={{ base: '16px', sm: '20px' }}
            mt={{ base: 12, sm: 16 }}
            px={{ base: 12, sm: 16 }}
            py={12}
            sx={{
              backgroundColor: '#f8f8f8',
              borderRadius: 6,
            }}
          >
            レビューの投稿期限は、施術後
            {configurations?.reviewDuration?.value || 7}
            日間です。
            <br />
            期限が過ぎた後、レビューを投稿することはできません。
          </Text>
        );
      }
      return (
        <Flex
          align="center"
          className={classes.therapistRating}
          data-reviewed={isReviewed}
          direction="column"
          gap={4}
        >
          {isReviewed ? (
            <Text color="sonicSilver" size={14}>
              送信日時: {dayjs(review.updatedAt).format('lll')}
            </Text>
          ) : (
            <Text align="center" color="sonicSilver" size={14}>
              セラピストはいかがでしたか？
              <br />
              星を選んで評価しましょう
            </Text>
          )}
          <Rating
            count={5}
            onChange={(value) => {
              router.push(
                `/booking/${bookingId}/review?rating=${value}&ref=BookingDetail`,
              );
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            readOnly={isReviewed}
            size="30px"
            value={review?.rating || 0}
          />
          {review?.comment?.therapist?.trim() && (
            <Text
              className={classes.reviewComment}
              color="blackOlive"
              size={14}
            >
              {review?.comment.therapist.trim()}
            </Text>
          )}
        </Flex>
      );
    }
    return <></>;
  };

  const getHref = () => {
    if (detail) {
      if (bookingStatus === 'CANCELED') {
        return {
          href: `/therapist/${detail?._id}`,
          rel: 'noreferrer',
          target: '_blank',
        };
      }
      if (
        bookingStatus !== 'CANCELED' &&
        (detail.summaryReview?.sumReviewer || 0) > 0
      ) {
        return {
          href: `/therapist/${detail?._id}/review?ref=BookingDetail`,
          rel: 'noreferrer',
          target: '_blank',
        };
      }
    }
    return {
      href: `/therapist/${detail?._id}`,
      rel: 'noreferrer',
      target: '_blank',
    };
  };

  if (loading) {
    return (
      <Box className={classes.therapistInfoWrapper}>
        <Flex align="center" gap={20}>
          <Skeleton
            circle
            h={120}
            sx={{ borderRadius: 100, flexShrink: 0 }}
            w={120}
          />
          <Flex direction="column" gap={12} w="100%">
            <Skeleton h={14} w="70%" />
            <Skeleton h={14} w="50% " />
            <Flex gap={10} mt={13} sx={{ '& > *': { flexGrow: 1 } }}>
              <Skeleton h={44} />
              <Skeleton h={44} />
            </Flex>
          </Flex>
        </Flex>
      </Box>
    );
  }

  return (
    <Box
      className={classes.therapistInfoWrapper}
      component={Link}
      {...getHref()}
    >
      <Flex gap={20}>
        <Image
          alt="Therapist avatar"
          className="therapist-avatar"
          height={120}
          src={
            detail?.profilePicture?.url ||
            detail?.avatar ||
            '/icons/icon-avatar-default-therapist.svg'
          }
          width={120}
        />
        <Flex direction="column" justify="center" w="100%">
          <Flex align="center" justify="space-between">
            <Flex className="therapist-content" direction="column">
              <Text
                className="therapist-name"
                color="black"
                lineClamp={1}
                size={18}
                weight="bold"
              >
                {detail?.nickName || detail?.fullName || '---'}
              </Text>
              <Text
                className="therapist-rating"
                color="marigoldActive"
                size={16}
                weight="bold"
              >
                <Image
                  alt="Star icon"
                  className="star-icon"
                  height={20}
                  src="/icons/icon-star.svg"
                  width={20}
                />
                <span>{rating.toFixed(1)}</span>
                <span>({summaryReview?.sumReviewer || 0}件)</span>
              </Text>
            </Flex>
            <IconChervonRight className="icon-right" />
          </Flex>
          {!mobileScreen && renderActionBtn()}
        </Flex>
      </Flex>
      {mobileScreen && renderActionBtn()}
      {renderReviewContent()}
    </Box>
  );
};

export default TherapistInfo;
