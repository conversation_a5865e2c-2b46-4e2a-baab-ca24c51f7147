import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  therapistInfoWrapper: {
    padding: '20px',
    backgroundColor: '#ffffff',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    '@media (max-width: 768px)': {
      padding: '15px 10px',
    },
    '& > *': {
      '@media (max-width: 768px)': {
        gap: 10,
      },
    },
    '.icon-right, .therapist-avatar': {
      flexShrink: 0,
    },
    '.therapist-avatar': {
      borderRadius: '50%',
      objectFit: 'cover',
      '@media (max-width: 768px)': {
        width: 55,
        height: 55,
      },
    },
    '.icon-right': {
      width: 16,
      height: 16,
      color: 'black',
      '@media (max-width: 768px)': {
        width: 12,
        height: 12,
      },
    },
    '.therapist-content': {
      '@media (max-width: 768px)': {
        gap: 0,
      },
      '.therapist-name': {
        '&:hover': {
          textDecoration: 'underline',
        },
        '@media (max-width: 768px)': {
          fontSize: 14,
        },
      },
      '.therapist-rating': {
        gap: 6,
        display: 'flex',
        alignItems: 'center',
        '@media (max-width: 768px)': {
          fontSize: 12,
          gap: 4,
          '.star-icon': {
            width: 16,
            height: 16,
          },
        },
        'span:last-of-type': {
          fontWeight: 'normal',
          color: theme.colors.darkLiver,
        },
      },
    },
  },
  actionBtnGroup: {
    width: '100%',
    gap: 10,
    marginTop: 8,
    '@media (max-width: 768px)': {
      marginTop: 12,
    },
    button: {
      padding: 8,
      flex: '1 1 50%',
      minHeight: 44,
      height: 'unset',
      fontSize: 16,
      alignItems: 'center',
      lineHeight: 1.2,
      '@media (max-width: 768px)': {
        minHeight: 36,
        fontSize: 12,
      },
      '.red-dot': {
        width: 10,
        height: 10,
        marginLeft: 4,
        flexShrink: 0,
      },
    },
  },
  therapistRating: {
    marginTop: 12,
    borderTop: '1px solid #dedede',
    paddingTop: 12,
  },
  reviewComment: {
    marginTop: 14,
    backgroundColor: '#f8f8f8',
    padding: 20,
    borderRadius: '4px',
    width: '100%',
    '@media (max-width: 768px)': {
      marginTop: 16,
      padding: 10,
    },
  },
  reviewBtn: {
    marginTop: 16,
    width: '100%',
    height: 44,
    fontSize: 16,
    '@media (max-width: 768px)': {
      marginTop: 12,
      height: 'auto',
      minHeight: 36,
      borderWidth: 2,
      fontSize: 12,
    },
  },
}));

export default useStyles;
