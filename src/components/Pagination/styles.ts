import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  paginationWrapper: {
    backgroundColor: '#e5f1f7',
    maxWidth: 750,
    width: '100%',
    margin: '0 auto',
    padding: 20,
    fontFamily: 'Helvetica Neue, sans-serif',
    '@media (max-width: 768px)': {
      backgroundColor: '#ffffff',
    },
    '@media (max-width: 480px)': {
      gap: 0,
      justifyContent: 'space-between',
    },

    '.mantine-Pagination-control': {
      border: '1px solid #c4d7e4',
      background: '#fff',
      fontSize: 25,
      fontWeight: 'bold',
      color: '#000000',
      padding: 0,
      borderRadius: 0,
      width: 40,
      height: 40,
      transform: 'none !important',
      fontFamily: 'Helvetica Neue, sans-serif',
      margin: 0,
      '@media (max-width: 768px)': {
        width: 32,
        height: 32,
        fontSize: 22,
      },
      svg: {
        width: 24,
        height: 24,
        '@media (max-width: 768px)': {
          width: 18,
          height: 18,
        },
      },
      '&.control-btn': {
        '@media (max-width: 768px)': {
          minWidth: 20,
          width: 20,
          height: 32,
        },
      },
      '&[data-active=true]': {
        border: '1px solid #c4d7e4',
        color: '#43749a',
        borderBottom: '3px solid #43749a',
        backgroundColor: 'white',
        pointerEvents: 'none',
        '&:not([data-disabled]):active': {
          backgroundColor: 'inherit',
        },
        '@media (max-width: 768px)': {
          border: '1px solid #c4d7e4',
          borderBottom: '3px solid #43749a',
        },
        '&:hover': {
          backgroundColor: '#f8f9fa',
        },
      },
      '&[data-disabled=true]': {
        pointerEvents: 'none',
        opacity: 0,
      },
      '&:hover': {
        backgroundColor: '#f8f9fa',
      },
    },
  },
};
