import type { PaginationRootProps, Sx } from '@mantine/core';
import { Flex, Pagination } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { merge } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';

import { sx } from './styles';

interface CustomPaginationProps extends PaginationRootProps {
  perPage?: number;
  page: number;
  customSx?: Sx;
}

const CustomPagination: React.FC<CustomPaginationProps> = ({
  total,
  page,
  customSx = {},
  ...rest
}) => {
  const mobileScreen = useMediaQuery('(max-width: 480px)');
  const router = useRouter();

  return (
    <Pagination.Root
      getItemProps={(p) => {
        return {
          component: Link,
          href: {
            pathname: router.pathname,
            query: { ...router.query, page: p },
          },
        };
      }}
      siblings={mobileScreen ? 0 : 1}
      total={total}
      value={page}
      {...rest}
    >
      <Flex
        gap={40}
        justify="center"
        sx={merge({}, sx.paginationWrapper, customSx)}
      >
        <Pagination.Previous
          className="control-btn"
          component={Link}
          href={{
            pathname: router.pathname,
            query: { ...router.query, page: page - 1 },
          }}
        />
        <Flex>
          <Pagination.Items />
        </Flex>
        <Pagination.Next
          className="control-btn"
          component={Link}
          href={{
            pathname: router.pathname,
            query: { ...router.query, page: page + 1 },
          }}
        />
      </Flex>
    </Pagination.Root>
  );
};

export default CustomPagination;
