import IconSuccess from '@icons/icon-success.svg';
import { Button, Flex, Group, Text, Title } from '@mantine/core';
import { useWindowEvent } from '@mantine/hooks';
import { useLogout, useUser } from 'hooks';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

import { styles, sx } from './styles';

const Step2: React.FC = () => {
  const { data: user } = useUser();
  const { logout: logoutFn } = useLogout();
  const router = useRouter();

  useEffect(() => {
    const handleRouteChange = () => {
      logoutFn(false, false);
    };
    router.events.on('routeChangeStart', handleRouteChange);
    router.beforePopState(() => {
      logoutFn(false, false);
      return true;
    });
    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [logoutFn, router]);
  useWindowEvent('beforeunload', () => {
    logoutFn(false, false);
  });

  return (
    <>
      <Flex align="center" direction="column" sx={sx.textWrapper}>
        <IconSuccess sx={sx.icon} />
        <Title order={4} sx={sx.title}>
          アカウントは完全に削除されました
        </Title>
        <Text>
          <b>{user?.name}</b>
          様のアカウントに関する全ての情報は完全に削除されました。
          <br />
          HOGUGU をご利用頂きまして誠にありがとうございました。
        </Text>
      </Flex>
      <Group position="center" sx={sx.groupBtn}>
        <Button
          onClick={() => {
            logoutFn(false);
          }}
          size="lg"
          styles={styles.nextBtn}
        >
          ホームへ戻る
        </Button>
      </Group>
    </>
  );
};

export default Step2;
