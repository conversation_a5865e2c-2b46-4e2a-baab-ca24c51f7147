import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  groupBtn: {
    marginTop: 66,
    '@media (max-width: 768px)': {
      marginTop: 40,
    },
  },

  textWrapper: {
    fontSize: 16,
    textAlign: 'center',
    svg: {
      width: 88,
      height: 88,
      marginBottom: 30,
      path: {
        fill: '#41850a !important',
      },
    },
    '@media (max-width: 768px)': {
      fontSize: 14,
      svg: {
        width: 56,
        height: 56,
        marginBottom: 24,
      },
      br: {
        display: 'none',
      },
    },
  },

  title: {
    color: '#41850a',
    fontSize: 30,
    marginBottom: 18,
    '@media (max-width: 768px)': {
      fontSize: 20,
      marginBottom: 12,
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  nextBtn: {
    root: {
      width: 300,
      '@media (max-width: 768px)': {
        width: 180,
      },
    },
  },
};
