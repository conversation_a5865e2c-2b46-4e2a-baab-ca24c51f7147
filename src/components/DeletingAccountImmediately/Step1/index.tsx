import { Box, Text, Title } from '@mantine/core';
import { OtpVerifyForm } from 'components/Auth';
import type { OtpVerifyFormValues } from 'components/Auth/OtpVerifyForm/schema';
import { useUser } from 'hooks';

import { sx } from './styles';

const Step1: React.FC<{
  isLoading: boolean;
  onSubmit: (values: OtpVerifyFormValues) => void;
  sendOTP: () => Promise<void>;
  backFn: () => void;
}> = ({ isLoading, onSubmit, backFn, sendOTP }) => {
  const { data: user } = useUser();

  return (
    <Box sx={sx.wrapper}>
      <Title color="blackOlive" order={3} sx={sx.title}>
        アカウント削除
      </Title>
      <OtpVerifyForm
        description={
          <>
            <Text sx={sx.redText}>
              この操作は元に戻すことができません。ご注意ください。
            </Text>
            <Text>
              <b>{user?.name}</b>{' '}
              様のアカウントを削除するため、SMSで受信した6桁の認証コードを入力してください。
            </Text>
          </>
        }
        initialValues={{ code: '' }}
        isLoading={isLoading}
        label="アカウントを完全に削除します。よろしいですか?"
        onCancel={backFn}
        onResendOtp={sendOTP}
        onSubmit={onSubmit}
        type="deleteAccount"
      />
    </Box>
  );
};

export default Step1;
