import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  title: {
    'h3&': {
      fontSize: 30,
      marginBottom: 30,
    },
    '@media (max-width: 768px)': {
      'h3&': {
        fontSize: 22,
        marginBottom: 32,
      },
    },
  },

  groupBtn: {
    marginTop: 66,
    '@media (max-width: 768px)': {
      marginTop: 60,
    },
  },

  redText: {
    marginBottom: 16,
    color: '#db1e0e',
    '@media (max-width: 768px)': {
      marginBottom: 12,
    },
  },

  wrapper: {
    '@media (max-width: 768px)': {
      '.mantine-TextInput-label': {
        fontSize: 16,
        marginBottom: 22,
      },
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  nextBtn: {
    root: {
      width: 300,
      height: 60,
      fontSize: 18,
      '@media (max-width: 768px)': {
        width: 180,
        height: 50,
        fontSize: 16,
      },
    },
  },
};
