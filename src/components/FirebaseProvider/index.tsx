import { onAuthStateChanged } from 'firebase/auth';
import { useUser } from 'hooks';
import type { ICustomer } from 'models/auth';
import { useEffect } from 'react';
import { auth } from 'utils/firebase';
import helpers from 'utils/helpers';
import getQueryClient from 'utils/queryClient';

import useAuthSignInAnonymously from './useAuthSignInAnonymously';
import useAuthSignInWithCustomToken from './useAuthSignInWithCustomToken';
import useAuthSignOut from './useAuthSignOut';

const FirebaseProvider = () => {
  const { data: currentUser, fetchStatus } = useUser();
  const { mutateAsync: signInFirebase } = useAuthSignInWithCustomToken(auth);
  const { mutateAsync: signOutFirebase } = useAuthSignOut(auth);
  const { mutateAsync: signInAnonymouslyFirebase } =
    useAuthSignInAnonymously(auth);

  const queryClient = getQueryClient();
  useEffect(() => {
    // Firebase auth state change listener
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (fetchStatus === 'fetching') return;

      if (!currentUser && firebaseUser) {
        await signOutFirebase();
        return;
      }

      if (currentUser && !currentUser?.isTesting && !firebaseUser) {
        const webCookie = helpers.getWebCookie();
        if (!webCookie.firebaseToken) return;
        const { user } = await signInFirebase(webCookie.firebaseToken);
        queryClient.setQueryData<ICustomer>(['currentUser'], {
          ...currentUser,
          firebaseUserId: user.uid,
        });
      }

      if (currentUser && currentUser?.isTesting && !firebaseUser) {
        const { user } = await signInAnonymouslyFirebase();
        queryClient.setQueryData<ICustomer>(['currentUser'], {
          ...currentUser,
          firebaseUserId: user.uid,
        });
      }
      if (currentUser && !currentUser?.firebaseUserId && firebaseUser?.uid) {
        queryClient.setQueryData<ICustomer>(['currentUser'], {
          ...currentUser,
          firebaseUserId: firebaseUser.uid,
        });
      }
    });
    return () => {
      unsubscribe();
    };
  }, [
    currentUser,
    fetchStatus,
    queryClient,
    signInAnonymouslyFirebase,
    signInFirebase,
    signOutFirebase,
  ]);

  return null;
};

export default FirebaseProvider;
