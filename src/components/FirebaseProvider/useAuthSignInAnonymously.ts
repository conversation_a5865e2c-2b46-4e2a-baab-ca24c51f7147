import type {
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import type { Auth, AuthError, UserCredential } from 'firebase/auth';
import { signInAnonymously } from 'firebase/auth';

export default function useAuthSignInWithCustomToken(
  auth: Auth,
  useMutationOptions?: UseMutationOptions<UserCredential, AuthError, void>,
): UseMutationResult<UserCredential, AuthError, void> {
  return useMutation<UserCredential, AuthError, void>(() => {
    return signInAnonymously(auth);
  }, useMutationOptions);
}
