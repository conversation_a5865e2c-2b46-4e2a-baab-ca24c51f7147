import IconChevronUp from '@icons/icon-chevron-up.svg';
import { Button } from '@mantine/core';
import { useWindowScroll } from '@mantine/hooks';

const ScrollTop = () => {
  const [scroll, scrollTo] = useWindowScroll();

  return (
    <Button
      onClick={() => scrollTo({ y: 0 })}
      p={0}
      styles={{
        root: {
          height: 60,
          width: 60,
          borderRadius: '100%',
          transform: `scale(${scroll.y > 200 ? 1 : 0})`,
          pointerEvents: scroll.y > 200 ? 'auto' : 'none',
          position: 'fixed',
          bottom: 50,
          right: 50,
          zIndex: 1,
          '@media (max-width: 768px)': {
            bottom: 16,
            right: 16,
          },
        },
        label: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 6,
          fontWeight: 'bold',
          fontSize: 12,
          svg: {
            width: 24,
            height: 12,
          },
        },
      }}
    >
      <IconChevronUp />
      TOP
    </Button>
  );
};

export default ScrollTop;
