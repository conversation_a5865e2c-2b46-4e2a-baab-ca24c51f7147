import IconDropdown from '@icons/icon-dropdown.svg';
import { Box, Button, Group, Menu, NavLink, Text } from '@mantine/core';
import { useLogout, useUser } from 'hooks';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  GUEST_NAVIGATOR,
  HEADER_NAVIGATOR,
  LOGGED_USER_NAVIGATOR,
} from 'utils/constants';

import { styles, sx } from './styles';

const NavBar: React.FC<{
  sumNoti?: number;
  sumMess?: number;
}> = ({ sumNoti, sumMess }) => {
  const router = useRouter();
  const { data: user } = useUser();
  const { logout: logoutFn } = useLogout();

  const isLogged = user;

  const renderActionMenu = () => {
    if (isLogged) {
      return (
        <Menu
          position="top-end"
          styles={styles.headerDropdown}
          transitionProps={{ transition: 'pop-top-right' }}
          withinPortal
        >
          <Menu.Target>
            <Button sx={sx.btnDropdown}>
              <Text sx={sx.username}>
                {user.name || 'ゲスト'}
                さん
              </Text>
              <IconDropdown height={12} width={12} />
              {(!!sumMess || !!sumNoti) && <Text className="red-dot" />}
            </Button>
          </Menu.Target>
          <Menu.Dropdown>
            {LOGGED_USER_NAVIGATOR.map((nav) => (
              <Menu.Item
                component={nav.component || Link}
                href={nav.href}
                icon={nav.icon}
                key={nav.href}
                onClick={() => {
                  if (nav.href !== '/logout') return;
                  logoutFn();
                }}
                rightSection={
                  (sumNoti && nav.href === '/my-page') ||
                  (sumMess && nav.href === '/my-page/booking-history') ? (
                    <Text className="red-dot" />
                  ) : null
                }
              >
                {nav.label}
              </Menu.Item>
            ))}
          </Menu.Dropdown>
        </Menu>
      );
    }
    return (
      <Box display="flex" h="100%">
        {GUEST_NAVIGATOR.map((nav) => (
          <Button
            color={nav.color}
            component={Link}
            href={nav.href}
            key={nav.href}
            leftIcon={nav.icon}
            sx={sx.authBtn}
          >
            {nav.label}
          </Button>
        ))}
      </Box>
    );
  };

  return (
    <>
      <Group noWrap spacing={30} sx={sx.navBarWrapper}>
        {HEADER_NAVIGATOR.map((nav) => (
          <NavLink
            active={nav.href === router.pathname}
            component={Link}
            href={nav.href}
            key={nav.href}
            label={nav.label}
            rightSection={nav.icon}
            sx={sx.navLink}
            {...(nav.newTab
              ? {
                  target: '_blank',
                  rel: 'noreferrer',
                }
              : {})}
          />
        ))}
      </Group>
      <Box sx={sx.authSection}>{renderActionMenu()}</Box>
    </>
  );
};

export default NavBar;
