import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  headerWrapper: (theme) => ({
    height: theme.other.headerHeight,
    paddingLeft: 40,
    boxShadow: '0px 2px 4px #00000029',
    position: 'fixed',
    width: '100%',
    zIndex: 100,
    backgroundColor: '#ffffff',
    top: '47px',
    '.logo a > svg': {
      '&:first-child': {
        width: 182,
      },
      '&:nth-child(2)': {
        marginLeft: 8,
        '@media (max-width: 768px)': {
          display: 'none',
        },
      },
    },
    '@media(max-width: 1280px)': {
      paddingRight: 40,
      display: 'flex',
      justifyContent: 'space-between !important',
      top: 0,
    },
    '@media (max-width: 768px)': {
      padding: '0 20px',
      height: theme.other.headerHeightMobile,
      '.logo a > svg:first-child': {
        width: 110,
      },
    },
  }),
  headerLayout: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    width: '100%',
    position: 'relative',
    '@media (max-width: 1280px)': {
      justifyContent: 'space-between',
    },
    '@media (max-width: 768px)': {
      justifyContent: 'space-between',
    },
  },
  logoSection: {
    flex: '0 0 auto',
  },
  navBarWrapper: {
    height: '100%',
    justifyContent: 'center',
    flex: 1,
    display: 'flex',
    '@media (max-width: 1280px)': {
      display: 'none',
    },
    '@media (min-width: 1281px) and (max-width: 1375px)': {
      gap: 20,
    },
  },
  authSection: {
    flex: '0 0 auto',
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    '@media (max-width: 1280px)': {
      display: 'none',
    },
  },
  navLink: {
    position: 'relative',
    height: '100%',
    '.mantine-NavLink-label': {
      gap: 8,
      display: 'flex',
      alignItems: 'center',
      whiteSpace: 'nowrap',
    },
    '.mantine-NavLink-rightSection': {
      marginLeft: 8,
    },
    '&:after': {
      content: '" "',
      position: 'absolute',
      bottom: 0,
      height: 5,
      width: '100%',
      backgroundColor: '#43749A',
      transform: 'scale(0, 1)',
      transformOrigin: 'right top',
      transition: 'transform 300ms ease-in-out, opacity 300ms ease-in-out',
      opacity: 0,
    },
    '&:hover:after': {
      transform: 'scale(1, 1)',
      transformOrigin: 'left top',
      opacity: 1,
    },
  },
  authBtn: {
    height: '100%',
    minWidth: 100,
    padding: 0,
    borderRadius: 0,
    fontSize: 12,
    boxShadow: 'none !important',
    '.mantine-Button-inner': {
      flexDirection: 'column',
    },
    '.mantine-Button-label': {
      height: 'auto',
    },
    '.mantine-Button-leftIcon': {
      margin: '0 0 4px',
    },
  },
  btnDropdown: (theme: MantineTheme) => ({
    backgroundColor: 'white',
    color: theme.colors.blackOlive,
    border: 'solid 1.5px #ddd',
    borderRadius: 25,
    height: 50,
    marginRight: 40,
    '&:hover, &[data-expanded="true"]': {
      backgroundColor: theme.colors.ghostWhite,
    },
    '.mantine-Button-label': {
      gap: 10,
    },
    svg: {
      fill: theme.colors.queenBlue?.[6],
    },
  }),
  username: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: 84,
  },
  anniversaryText: (theme: MantineTheme) => ({
    color: theme.colors.blackOlive,
    fontSize: 12,
    fontWeight: 400,
    a: {
      fontWeight: 700,
      color: '#EF282D',
      textDecoration: 'none',
    },
    b: {
      fontWeight: 700,
      color: '#00ACEF',
    },
    '@media (max-width: 768px)': {
      display: 'none',
    },
  }),
  anniversaryTextMobile: (theme: MantineTheme) => ({
    '@media (min-width: 769px)': {
      display: 'none',
    },
    color: '#EF282D',
    fontSize: 11,
    fontWeight: 700,
    display: 'flex',
    alignItems: 'center',
    position: 'absolute',
    left: 35,
    bottom: 32,
    span: {
      color: theme.colors.blackOlive,
      fontSize: 8,
    },
    'svg:last-of-type': {
      marginBottom: 3,
      marginLeft: 2,
      marginRight: 2,
    },
  }),
  subMenuWrapper: {
    backgroundColor: '#225277',
    position: 'fixed',
    width: '100%',
    height: 47,
    zIndex: 101,
    top: 0,
    padding: '12px 50px',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    '@media (max-width: 1280px)': {
      display: 'none',
    },
  },
  subMenuGroup: {
    gap: 30,
  },
  subMenuLink: {
    color: '#FFFFFF',
    fontFamily: 'YuGothic',
    fontWeight: 700,
    fontSize: 14,
    lineHeight: '1.5em',
    letterSpacing: '3%',
    padding: 0,
    '&:hover': {
      backgroundColor: 'transparent',
      color: '#FFFFFF',
    },
    '.mantine-NavLink-label': {
      color: '#FFFFFF',
      display: 'flex',
      alignItems: 'center',
      gap: 8,
      whiteSpace: 'nowrap',
    },
    '.mantine-NavLink-rightSection': {
      marginLeft: 5,
      color: '#FFFFFF',
      svg: {
        fill: '#FFFFFF',
        width: 10.1,
        height: 10.1,
      },
    },
    '&[data-href*="contact"]': {
      width: 112,
      '.mantine-NavLink-label': {
        justifyContent: 'space-between',
      },
    },
  },
};

export const styles: Record<string, any> = {
  burger: (theme: MantineTheme) => ({
    root: {
      '&.has-red-dot': {
        position: 'relative',
        '&:after': {
          content: '""',
          display: 'block',
          position: 'absolute',
          right: 0,
          top: 4,
          width: 10,
          height: 10,
          backgroundColor: theme.colors.red,
          borderRadius: '50%',
          animation: 'redDotPulse 3s infinite',
        },
      },
    },
  }),
  headerDropdown: (theme: MantineTheme) => ({
    item: {
      borderRadius: 0,
      padding: '15px 20px',
      height: 55,
      fontWeight: 'bold',
      position: 'relative',
      '&:after': {
        content: '""',
        display: 'block',
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 0,
        height: 1,
        backgroundColor: theme.colors.platinum,
        width: 'calc(100% - 22px)',
        marginLeft: 'auto',
        marginRight: 'auto',
      },
      '&:last-child': {
        '&:after': {
          display: 'none',
        },
      },
      svg: {
        maxWidth: 15,
      },
    },
    dropdown: {
      color: 'black',
      maxWidth: 250,
      width: '100% !important',
      borderRadius: 6,
      fontSize: 14,
      boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
      marginTop: 30,
    },
  }),
};
