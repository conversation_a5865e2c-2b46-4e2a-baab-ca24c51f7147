import { Box, Group, NavLink } from '@mantine/core';
import Link from 'next/link';
import { SUBMENU_NAVIGATOR } from 'utils/constants';

import { sx } from './styles';

const SubMenu: React.FC = () => {
  return (
    <Box sx={sx.subMenuWrapper}>
      <Group noWrap spacing={30} sx={sx.subMenuGroup}>
        {SUBMENU_NAVIGATOR.map((nav) => (
          <NavLink
            component={Link}
            href={nav.href}
            key={nav.href}
            label={nav.label}
            rightSection={nav.icon}
            sx={sx.subMenuLink}
            {...(nav.newTab
              ? {
                  target: '_blank',
                  rel: 'noreferrer',
                }
              : {})}
          />
        ))}
      </Group>
    </Box>
  );
};

export default SubMenu;
