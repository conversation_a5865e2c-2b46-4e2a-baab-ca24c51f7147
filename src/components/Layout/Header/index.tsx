import AnniversaryLogo from '@icons/anniversary.svg';
import IconCrown from '@icons/icon-crown.svg';
import HoguguLogo from '@icons/logo.svg';
import { Box, Burger, Text } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useGlobalState, useTotalNotification } from 'hooks';
import Link from 'next/link';

import NavBar from './NavBar';
import { styles, sx } from './styles';
import SubMenu from './SubMenu';

const LayoutHeader: React.FC = () => {
  const mobileScreen = useMediaQuery('(max-width: 1280px)', true, {
    getInitialValueInEffect: false,
  });
  const { firebaseUser, openedDrawer, setOpenDrawer } = useGlobalState();
  // Kept refreshing notifiation after 15s
  const { data: summaryNotification } = useTotalNotification();

  return (
    <>
      <SubMenu />
      <Box component="header" sx={sx.headerWrapper}>
        <Box sx={sx.headerLayout}>
          <Box className="logo" sx={sx.logoSection}>
            {process.env.NEXT_PUBLIC_ENABLE_ANNIVERSARY ? (
              <>
                <Link aria-label="Homepage" href="https://hogugu.com">
                  <HoguguLogo />
                  <AnniversaryLogo />
                </Link>
                <Text
                  align="center"
                  color="blackOlive"
                  mt={5}
                  sx={sx.anniversaryText}
                >
                  「出張リラクゼーション」
                  <Link
                    aria-label="利用者数No.1"
                    href={`${process.env.NEXT_PUBLIC_LP_URL}/act-on-specified-commercial-transaction.html`}
                    target="_blank"
                  >
                    利用者数No.1
                  </Link>
                  ※
                </Text>
                <Text align="center" color="blackOlive" sx={sx.anniversaryText}>
                  予約数<b>198,092</b>件 口コミ数<b>144,617</b>件
                </Text>
                <Text sx={sx.anniversaryTextMobile}>
                  利用者数
                  <IconCrown />
                  No.1<span>※</span>
                </Text>
              </>
            ) : (
              <Link aria-label="Homepage" href="https://hogugu.com">
                <HoguguLogo />
              </Link>
            )}
          </Box>
          {mobileScreen ? (
            <Burger
              className={
                firebaseUser?.bookingsHaveNewMessages ||
                summaryNotification?.total
                  ? 'has-red-dot'
                  : ''
              }
              onClick={() => setOpenDrawer(true)}
              opened={openedDrawer}
              styles={styles.burger}
            />
          ) : (
            <NavBar
              sumMess={firebaseUser?.bookingsHaveNewMessages || 0}
              sumNoti={summaryNotification?.total}
            />
          )}
        </Box>
      </Box>
    </>
  );
};

export default LayoutHeader;
