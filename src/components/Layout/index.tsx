import { Box, LoadingOverlay } from '@mantine/core';
import { CallNotificationModal } from 'components/Modals';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { FOOTER_PAGES } from 'utils/constants';

import { sx } from './styles';

const Header = dynamic(() => import('./Header'));
const Drawer = dynamic(() => import('./Drawer'), { ssr: false });
const ScrollTop = dynamic(() => import('components/ScrollTop'), { ssr: false });
const Footer = dynamic(() => import('./Footer'));
const SimpleFooter = dynamic(() => import('./SimpleFooter'));

interface LayoutProps {
  children: React.ReactNode;
  error?: boolean;
}

const Layout = ({ children, error }: LayoutProps) => {
  const router = useRouter();

  const [preloadPage, setPreloadPage] = useState(true);

  useEffect(() => {
    setPreloadPage(false);
  }, []);

  return (
    <Box sx={{ position: 'relative' }}>
      <Header />
      <Drawer />
      <ScrollTop />
      <Box
        data-simple={FOOTER_PAGES.simple?.includes(router.pathname)}
        sx={sx.mainWrapper}
      >
        {children}
        <CallNotificationModal />
      </Box>
      {FOOTER_PAGES.simple?.includes(router.pathname) ? (
        <SimpleFooter />
      ) : (
        <Footer error={error} />
      )}
      <LoadingOverlay
        exitTransitionDuration={1500}
        loaderProps={{ size: 'xl' }}
        transitionDuration={500}
        visible={preloadPage}
        zIndex={100}
      />
    </Box>
  );
};

export default Layout;
