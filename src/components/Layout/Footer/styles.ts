import type { MantineTheme } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/core';

const useStyles = createStyles((theme: MantineTheme) => {
  return {
    footerWrapper: {
      backgroundColor: theme.colors.ghostWhite,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      padding: '44px 0 0',
      height: theme.other.footerHeight,
      position: 'relative',
      overflowX: 'hidden',
      '&:before': {
        content: '""',
        display: 'block',
        position: 'absolute',
        top: 0,
        height: 2,
        left: 0,
        right: 0,
        width: 'calc(100% - 40px)',
        maxWidth: '1140px',
        backgroundColor: theme.colors.queenBlue[6],
        marginLeft: 'auto',
        marginRight: 'auto',
        [`@media (max-width: ${theme.breakpoints.sm})`]: {
          height: 0,
        },
      },
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        padding: '0',
        height: 'auto',
      },
    },

    logo: {
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        order: 2,
      },
    },

    searchArea: {
      width: 1091,
      // height: 70,
      fontWeight: 'bold',
      backgroundColor: theme.white,
      borderRadius: 6,
      margin: '28px 0 35px',
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        flexDirection: 'column',
        height: 'auto',
        maxWidth: 295,
        margin: '40px 0 20px',
      },
    },
    searchAreaTitle: {
      minWidth: 140,
      borderRadius: '6px 0 0 6px',
      alignSelf: 'stretch',
      backgroundColor: theme.colors.queenBlue[6],
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        padding: '8px 0',
        borderRadius: '6px 6px 0 0',
      },
    },
    citySearchGroup: {
      width: '100%',
      gap: '8px',
      padding: '12px',
      alignItems: 'flex-start',
      position: 'relative',
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        padding: '18px 11px',
        borderRadius: '6px 6px 0 0',
        alignItems: 'stretch',
      },
      '&:after': {
        right: 0,
        top: 16,
        height: 72,
        width: 1,
        backgroundColor: theme.colors.quickSilver,
        content: '" "',
        position: 'absolute',
        [`@media (max-width: ${theme.breakpoints.sm})`]: {
          width: 0,
        },
      },
    },
    citySearchRow: {
      width: '100%',
      flexWrap: 'nowrap',
      [`.${getStylesRef('navLink')}`]: {
        flex: '0 1 55px',
        [`@media (max-width: ${theme.breakpoints.sm})`]: {
          flex: '0 1 20%',
        },
        '&:after': {
          right: 0,
          height: 13,
          width: 1,
        },
        '&:last-of-type:after': {
          display: 'none',
        },
      },
    },
    currentLocationSearch: {
      minWidth: 157,
      textAlign: 'center',
      position: 'relative',
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        minWidth: 'unset',
        marginBottom: 16,
      },
      '&:after': {
        content: '" "',
        height: 1,
        position: 'absolute',
        left: -11,
        bottom: -3,
        backgroundColor: theme.black,
        [`@media (max-width: ${theme.breakpoints.sm})`]: {
          width: '120%',
        },
      },
    },

    groupFooterNavigator: {
      maxWidth: 860,
      gap: 6,
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        backgroundColor: theme.white,
        padding: '36px 20px',
        borderRadius: 6,
        marginBottom: '30px',
        maxWidth: 295,
      },
    },

    footerNavigator: {
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      flexWrap: 'wrap',
      gap: '6px 60px',
      [`.${getStylesRef('navLink')}`]: {
        color: theme.colors.nickel,
        '&[data-active], &[data-active]:hover, &:hover': {
          color: theme.colors.queenBlue?.[6] || theme.colors.blue[6],
        },
        '&:not(.btnLogout):last-of-type:after, &:nth-of-type(5n):after': {
          width: 0,
        },
        [theme.fn.smallerThan('sm')]: {
          width: '100%',
          textAlign: 'left',
          color: theme.black,
          paddingBottom: 4,
          borderBottom: `1px solid #e3e3e3`,
          '&:after': {
            width: 'unset !important',
            right: 5,
            height: 'unset',
            backgroundColor: 'transparent',
            content: 'url("/icons/icon-chevron-right.svg")',
          },
        },
      },
    },

    navLink: {
      ref: getStylesRef('navLink'),
      position: 'relative',
      textAlign: 'center',
      color: theme.black[6],
      '&:after': {
        content: '" "',
        position: 'absolute',
        right: -30,
        height: 13,
        width: 1,
        backgroundColor: theme.colors.quickSilver?.[6] || theme.colors.gray[6],
        pointerEvents: 'none',
      },
      '&.btnLogout:after': {
        [theme.fn.largerThan('sm')]: {
          content: '" "',
          position: 'absolute',
          left: -30,
          height: 13,
          width: 1,
          backgroundColor:
            theme.colors.quickSilver?.[6] || theme.colors.gray[6],
          pointerEvents: 'none',
        },
      },
    },

    downloadSection: {
      gap: 30,
      padding: '30px 0',
      a: {
        position: 'relative',
        '&:first-of-type': {
          width: 218,
          height: 71,
        },
        '&:last-of-type': {
          width: 241,
          height: 71,
        },
      },
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        order: 3,
        gap: 8,
        padding: '24px 0 30px',
        'a:first-of-type': {
          width: 150,
          height: 49,
        },
        'a:last-of-type': {
          width: 166,
          height: 49,
        },
      },
    },

    socialMedia: {
      gap: 30,
      a: {
        position: 'relative',
        width: 36,
        height: 36,
      },
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        order: 4,
        gap: 26,
        a: {
          width: 40,
          height: 40,
        },
      },
    },
  };
});

export default useStyles;
