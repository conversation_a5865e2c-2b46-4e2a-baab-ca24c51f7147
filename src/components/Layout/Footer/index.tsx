import Logo from '@icons/logo.svg';
import { Box, Flex, Group, NavLink, Stack, Text } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { useLogout, useMutate, useUser } from 'hooks';
import type {
  GetAreaCoordinatesPayload,
  IPrefectureItem,
} from 'models/resource';
import { resourceQuery } from 'models/resource';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import {
  FOOTER_NAVIGATOR,
  LOGGED_USER_NAVIGATOR,
  SEARCH_AREA,
  SOCIAL_CONTACTS,
} from 'utils/constants';
import { eventLog, requestLocation } from 'utils/helpers';
import notification from 'utils/notification';

import useStyles from './styles';

const LayoutFooter = ({ error }: { error?: boolean }) => {
  const { classes } = useStyles();
  const { data: currentUserData } = useUser();
  const { logout: logoutFn } = useLogout();
  const mobileScreen = useMediaQuery('(max-width: 768px)');
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const isLogin = currentUserData;

  const { mutateAsync: getAreaCoordinatesFn } = useMutate<
    GetAreaCoordinatesPayload,
    IPrefectureItem
  >(resourceQuery.getAreaCoordinates);

  const getChunkAreas = (num: number) => {
    const chunks = [];
    for (let i = 0; i <= SEARCH_AREA.length; i += num) {
      chunks.push(SEARCH_AREA.slice(i, i + num));
    }
    return chunks;
  };

  const cityGroup = useMemo(() => {
    if (mobileScreen) {
      return getChunkAreas(5);
    }
    return getChunkAreas(14);
  }, [mobileScreen]);

  const handleOnSearchingLocation = () => {
    setIsLoading(true);
    requestLocation({
      successCallback: (position) => {
        getAreaCoordinatesFn(
          {
            lat:
              typeof router.query.lat === 'string'
                ? Number(router.query.lat)
                : position.coords.latitude,
            lng:
              typeof router.query.lng === 'string'
                ? Number(router.query.lng)
                : position.coords.longitude,
          },
          {
            onSuccess: (data) => {
              setIsLoading(false);
              if (data) {
                eventLog('view_address', {
                  address_type: 'manual',
                  reference_section: 'google_address_with_confirmation',
                });
                const prefName = data.nameEn;
                const cityName =
                  data.child && data.child.nameEn ? data.child.nameEn : '';
                const townName =
                  data.child && data.child.child && data.child.child.nameEn
                    ? data.child?.child.nameEn
                    : '';
                router.push(
                  `/${prefName}${cityName ? `/${cityName}` : ''}${
                    cityName && townName ? `-${townName}` : ''
                  }${!cityName && townName ? `/${townName}` : ''}`,
                );
              } else {
                notification.show({
                  message: '位置情報が取得できません。',
                });
              }
            },
            onError: () => {
              setIsLoading(false);
            },
          },
        );
      },
      errorCallback: () => {
        setIsLoading(false);
        notification.show({
          message: '位置情報が取得できません。',
        });
      },
    });
  };

  return (
    <footer className={classes.footerWrapper}>
      {!error && mobileScreen && (
        <Box
          component={Link}
          href="https://hogugu-customer.onelink.me/NAXz/r151w3tn"
          sx={{ position: 'relative', width: '100%', aspectRatio: '1' }}
        >
          <Image
            alt="Footer banner"
            fill
            quality={100}
            sizes="100vw"
            src="/images/footer-banner.png"
          />
        </Box>
      )}
      <Link
        aria-label="Homepage"
        className={classes.logo}
        href="https://hogugu.com"
      >
        <Logo width="200px" />
      </Link>
      <Group className={classes.searchArea} noWrap spacing={0}>
        <Box className={classes.searchAreaTitle}>
          <Text color="white">エリア検索</Text>
        </Box>
        <Stack className={classes.citySearchGroup}>
          {cityGroup.map((group, index) => (
            <Group className={classes.citySearchRow} key={index} spacing={0}>
              {group.map((nav) => (
                <NavLink
                  className={classes.navLink}
                  component={Link}
                  href={nav.href}
                  key={nav.href}
                  label={nav.label}
                  onClick={() => {
                    eventLog('view_address', {
                      address_type: 'manual',
                      reference_section: 'area_address',
                    });
                  }}
                />
              ))}
            </Group>
          ))}
        </Stack>
        <Box className={classes.currentLocationSearch}>
          <NavLink
            component="div"
            label={isLoading ? '取得中…' : '現在地から探す'}
            onClick={handleOnSearchingLocation}
            sx={{ textAlign: 'center' }}
          />
        </Box>
      </Group>
      <Group
        align="center"
        className={classes.groupFooterNavigator}
        position="center"
      >
        <Flex className={classes.footerNavigator}>
          {isLogin &&
            LOGGED_USER_NAVIGATOR.map((nav) => (
              <NavLink
                className={`${classes.navLink} ${nav.sx}`}
                component={nav.component || Link}
                href={nav.href}
                key={nav.href}
                label={nav.label}
                onClick={() => {
                  if (nav.href !== '/logout') return;
                  logoutFn();
                }}
              />
            ))}
          {/* REMOVE FOLLOW DESIGN  */}
          {/* {!isLogin &&
            GUEST_NAVIGATOR.map((nav) => (
              <NavLink
                className={`${classes.navLink} ${nav.sx}`}
                component={Link}
                href={nav.href}
                key={nav.href}
                label={nav.label}
              />
            ))} */}
        </Flex>
        <Group className={classes.footerNavigator}>
          {FOOTER_NAVIGATOR.map((nav) => (
            <NavLink
              className={classes.navLink}
              component={Link}
              href={nav.href}
              key={nav.href}
              label={nav.label}
              {...(nav.newTab
                ? {
                    target: '_blank',
                    rel: 'noreferrer',
                  }
                : {})}
            />
          ))}
        </Group>
      </Group>
      <Group
        align="center"
        className={classes.downloadSection}
        position="center"
      >
        <a
          href="https://apps.apple.com/JP/app/id1467856161"
          rel="noreferrer"
          target="_blank"
        >
          <Image
            alt="Apple store"
            fill
            sizes="10vw"
            src="/icons/icon-app-store.svg"
          />
        </a>
        <a
          href="https://play.google.com/store/apps/details?id=com.hogugu.android.customer.booking"
          rel="noreferrer"
          target="_blank"
        >
          <Image
            alt="Google play"
            fill
            sizes="10vw"
            src="/icons/icon-google-play.svg"
          />
        </a>
      </Group>
      <Group className={classes.socialMedia}>
        {Object.keys(SOCIAL_CONTACTS).map((key) => (
          <Link
            href={SOCIAL_CONTACTS[key]?.href || '/'}
            key={key}
            rel="noreferrer"
            target="_blank"
          >
            <Image
              alt={key}
              fill
              sizes="5vw"
              src={SOCIAL_CONTACTS[key]?.icon || ''}
            />
          </Link>
        ))}
      </Group>
      <Text
        color="#3C3C3C"
        mt={30}
        sx={(theme) => ({
          fontSize: 12,
          textAlign: 'center',
          [`@media (max-width: ${theme.breakpoints.sm})`]: {
            order: 5,
            marginTop: 22,
            fontSize: 10,
          },
        })}
      >
        Copyright ©︎ HOGUGU Technologies, Inc. All Rights Reserved.
      </Text>
    </footer>
  );
};

export default LayoutFooter;
