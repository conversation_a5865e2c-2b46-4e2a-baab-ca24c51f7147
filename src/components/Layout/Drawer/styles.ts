import type { CSSObject, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  navLinkWrapper: {
    marginTop: 18,
  },
  navLink: {
    padding: '20px 10px',
    borderBottom: '1px solid #e3e3e3',
    '&:first-of-type': {
      borderTop: '1px solid #e3e3e3',
    },
    '&[type="button"]': {
      marginTop: 0,
      borderTop: 'none',
      width: '100%',
    },
    '.red-dot': {
      marginRight: 16,
    },
  },
  subNavLink: {
    padding: '20px 10px',
    borderBottom: '1px solid #e3e3e3',
    '&:nth-last-child(4)': {
      marginTop: 30,
    },
    '&[type="button"]': {
      marginTop: 0,
      borderTop: 'none',
      width: '100%',
    },
    '.red-dot': {
      marginRight: 16,
    },
  },
  mediaLink: {
    svg: {
      color: '#327eb9',
    },
  },
  username: {
    borderBottom: 'none',
    borderTop: 'none !important',
    paddingLeft: 0,
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  drawerStyles: {
    content: {
      overflow: 'unset !important',
      '@media (max-width: 768px)': {
        flex: '0 0 calc(100% - 45px)',
      },
    },
    close: {
      width: 45,
      height: 45,
      backgroundColor: '#43749A',
      borderRadius: 0,
      position: 'absolute',
      top: 0,
      left: -45,
      '&:hover': {
        backgroundColor: '#43749A',
      },
      svg: {
        color: '#ffffff',
        width: 32,
        height: 32,
      },
    },
    title: {
      paddingTop: 10,
      paddingLeft: 35,
    },
    header: {
      padding: 0,
    },
    body: {
      padding: '10px 35px 35px !important',
      overflow: 'auto',
      maxHeight: '100%',
    },
  },
};
