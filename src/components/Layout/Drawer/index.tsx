import IconChevronRight from '@icons/icon-chevron-right.svg';
import { Box, Drawer, NavLink, Text } from '@mantine/core';
import {
  useGlobalState,
  useLogout,
  useTotalNotification,
  useUser,
} from 'hooks';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  GUEST_NAVIGATOR,
  HEADER_NAVIGATOR,
  LOGGED_USER_NAVIGATOR,
  SUBMENU_NAVIGATOR,
} from 'utils/constants';

import { styles, sx } from './styles';

const LayoutDrawer = () => {
  const router = useRouter();
  const { data: user } = useUser();
  const { firebaseUser, openedDrawer, setOpenDrawer } = useGlobalState();
  const { logout: logoutFn } = useLogout();
  const { data: summaryNotification } = useTotalNotification();

  const isLogged = user;

  return (
    <Drawer
      onClose={() => setOpenDrawer(false)}
      opened={openedDrawer}
      position="right"
      size="330px"
      styles={styles.drawerStyles}
    >
      <Image alt="Hogugu logo" height={30} src="/icons/logo.svg" width={122} />
      <Box sx={sx.navLinkWrapper}>
        <NavLink
          component={Text}
          label={isLogged ? `${user.name} さん` : 'ゲスト さん'}
          sx={[sx.navLink, sx.username]}
        />
        {isLogged &&
          LOGGED_USER_NAVIGATOR.map((nav) => (
            <NavLink
              active={nav.href === router.pathname}
              component={nav.component || Link}
              href={nav.href}
              key={nav.href}
              label={nav.label}
              onClick={() => {
                setOpenDrawer(false);
                if (nav.href !== '/logout') return;
                logoutFn();
              }}
              rightSection={
                <>
                  {(summaryNotification?.total && nav.href === '/my-page') ||
                  (firebaseUser?.bookingsHaveNewMessages &&
                    nav.href === '/my-page/booking-history') ? (
                    <Text className="red-dot" />
                  ) : null}
                  <IconChevronRight />
                </>
              }
              sx={sx.navLink}
            />
          ))}
        {!isLogged &&
          GUEST_NAVIGATOR.map((nav) => (
            <NavLink
              active={nav.href === router.pathname}
              component={Link}
              href={nav.href}
              key={nav.href}
              label={nav.label}
              onClick={() => {
                setOpenDrawer(false);
              }}
              rightSection={<IconChevronRight />}
              sx={sx.navLink}
            />
          ))}
        {HEADER_NAVIGATOR.map((nav) => (
          <NavLink
            active={nav.href === router.pathname}
            component={Link}
            href={nav.href}
            key={nav.href}
            label={nav.label}
            onClick={() => {
              setOpenDrawer(false);
            }}
            rightSection={nav.icon || <IconChevronRight />}
            sx={[sx.navLink, nav?.sx ? sx[nav.sx] : {}]}
            {...(nav.newTab
              ? {
                  target: '_blank',
                  rel: 'noreferrer',
                }
              : {})}
          />
        ))}
        <Box sx={sx.submenuSection}>
          {SUBMENU_NAVIGATOR.map((nav) => (
            <NavLink
              active={nav.href === router.pathname}
              component={Link}
              href={nav.href}
              key={nav.href}
              label={nav.label}
              onClick={() => {
                setOpenDrawer(false);
              }}
              rightSection={nav.icon || <IconChevronRight />}
              sx={[sx.subNavLink, nav?.sx ? sx[nav.sx] : {}]}
              {...(nav.newTab
                ? {
                    target: '_blank',
                    rel: 'noreferrer',
                  }
                : {})}
            />
          ))}
        </Box>
      </Box>
    </Drawer>
  );
};

export default LayoutDrawer;
