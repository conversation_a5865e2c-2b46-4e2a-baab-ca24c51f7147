import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => {
  return {
    footerWrapper: {
      backgroundColor: theme.colors.ghostWhite,
      textAlign: 'center',
      padding: '49px 0 40px',
      position: 'relative',
      '&:before': {
        content: '""',
        display: 'block',
        position: 'absolute',
        top: 0,
        height: 2,
        left: 0,
        right: 0,
        width: 'calc(100% - 40px)',
        maxWidth: '1140px',
        backgroundColor: theme.colors.queenBlue[6],
        marginLeft: 'auto',
        marginRight: 'auto',
        [`@media (max-width: ${theme.breakpoints.sm})`]: {
          height: 1,
          width: '100%',
          backgroundColor: '#d1d1d1',
        },
      },
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        padding: '24px 0',
      },
    },

    logo: {
      maxHeight: 50,
      height: '100%',
      display: 'inline-flex',
      [`@media (max-width: ${theme.breakpoints.sm})`]: {
        maxHeight: 44,
      },
    },
    copyright: {
      fontSize: 12,
      marginTop: 24,
    },
  };
});

export default useStyles;
