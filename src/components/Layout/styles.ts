import type { MantineTheme, Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  mainWrapper: (theme: MantineTheme) => ({
    paddingTop: theme.other.headerHeight,
    minHeight: `calc(100vh - ${theme.other.footerHeight})`,
    backgroundColor: '#f5f9fb',
    overflowX: 'hidden',
    '@media (max-width: 768px)': {
      paddingTop: theme.other.headerHeightMobile,
      minHeight: `unset`,
    },
    '&[data-simple=true]': {
      minHeight: `calc(100vh - ${theme.other.simpleFooterHeight})`,
      '@media (max-width: 768px)': {
        minHeight: `calc(100vh - ${theme.other.simpleFooterHeightMobile})`,
      },
    },
  }),
};
