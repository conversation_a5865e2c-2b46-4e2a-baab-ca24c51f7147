import IconTherapistBusy from '@icons/icon-therapist-busy.svg';
import {
  Badge,
  Flex,
  Group,
  Skeleton,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import type { ITherapistItem } from 'models/therapist';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import React, { useState } from 'react';

import StatGroup from '../StatGroup';
import useStyles from './styles';

const TherapistApprovalRateModal = dynamic(
  () => import('components/Modals').then((r) => r.TherapistApprovalRateModal),
  { ssr: false },
);

interface PrimaryInfoSectionProps {
  detail?: ITherapistItem;
  summaryReview?: {
    sumRating: number;
    sumReviewer: number;
  };
  loading?: boolean;
}

const PrimaryInfoSection: React.FC<PrimaryInfoSectionProps> = ({
  detail,
  summaryReview,
  loading = false,
}) => {
  const { classes } = useStyles();
  const [openApprovalRate, setOpenApprovalRate] = useState(false);
  const rating =
    (summaryReview?.sumRating || 0) / (summaryReview?.sumReviewer || 0) || 0;

  const handleOpenApprovalRate = () => {
    setOpenApprovalRate(true);
  };

  if (loading) {
    return (
      <Group
        className={classes.therapistPrimaryInfoWrapper}
        spacing={0}
        sx={{ alignItems: 'center' }}
      >
        <Flex className={classes.leftColumn}>
          <Skeleton h={200} sx={{ flexShrink: 0 }} w={230} />
          <Flex direction="column" gap={31} justify="center" w="100%">
            <Flex direction="column" gap={20}>
              <Skeleton h={20} w="100%" />
              <Skeleton h={20} w="50%" />
            </Flex>
            <Skeleton h={73} w="100%" />
          </Flex>
        </Flex>
        <Flex
          className={classes.rightColumn}
          direction="column"
          gap={24}
          justify="center"
        >
          <Skeleton h={14} w="50%" />
          <Skeleton h={14} w="100%" />
          <Skeleton h={14} w="100%" />
          <Skeleton h={14} w="100%" />
          <Skeleton h={14} w="100%" />
        </Flex>
      </Group>
    );
  }

  return (
    <Group className={classes.therapistPrimaryInfoWrapper} spacing={0}>
      {detail?.therapistCurrentTreatment?.isBusy && (
        <Text className={classes.therapistBusyBadge}>
          <IconTherapistBusy />
          <span>
            現在、他のお客様を施術中のため、返信に時間がかかる場合があります。
          </span>
        </Text>
      )}
      <Stack
        align="start"
        display={{ base: 'flex', sm: 'none' }}
        spacing={20}
        w="100%"
      >
        <Group noWrap spacing={15}>
          <Image
            alt="Therapist avatar"
            className="therapist-avatar"
            height={70}
            priority
            src={
              detail?.profilePicture?.url ||
              '/icons/icon-avatar-default-therapist.svg'
            }
            width={70}
          />
          <Stack spacing={5}>
            <Title className={classes.therapistName} order={1} size={24}>
              {detail?.nickName}
            </Title>
            <Text className={classes.therapistRating} size={12} weight="bold">
              {detail?.isNew && (
                <Badge color="green" data-tag="new" radius={2}>
                  New
                </Badge>
              )}
              <Image
                alt="Star icon"
                height={25}
                src="/icons/icon-star.svg"
                width={25}
              />
              <span>{rating.toFixed(1)}</span>
              <span>({summaryReview?.sumReviewer || 0}件)</span>
            </Text>
          </Stack>
        </Group>
        <StatGroup
          {...detail?.summaryResponseRate}
          onOpenDetail={handleOpenApprovalRate}
          sumCancelCountBookings={detail?.sumCancelCountBookings}
        />
      </Stack>
      <Group
        align="start"
        className={classes.leftColumn}
        display={{ base: 'none', sm: 'flex' }}
      >
        <Image
          alt="Therapist avatar"
          className="therapist-avatar"
          height={200}
          priority
          src={
            detail?.profilePicture?.url ||
            '/icons/icon-avatar-default-therapist.svg'
          }
          style={{ flexShrink: 0 }}
          width={200}
        />
        <Stack
          align="start"
          className={classes.therapistInfoContentWrapper}
          spacing={0}
        >
          <Title className={classes.therapistName} order={1} size={24}>
            {detail?.nickName}
          </Title>
          <Text className={classes.therapistRating} size={12} weight="bold">
            {detail?.isNew && (
              <Badge color="green" data-size="lg" data-tag="new" radius={2}>
                New
              </Badge>
            )}
            <Image
              alt="Star icon"
              height={25}
              src="/icons/icon-star.svg"
              width={25}
            />
            <span>{rating.toFixed(1)}</span>
            <span>({summaryReview?.sumReviewer || 0}件)</span>
          </Text>
          <StatGroup
            {...detail?.summaryResponseRate}
            onOpenDetail={handleOpenApprovalRate}
            sumCancelCountBookings={detail?.sumCancelCountBookings}
          />
        </Stack>
      </Group>
      <Text className={classes.rightColumn}>
        <Image
          alt="Comment icon"
          height={16}
          src="/icons/icon-comment.svg"
          width={16}
        />
        {detail?.introduction}
      </Text>
      <TherapistApprovalRateModal
        onClose={() => setOpenApprovalRate(false)}
        opened={openApprovalRate}
      />
    </Group>
  );
};

export default PrimaryInfoSection;
