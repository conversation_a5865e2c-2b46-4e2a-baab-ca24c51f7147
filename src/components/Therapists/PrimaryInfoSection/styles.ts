import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  therapistPrimaryInfoWrapper: {
    maxWidth: 1140,
    margin: '48px auto 0',
    gap: '20px 50px',
    alignItems: 'flex-start',
    [theme.fn.smallerThan('sm')]: {
      marginTop: 32,
    },
    '.therapist-avatar': {
      borderRadius: 10,
      objectFit: 'cover',
      flexShrink: 0,
      [theme.fn.smallerThan('sm')]: {
        borderRadius: '100%',
        height: 70,
        width: 70,
      },
    },
  },
  therapistBusyBadge: {
    backgroundColor: theme.colors.floralWhite[0],
    color: theme.colors.marigold[0],
    fontWeight: 500,
    fontSize: 16,
    padding: '16px 24px',
    borderRadius: '4px',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    boxShadow: '0px 1px 1px 0px rgba(0, 0, 0, 0.16)',
    marginBottom: 20,
    marginTop: -8,
    span: {
      marginLeft: 16,
    },
    svg: {
      flexShrink: 0,
    },
    [theme.fn.smallerThan('sm')]: {
      margin: '0 -20px',
      width: 'calc(100% + 40px)',
      borderRadius: 0,
      alignItems: 'flex-start',
      padding: '16px 20px',
      fontSize: 14,
      lineHeight: '20px',
      span: {
        marginLeft: 8,
      },
      svg: {
        width: 24,
        height: 24,
      },
    },
  },
  leftColumn: {
    flex: '0 1 670px',
    flexWrap: 'nowrap',
    gap: 30,
  },
  therapistName: {
    marginBottom: 15,
    [theme.fn.smallerThan('sm')]: {
      margin: 0,
      fontSize: 15,
    },
  },
  therapistRating: {
    color: theme.colors.marigold[0],
    fontSize: 20,
    display: 'flex',
    alignItems: 'center',
    gap: 4,
    marginBottom: 18,
    'span:last-of-type': {
      color: '#4d4d4d',
      fontWeight: 'normal',
    },
    [theme.fn.smallerThan('sm')]: {
      margin: 0,
      fontSize: 12,
      img: {
        width: 16,
        height: 16,
      },
    },
  },
  rightColumn: {
    flex: '1 1 150px !important',
    fontSize: 18,
    textAlign: 'justify',
    display: 'flex',
    img: {
      display: 'none',
      marginTop: 4,
      marginRight: 8,
    },
    [theme.fn.smallerThan('sm')]: {
      fontSize: 14,
      img: {
        display: 'inline',
      },
    },
  },
  therapistInfoContentWrapper: {
    flexGrow: 1,
  },
}));

export default useStyles;
