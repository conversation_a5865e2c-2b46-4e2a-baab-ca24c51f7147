import { useFetchData } from 'hooks';
import { isEmpty, sortBy } from 'lodash';
import type { IPrefectureItem } from 'models/resource';
import { resourceQuery } from 'models/resource';
import type { ITherapistItem } from 'models/therapist';

const useDeparturePoint = (
  departurePoint: ITherapistItem['departurePoint'],
) => {
  const { data: prefectureList = [] } = useFetchData<IPrefectureItem[]>(
    resourceQuery.getPrefectureList,
  );

  const departurePoints = (() => {
    if (isEmpty(prefectureList) || isEmpty(departurePoint)) return [];
    const pref = prefectureList.find((item) =>
      item.areaCode.includes(departurePoint[0]?.areaCode ?? ''),
    );
    if (!pref) return [];
    let url = `/${pref.nameEn}`;
    const result = [
      {
        name: pref.name,
        level: pref.level,
        url,
      },
    ];
    for (let i = (departurePoint?.length ?? 1) - 1; i > 0; i -= 1) {
      const departure = departurePoint[i];
      if (!departure || departure?.name === '23区内') break;
      const area = pref.children?.find(
        (item) =>
          item.name.includes(departure.name) && item.level === departure.level,
      );
      if (area) url += `/${area.nameEn}`;
      result.push({
        name: departure.name,
        level: departure.level,
        url,
      });
    }
    return sortBy(result, ['level']);
  })();

  return { departurePoints };
};

export default useDeparturePoint;
