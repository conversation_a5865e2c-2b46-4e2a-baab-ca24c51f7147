import IconMessage from '@icons/icon-message.svg';
import { Box, Group, Rating, Stack, Text } from '@mantine/core';
import ReviewCell from 'components/ReviewCell';
import ReviewSkeleton from 'components/ReviewCell/ReviewSkeleton';
import { times } from 'lodash';
import _toNumber from 'lodash/toNumber';
import type { IReviewList } from 'models/therapist';
import Link from 'next/link';

import { sx } from './styles';

interface CustomerReviewSectionProps {
  className?: string;
  title?: React.ReactNode;
  reviewInfo?: IReviewList;
  therapistId?: string;
  loading?: boolean;
}

const CustomerReviewSection: React.FC<CustomerReviewSectionProps> = ({
  className,
  title,
  reviewInfo,
  therapistId,
  loading = false,
}) => {
  const summaryReview = reviewInfo?.summaryReview;
  const reviews = reviewInfo?.reviews?.data || [];
  const total = reviewInfo?.reviews?.total || 0;
  const rating =
    (summaryReview?.sumRating || 0) / (summaryReview?.sumReviewer || 0) || 0;

  if (loading) {
    return (
      <Box className={className} component="section" id="review-section">
        {title}
        <Stack mt={30} spacing={10}>
          {times(2).map((i) => (
            <ReviewSkeleton key={i} />
          ))}
        </Stack>
      </Box>
    );
  }
  return (
    <Box className={className} component="section" id="review-section">
      {title}
      <Group position="apart" sx={sx.reviewSummaryWrapper}>
        <Group sx={sx.reviewSummary}>
          <Text sx={sx.avgRating}>
            {rating.toFixed(1)}
            <Rating
              fractions={5}
              readOnly
              value={_toNumber(rating.toFixed(1))}
            />
          </Text>
          <Text sx={sx.totalRating}>
            <IconMessage />
            {summaryReview?.sumReviewer || 0}件
          </Text>
        </Group>
        {total > 3 && (
          <Text
            component={Link}
            href={`/therapist/${therapistId}/review`}
            sx={sx.seeMoreReview}
          >
            すべて見る
          </Text>
        )}
      </Group>
      <Stack spacing={10}>
        {reviews.map((review) => {
          return <ReviewCell info={review} key={review._id} />;
        })}
      </Stack>
    </Box>
  );
};

export default CustomerReviewSection;
