import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  reviewSummaryWrapper: {
    padding: '4px 20px 24px',
    flexWrap: 'nowrap',
    '@media (max-width: 768px)': {
      padding: '4px 20px 16px',
      flexWrap: 'wrap',
    },
  },
  reviewSummary: {
    flexWrap: 'wrap',
    gap: '0px 30px',
    '@media (max-width: 768px)': {
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
  },
  seeMoreReview: {
    flexShrink: 0,
    color: '#43749a',
    fontWeight: 'bold',
    '&:hover': {
      textDecoration: 'underline',
    },
    '@media (max-width: 768px)': {
      alignSelf: 'flex-start',
      marginTop: 12,
    },
  },
  avgRating: {
    display: 'flex',
    alignItems: 'center',
    fontSize: 35,
    fontWeight: 'bold',
    color: '#3c3c3c',
    gap: 8,
    whiteSpace: 'nowrap',
    '@media (max-width: 768px)': {
      fontSize: 28,
    },
    svg: {
      width: 32,
      height: 32,
      '@media (max-width: 768px)': {
        width: 24,
        height: 24,
      },
    },
  },
  totalRating: {
    display: 'flex',
    alignItems: 'center',
    color: '#4d4d4d',
    fontSize: 20,
    fontWeight: 'bold',
    gap: 10,
    '@media (max-width: 768px)': {
      fontSize: 12,
      gap: 7,
    },
    svg: {
      width: 24,
      height: 24,
      '@media (max-width: 768px)': {
        width: 14,
        height: 14,
      },
    },
  },
};
