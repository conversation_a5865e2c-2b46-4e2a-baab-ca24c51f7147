import type { CSSObject } from '@emotion/react';
import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  menuSelectWrapper: {
    position: 'relative',
  },
  checkBoxGroup: {
    padding: '30px 20px 0',
    gap: 6,
    position: 'relative',
    '@media (max-width: 768px)': {
      padding: '20px 0 0',
      gap: 16,
    },
  },
  menuNoteWrapper: {
    marginTop: 30,
    border: 'solid 2px #5581a3',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    padding: '20px 20px 18px',
    '@media (max-width: 768px)': {
      position: 'fixed',
      bottom: -1,
      width: '100%',
      marginLeft: -20,
      zIndex: 10,
      padding: '15px 0 0',
      borderRadius: 0,
      border: 0,
      backgroundColor: 'rgba(85, 129, 163, 0.9)',
      transform: 'translate(0, 100%)',
      transition: 'transform 1s cubic-bezier(0.16, 1, 0.3, 1)',
      '&.active': {
        transform: 'translate(0, 0)',
      },
    },
  },
  noteTitle: {
    color: '#a26900',
    fontWeight: 'bold',
    marginBottom: 17,
    '@media (max-width: 768px)': {
      padding: '0 20px',
      color: '#ffd280',
      fontSize: 12,
      marginBottom: 10,
    },
  },
  noteContent: {
    marginBottom: 12,
    '@media (max-width: 768px)': {
      padding: '0 20px',
      color: '#ffffff',
      fontSize: 12,
      marginBottom: 10,
    },
  },
  openApp: {
    fontWeight: 'bold',
    height: 40,
    '@media (max-width: 768px)': {
      backgroundColor: '#e8a62d',
      color: '#ffffff',
      borderRadius: 0,
      '&:hover': {
        backgroundColor: '#e8a62d',
      },
      '&[disabled]': {
        color: '#000000',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
      },
    },
  },
};

export const styles: Record<string, Record<string, CSSObject>> = {
  checkboxStyles: {
    root: {
      display: 'flex',
      '&[data-checked=true]': {
        '.mantine-Checkbox-labelWrapper': {
          borderColor: '#dca338',
          position: 'relative',
          '.menu-info span:nth-of-type(2)': {
            color: '#dca338',
            fontWeight: 'bold',
          },
          '@media (max-width: 768px)': {
            '.icon-close': {
              display: 'block',
            },
          },
        },
      },
    },
    inner: {
      marginRight: 12,
      '@media (max-width: 768px)': {
        display: 'none',
      },
    },
    input: {
      cursor: 'pointer',
      borderRadius: 0,
      borderWidth: 1.5,
    },
    body: {
      width: '100%',
      alignItems: 'center',
    },
    labelWrapper: {
      borderRadius: 2,
      border: '1px solid #d1d1d1',
      width: '100%',
    },
    label: {
      padding: 0,
      position: 'relative',
      backgroundColor: '#ffffff',
      '.icon-close': {
        position: 'absolute',
        top: 0,
        left: 0,
        transform: 'translate(-50%, -50%)',
        zIndex: 1,
        display: 'none',
      },
      '.menu-label': {
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
        '@media (max-width: 768px)': {
          fontSize: 14,
        },
      },
      '.menu-image': {
        flexShrink: 0,
        position: 'relative',
        aspectRatio: '1.3',
        minHeight: 60,
        alignSelf: 'stretch',
        img: {
          borderTopLeftRadius: 2,
          borderBottomLeftRadius: 2,
          objectFit: 'cover',
        },
      },
      '.menu-info': {
        flex: '1 1 auto',
        display: 'flex',
        flexDirection: 'column',
        padding: '0 8px 0 20px',
        gap: 0,
        span: {
          color: '#696969',
          fontSize: 16,
          fontWeight: 'bold',
          '@media (max-width: 768px)': {
            fontSize: 14,
          },
          '&:nth-of-type(2)': {
            fontSize: 12,
            fontWeight: 'normal',
            color: '#909090',
          },
        },
      },
      '.menu-info-icon': {
        flexShrink: 0,
        marginRight: '20px',
        transition: 'all 100ms ease-in-out',
        '&:hover, &:active': {
          transform: 'scale(1.2)',
        },
      },
    },
  },
  modalStyles: {
    header: {
      top: 65,
      right: 30,
      '@media (max-width: 768px)': {
        top: 15,
        right: 15,
      },
    },
    close: {
      background: '#ffffff !important',
      color: '#43749a !important',
      '@media (max-width: 768px)': {
        backgroundColor: '#43749a !important',
        color: '#ffffff !important',
      },
    },
  },
};
