import IconCloseYellow from '@icons/icon-close-yellow.svg';
import {
  Box,
  Button,
  Checkbox,
  Flex,
  Group,
  Skeleton,
  Stack,
  Text,
} from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import { openContextModal } from '@mantine/modals';
import type { ICreateBooking } from 'hooks/types';
import { times } from 'lodash';
import _isEmpty from 'lodash/isEmpty';
import type { IMenuItem, IPriceOption } from 'models/therapist';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import helpers, { eventLog } from 'utils/helpers';

import { styles, sx } from './styles';

interface MenuSelectSectionProps {
  className?: string;
  title?: React.ReactNode;
  menus?: IMenuItem[];
  minMenu?: {
    duration: number;
    price: number;
  };
  handleStartBooking: (values: Pick<ICreateBooking, 'menus'>) => void;
  loading?: boolean;
  therapistId: string;
}

const MenuSelectSection: React.FC<MenuSelectSectionProps> = ({
  className,
  title,
  minMenu,
  menus = [],
  handleStartBooking,
  loading = false,
  therapistId,
}) => {
  const router = useRouter();
  const [checked, setChecked] = useState<Record<string, IMenuItem>>({});
  const { ref, entry } = useIntersection({
    root: null,
    rootMargin: '0px',
    threshold: 0.05,
  });

  useEffect(() => {
    if (router?.query?.viewMenu === 'true') {
      const section = document.querySelector('#menu-section');
      section?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [router?.query?.viewMenu]);

  let totalDuration = 0;
  Object.keys(checked).forEach((key) => {
    totalDuration += checked[key]?.selectedOption?.duration || 0;
  });

  const handleOnCheck = (menu: IMenuItem, price: IPriceOption) => {
    const newChecked: Record<string, IMenuItem> = {
      ...checked,
      [menu._id]: {
        ...menu,
        selectedOption: price,
      },
    };
    setChecked(newChecked);
  };

  const handleOnRemove = (menu: IMenuItem) => {
    const newChecked: Record<string, IMenuItem> = {
      ...checked,
    };

    if (newChecked[menu._id]) {
      delete newChecked[menu._id];
      setChecked(newChecked);
    }
  };

  const handleOnSubmit = () => {
    const selectedMenus = Object.keys(checked).map(
      (key) => checked[key],
    ) as IMenuItem[];
    handleStartBooking({
      menus: selectedMenus,
    });
  };

  const minDuration = minMenu?.duration || 60;

  const renderMenus = () => {
    if (loading) {
      return (
        <Stack sx={sx.checkBoxGroup}>
          {times(3).map((i) => (
            <Flex align="center" gap={12} key={i}>
              <Box bg="white" h={18} sx={{ flexShrink: 0 }} w={18} />
              <Flex
                align="center"
                bg="white"
                gap={20}
                h={60}
                px={10}
                sx={{ borderRadius: 2 }}
                w="100%"
              >
                <Skeleton h={40} w={50} />
                <Skeleton h={14} w="50%" />
              </Flex>
            </Flex>
          ))}
        </Stack>
      );
    }

    return (
      <Box sx={sx.menuSelectWrapper}>
        <Stack sx={sx.checkBoxGroup}>
          {menus.map((menu) => {
            const menuDurationPrice = checked[menu._id]
              ? `${
                  checked[menu._id]?.selectedOption?.duration
                }分 / ¥${helpers.numberFormat(
                  checked[menu._id]?.selectedOption?.price,
                )}`
              : `${menu?.options[0]?.duration}分 / ¥${helpers.numberFormat(
                  menu?.options[0]?.price,
                )} 〜`;
            return (
              <Checkbox
                checked={!_isEmpty(checked[menu._id])}
                key={menu._id}
                label={
                  <Group
                    className="menu-label"
                    noWrap
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      eventLog('select_menu', {
                        therapist_id: therapistId,
                        menu_id: menu._id,
                      });
                      openContextModal({
                        modal: 'MenuPriceSelectModal',
                        centered: true,
                        size: 335,
                        innerProps: {
                          priceOptions: menu.options,
                          priceSelected: checked[menu._id]?.selectedOption,
                          onSelect: (option: IPriceOption) => {
                            handleOnCheck(menu, option);
                            eventLog('add_to_cart', {
                              menu_id: menu._id,
                              duration: option?.duration,
                              price: option?.price,
                            });
                          },
                        },
                        withCloseButton: false,
                      });
                    }}
                    spacing={0}
                  >
                    <IconCloseYellow
                      className="icon-close"
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        eventLog('remove_from_cart', {
                          menu_id: menu._id,
                          duration: checked[menu._id]?.selectedOption?.duration,
                          price: checked[menu._id]?.selectedOption?.price,
                        });
                        handleOnRemove(menu);
                      }}
                    />
                    <div className="menu-image">
                      <Image
                        alt="Menu image"
                        fill
                        sizes="15vw"
                        src={
                          menu.images?.large?.url || '/images/menu-default.webp'
                        }
                      />
                    </div>
                    <div className="menu-info">
                      <Text component="span" lineClamp={1}>
                        {menu.title}
                      </Text>
                      <Text component="span" lineClamp={1}>
                        {menuDurationPrice}
                      </Text>
                    </div>
                    <div
                      className="menu-info-icon"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        eventLog('view_menu', {
                          menu_id: menu._id,
                          menu_name: menu.title,
                          menu_type: menu.type,
                        });
                        openContextModal({
                          modal: 'MenuInfoModal',
                          size: 1340,
                          innerProps: {
                            menu,
                          },
                          styles: styles.modalStyles,
                        });
                      }}
                    >
                      <Image
                        alt="Menu info icon"
                        height={16}
                        src="/icons/icon-info-1.svg"
                        width={16}
                      />
                    </div>
                  </Group>
                }
                onChange={(e) => {
                  if (e.target.checked) {
                    openContextModal({
                      modal: 'MenuPriceSelectModal',
                      size: 335,
                      centered: true,
                      innerProps: {
                        priceOptions: menu.options,
                        priceSelected: checked[menu._id]?.selectedOption,
                        onSelect: (option: IPriceOption) => {
                          handleOnCheck(menu, option);
                          eventLog('add_to_cart', {
                            menu_id: menu._id,
                            duration: option?.duration,
                            price: option?.price,
                          });
                        },
                      },
                      withCloseButton: false,
                    });
                  } else {
                    eventLog('remove_from_cart', {
                      menu_id: menu._id,
                      duration: checked[menu._id]?.selectedOption?.duration,
                      price: checked[menu._id]?.selectedOption?.price,
                    });
                    handleOnRemove(menu);
                  }
                }}
                styles={styles.checkboxStyles}
                value={menu._id}
                wrapperProps={{
                  'data-checked': !_isEmpty(checked[menu._id]),
                }}
              />
            );
          })}
        </Stack>
      </Box>
    );
  };

  return (
    <Box className={className} component="section" id="menu-section" ref={ref}>
      {title}
      {renderMenus()}
      <Box
        className={entry?.isIntersecting ? 'active' : ''}
        sx={sx.menuNoteWrapper}
      >
        <Box sx={sx.noteTitle}>
          このセラピストは合計{minDuration}分以上からご予約いただけます。
        </Box>
        <Box sx={sx.noteContent}>
          施術料金は交通(出張)費を含みます。
          <br />
          施術に必要なタオルやオイルなどはすべてセラピストがご用意いたします。
        </Box>
        <Button
          disabled={totalDuration < minDuration}
          fullWidth
          onClick={handleOnSubmit}
          sx={sx.openApp}
        >
          予約リクエスト入力
        </Button>
      </Box>
    </Box>
  );
};

export default MenuSelectSection;
