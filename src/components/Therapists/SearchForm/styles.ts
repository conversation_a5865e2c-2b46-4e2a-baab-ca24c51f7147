import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  searchForm: {
    padding: '20px 15px 44px',
    border: 'solid 1px #43749a',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    position: 'relative',
  },
  genderField: {
    '.mantine-InputWrapper-label': {
      padding: '0 15px 5px',
      fontSize: 16,
      display: 'flex',
      alignItems: 'center',
      gap: 10,
      color: '#727272',
      margin: 0,
      svg: {
        color: '#000000',
      },
    },
  },
  submitBtn: {
    maxWidth: 230,
    width: '100%',
    height: 50,
    fontSize: 16,
    margin: '10px auto 0',
    svg: {
      marginRight: 10,
    },
  },
};
