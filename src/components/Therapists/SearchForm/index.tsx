import { yupResolver } from '@hookform/resolvers/yup';
import IconAvatar from '@icons/icon-avatar.svg';
import IconCalendar from '@icons/icon-calendar.svg';
import IconLocation from '@icons/icon-location.svg';
import IconNote from '@icons/icon-note.svg';
import IconSearch from '@icons/icon-search.svg';
import { Box, Button, LoadingOverlay, Stack, Title } from '@mantine/core';
import {
  CheckboxField,
  ChipField,
  DateTimePicker,
  LocationPicker,
} from 'components/Form';
import type { IMenuItem } from 'models/therapist';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { DEFAULT_MENUS, GENDER_SEARCHING } from 'utils/constants';
import type { InferType } from 'yup';

import schema from './schema';
import { sx } from './styles';

export type SearchTherapistsFormValues = InferType<typeof schema>;

interface TherapistsSearchFormProps {
  menus?: IMenuItem[];
  onSubmit: (value: SearchTherapistsFormValues) => void;
  defaultValues: SearchTherapistsFormValues;
  isLoading?: boolean;
}

const TherapistsSearchForm: React.FC<TherapistsSearchFormProps> = ({
  menus,
  onSubmit,
  defaultValues,
  isLoading = false,
}) => {
  const { control, handleSubmit, reset } = useForm<SearchTherapistsFormValues>({
    defaultValues,
    shouldFocusError: true,
    resolver: yupResolver(schema),
  });

  const menuOptions = useMemo(
    () => [
      {
        label: 'メニューすべて',
        value: '',
      },
      ...(menus
        ? menus.map((menu) => ({
            value: menu.titleEn,
            label: menu.title,
          }))
        : DEFAULT_MENUS),
    ],
    [menus],
  );

  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <Box sx={sx.searchForm}>
      <Title mb={20} order={4} size={20}>
        検索条件
      </Title>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={16}>
          <DateTimePicker
            control={control}
            label={
              <>
                <IconCalendar />
                開始日時
              </>
            }
            name="dateBooking"
            placeholder="開始日時"
            required
          />
          <LocationPicker
            control={control}
            label={
              <>
                <IconLocation />
                エリア
              </>
            }
            name="areaNames"
            placeholder="エリア"
            required
          />
          <CheckboxField
            control={control}
            label={
              <>
                <IconNote />
                施術メニュー
              </>
            }
            name="selectMenu"
            options={menuOptions}
            selectedAllValue=""
          />
          <ChipField
            control={control}
            inputWrapperProps={{ sx: sx.genderField }}
            label={
              <>
                <IconAvatar />
                セラピストの性別
              </>
            }
            name="gender"
            options={['1', '2', '0'].map((key) => ({
              value: key,
              children: GENDER_SEARCHING[key],
            }))}
          />
          <Button sx={sx.submitBtn} type="submit">
            <IconSearch />
            条件を変更
          </Button>
        </Stack>
      </form>
      <LoadingOverlay
        exitTransitionDuration={1500}
        loaderProps={{ size: 'lg' }}
        overlayBlur={3}
        sx={{ borderRadius: 6 }}
        transitionDuration={1500}
        visible={isLoading}
      />
    </Box>
  );
};

export default TherapistsSearchForm;
