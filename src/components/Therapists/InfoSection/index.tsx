import { Anchor, Box, Group, Skeleton, Stack, Text } from '@mantine/core';
import { isEmpty } from 'lodash';
import type { ITherapistItem } from 'models/therapist';
import Image from 'next/image';
import { GENDER } from 'utils/constants';

import useDeparturePoint from '../useDeparturePoint';
import { sx } from './styles';

interface InfoSectionProps {
  className?: string;
  title?: React.ReactNode;
  detail?: ITherapistItem;
  loading?: boolean;
}

const InfoSection: React.FC<InfoSectionProps> = ({
  className,
  title,
  detail,
  loading = false,
}) => {
  const { departurePoints } = useDeparturePoint(detail?.departurePoint || []);

  if (loading) {
    return (
      <Box className={className} component="section" id="info-section">
        {title}
        <Stack px={0} py={30} spacing={20} sx={sx.infoContentWrapper}>
          <Skeleton h={20} w="30%" />
          <Skeleton h={20} w="100%" />
          <Skeleton h={20} w="100%" />
        </Stack>
      </Box>
    );
  }

  return (
    <Box className={className} component="section" id="info-section">
      {title}
      <Stack px={20} py={30} spacing={20} sx={sx.infoContentWrapper}>
        <Group sx={sx.infoRow}>
          <Text sx={sx.infoContent}>
            <Image
              alt="性別"
              height={22}
              src="/icons/icon-avatar.svg"
              width={22}
            />
            <span className="label">性別</span>
            <span className="content">
              {typeof detail?.gender === 'number' ? GENDER[detail.gender] : ''}
            </span>
          </Text>
          <Text sx={sx.infoContent}>
            <Image
              alt="施術歴"
              height={22}
              src="/icons/icon-backpack.svg"
              width={22}
            />
            <span className="label">施術歴</span>
            <span className="content">{detail?.experience?.name}</span>
          </Text>
        </Group>
        {!isEmpty(departurePoints) && (
          <Text className="departure-point" sx={sx.infoContent}>
            <Image
              alt="出発地"
              height={32}
              src="/icons/icon-person.svg"
              width={20}
            />
            <span className="label">出発地</span>
            <span className="content">
              {departurePoints.map((departure, index) => (
                <span key={index}>
                  <Anchor href={departure.url}>{departure.name}</Anchor>
                  {index < departurePoints.length - 1 && ' / '}
                </span>
              ))}
            </span>
          </Text>
        )}
        <Text className="self-pr" sx={sx.infoContent}>
          <Image
            alt="出発地"
            height={22}
            src="/icons/icon-siren-microphone.svg"
            width={22}
          />
          <span className="label">自己PR</span>
          <span className="content">{detail?.certificate}</span>
        </Text>
      </Stack>
    </Box>
  );
};

export default InfoSection;
