import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  infoContentWrapper: {
    padding: '30px 20px',
    '@media (max-width: 768px)': {
      padding: '17px 20px 0',
    },
  },
  infoContent: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: 12,
    fontSize: 20,
    color: '#000000',
    '@media (max-width: 768px)': {
      fontSize: 14,
      flexWrap: 'wrap',
    },
    img: {
      marginTop: 6,
      '@media (max-width: 768px)': {
        width: 17,
        height: 17,
        marginTop: 3,
      },
    },
    '.label': {
      fontWeight: 'bold',
      flexShrink: 0,
    },
    '&.departure-point': {
      img: {
        marginTop: 0,
        '@media (max-width: 768px)': {
          marginTop: 0,
          width: 15,
          height: 24,
        },
      },
      '.content': {
        color: '#43749A',
        a: {
          textDecoration: 'underline',
        },
      },
    },
    '&.self-pr': {
      flexWrap: 'wrap',
      '.content': {
        flexBasis: '100%',
      },
    },
  },
  infoRow: {
    gap: '20px 42px',
    '@media (max-width: 480px)': {
      justifyContent: 'space-between',
    },
  },
};
