import { yupResolver } from '@hookform/resolvers/yup';
import IconAvatar from '@icons/icon-avatar.svg';
import IconCalendar from '@icons/icon-calendar.svg';
import IconClock from '@icons/icon-clock.svg';
import IconLocation from '@icons/icon-location.svg';
import IconNote from '@icons/icon-note.svg';
import IconSearch from '@icons/icon-search.svg';
import { Box, Button, Group, LoadingOverlay, Stack } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import schema from 'components/Therapists/SearchForm/schema';
import { isEmpty } from 'lodash';
import type { IMenuItem } from 'models/therapist';
import { useForm } from 'react-hook-form';
import { GENDER_SEARCHING } from 'utils/constants';
import dayjs from 'utils/dayjs';

import type { SearchTherapistsFormValues } from '../SearchForm';
import { sx } from './styles';

interface TherapistsSearchFormProps {
  menus?: IMenuItem[];
  isLoading?: boolean;
  defaultValues: SearchTherapistsFormValues;
  onSubmit: (value: SearchTherapistsFormValues) => void;
}

const TherapistsSearchFormSP: React.FC<TherapistsSearchFormProps> = ({
  menus,
  isLoading = false,
  defaultValues,
  onSubmit,
}) => {
  const { control, handleSubmit, reset } = useForm<SearchTherapistsFormValues>({
    defaultValues,
    resolver: yupResolver(schema),
  });

  const menuSelected: string[] = [];
  (menus || []).forEach((menu) => {
    if ((defaultValues.selectMenu || []).includes(menu.titleEn)) {
      menuSelected.push(menu.title);
    }
  });

  return (
    <Box sx={sx.searchFormSP}>
      <Stack spacing={6} sx={{ padding: '0 20px' }}>
        <Group position="apart" spacing={6} sx={sx.fieldRow}>
          <Box sx={sx.fieldItem}>
            <IconCalendar />
            {dayjs(defaultValues.dateBooking).format('LL')}
          </Box>
          <Box sx={sx.fieldItem}>
            <IconClock />
            {dayjs(defaultValues.dateBooking).format('HH:mm')} ~
          </Box>
        </Group>
        <Group position="apart" spacing={6} sx={sx.fieldRow}>
          <Box sx={sx.fieldItem}>
            <IconLocation />
            {defaultValues.areaNames.map((area) => area.name).join(' ')}
          </Box>
          <Box sx={sx.fieldItem}>
            <IconAvatar />
            {GENDER_SEARCHING[defaultValues.gender || '']}
          </Box>
        </Group>
        <Box sx={sx.fieldItem}>
          <IconNote />
          {isEmpty(menuSelected) ? 'メニューすべて' : menuSelected.join(' ')}
        </Box>
      </Stack>
      <Button
        fullWidth
        onClick={() => {
          reset(defaultValues);
          openContextModal({
            modal: 'SearchTherapistsFormModal',
            size: 768,
            innerProps: {
              menus,
              control,
              onSubmit: handleSubmit(onSubmit),
            },
            styles: {
              header: {
                top: '15px !important',
                right: '15px !important',
              },
              close: {
                width: 30,
                height: 30,
                svg: {
                  width: 20,
                  height: 20,
                },
              },
            },
          });
        }}
        sx={sx.openModalBtn}
      >
        <IconSearch />
        条件を変更
      </Button>
      <LoadingOverlay
        exitTransitionDuration={1500}
        overlayBlur={3}
        sx={{ borderRadius: 6 }}
        transitionDuration={1500}
        visible={isLoading}
      />
    </Box>
  );
};

export default TherapistsSearchFormSP;
