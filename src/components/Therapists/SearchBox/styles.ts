import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  searchFormSP: {
    padding: '20px 10px 10px',
    backgroundColor: '#ffffff',
    borderRadius: 6,
    position: 'relative',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
  },
  fieldRow: {
    '& > *:last-of-type': {
      flex: '0 0 80px',
    },
  },
  fieldItem: {
    display: 'flex',
    color: '#3c3c3c',
    alignItems: 'center',
    svg: {
      width: 16,
      height: 16,
      marginRight: 8,
    },
  },
  openModalBtn: {
    height: 46,
    fontSize: 14,
    marginTop: 16,
    svg: {
      marginRight: 10,
    },
  },
};
