import IconMessage from '@icons/icon-message.svg';
import {
  Flex,
  Group,
  Rating,
  Skeleton,
  Stack,
  Text,
  Title,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import _toNumber from 'lodash/toNumber';
import type { ITherapistItem } from 'models/therapist';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import React, { useState } from 'react';
import { GENDER } from 'utils/constants';

import StatGroup from '../StatGroup';
import { sx } from './styles';

const TherapistApprovalRateModal = dynamic(
  () => import('components/Modals').then((r) => r.TherapistApprovalRateModal),
  { ssr: false },
);

interface ReviewPrimaryInfoSectionProps {
  detail?: ITherapistItem;
  summaryReview?: {
    sumRating: number;
    sumReviewer: number;
  };
  loading?: boolean;
}

const ReviewPrimaryInfoSection: React.FC<ReviewPrimaryInfoSectionProps> = ({
  detail,
  summaryReview,
  loading = false,
}) => {
  const [openApprovalRate, setOpenApprovalRate] = useState(false);
  const mobileScreen = useMediaQuery('(max-width: 768px)', true, {
    getInitialValueInEffect: false,
  });
  const rating =
    (summaryReview?.sumRating || 0) / (summaryReview?.sumReviewer || 0) || 0;

  const handleOpenApprovalRate = () => {
    setOpenApprovalRate(true);
  };

  if (loading) {
    return (
      <Stack sx={sx.reviewPrimaryInfoWrapper}>
        <Flex gap={30} sx={{ maxWidth: 650, width: '100%' }}>
          <Skeleton h={170} sx={{ flexShrink: 0 }} w={200} />
          <Flex direction="column" gap={37} justify="center" w="100%">
            <Flex direction="column" gap={20}>
              <Skeleton h={20} w="100%" />
              <Skeleton h={20} w="50%" />
            </Flex>
            <Skeleton h={73} w="100%" />
          </Flex>
        </Flex>
      </Stack>
    );
  }

  return (
    <Stack sx={sx.reviewPrimaryInfoWrapper}>
      <Group noWrap>
        <Image
          alt="Therapist avatar"
          className="therapist-avatar"
          height={200}
          priority
          src={
            detail?.profilePicture?.url ||
            '/icons/icon-avatar-default-therapist.svg'
          }
          width={200}
        />
        <Stack spacing={0} sx={sx.infoContentWrapper}>
          <Title order={1} size={24} sx={sx.therapistName}>
            {detail?.nickName}
          </Title>
          {mobileScreen ? (
            <Group sx={sx.mobileInfoRow}>
              <Text sx={sx.mobileInfoContent}>
                <Image
                  alt="性別"
                  height={22}
                  src="/icons/icon-avatar.svg"
                  width={22}
                />
                <span className="label">性別</span>
                <span className="content">
                  {typeof detail?.gender === 'number'
                    ? GENDER[detail.gender]
                    : ''}
                </span>
              </Text>
              <Text sx={sx.mobileInfoContent}>
                <Image
                  alt="施術歴"
                  height={22}
                  src="/icons/icon-backpack.svg"
                  width={22}
                />
                <span className="label">施術歴</span>
                <span className="content">{detail?.experience?.name}</span>
              </Text>
            </Group>
          ) : (
            <Group align="end" sx={sx.desktopInfoRow}>
              <StatGroup
                {...detail?.summaryResponseRate}
                onOpenDetail={handleOpenApprovalRate}
                sumCancelCountBookings={detail?.sumCancelCountBookings}
              />
              <Group spacing={30} sx={sx.reviewSummaryWrapper}>
                <Text sx={sx.avgRating}>
                  {rating.toFixed(1)}
                  <Rating
                    fractions={5}
                    readOnly
                    value={_toNumber(rating.toFixed(1))}
                  />
                </Text>
                <Text sx={sx.totalRating}>
                  <IconMessage />
                  {summaryReview?.sumReviewer || 0}件
                </Text>
              </Group>
            </Group>
          )}
        </Stack>
      </Group>
      {mobileScreen && (
        <StatGroup
          {...detail?.summaryResponseRate}
          onOpenDetail={handleOpenApprovalRate}
          sumCancelCountBookings={detail?.sumCancelCountBookings}
        />
      )}
      <TherapistApprovalRateModal
        onClose={() => setOpenApprovalRate(false)}
        opened={openApprovalRate}
      />
    </Stack>
  );
};

export default ReviewPrimaryInfoSection;
