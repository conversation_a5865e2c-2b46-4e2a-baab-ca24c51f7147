import type { Sx } from '@mantine/core';

export const sx: Record<string, Sx> = {
  reviewPrimaryInfoWrapper: {
    maxWidth: 1140,
    margin: '48px auto 0',
    gap: 20,
    '@media (max-width: 768px)': {
      marginTop: 32,
    },
    '.therapist-avatar': {
      borderRadius: 10,
      objectFit: 'cover',
      flexShrink: 0,
      '@media (max-width: 768px)': {
        borderRadius: '100%',
        height: 70,
        width: 70,
      },
    },
  },
  infoContentWrapper: {
    alignItems: 'flex-start',
    width: '100%',
    '@media (max-width: 768px)': {
      gap: 4,
    },
  },
  therapistName: {
    marginBottom: 20,
    '@media (max-width: 768px)': {
      margin: 0,
      fontSize: 15,
    },
  },
  avgRating: {
    display: 'flex',
    alignItems: 'center',
    fontSize: 35,
    fontWeight: 'bold',
    color: '#3c3c3c',
    gap: 8,
    whiteSpace: 'nowrap',
    '@media (max-width: 768px)': {
      fontSize: 28,
    },
    svg: {
      width: 32,
      height: 32,
      '@media (max-width: 768px)': {
        width: 24,
        height: 24,
      },
    },
  },
  totalRating: {
    display: 'flex',
    alignItems: 'center',
    color: '#4d4d4d',
    fontSize: 20,
    fontWeight: 'bold',
    gap: 10,
    '@media (max-width: 768px)': {
      fontSize: 12,
      gap: 7,
    },
    svg: {
      width: 24,
      height: 24,
      '@media (max-width: 768px)': {
        width: 14,
        height: 14,
      },
    },
  },

  mobileInfoContent: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: 8,
    fontSize: 14,
    color: '#000000',
    img: {
      marginTop: 3,
      width: 16,
      height: 16,
    },
    '.label': {
      fontWeight: 'bold',
      flexShrink: 0,
    },
  },
  mobileInfoRow: {
    gap: '0 20px',
  },
  desktopInfoRow: {
    gap: '12px 50px',
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    width: '100%',
  },
  reviewSummaryWrapper: {
    justifyContent: 'space-between',
  },
};
