import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  therapistCardWrapper: {
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.16)',
    backgroundColor: theme.white,
    padding: '10px 18px 16px',
    borderRadius: 6,
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    transition: 'box-shadow 200ms ease-in',
    '&:hover': {
      boxShadow: '0 2px 24px 0 rgba(0, 0, 0, 0.2)',
    },
  },
  therapistBusyBadge: {
    backgroundColor: theme.colors.papayaWhip[0],
    color: theme.colors.marigold[0],
    fontWeight: 500,
    fontSize: 12,
    lineHeight: '16px',
    padding: '2px 6px',
    borderRadius: '2px',
    display: 'flex',
    alignItems: 'center',
    marginLeft: 8,
    span: {
      marginLeft: 4,
    },
    svg: {
      width: 16,
      height: 16,
      flexShrink: 0,
    },
  },
  therapistAvatar: {
    borderRadius: '100%',
    objectFit: 'cover',
    width: 60,
    height: 60,
    flexShrink: 0,
  },
  rating: {
    display: 'flex',
    alignItems: 'center',
    gap: 4,
    color: '#e39300',
    'span:last-of-type': {
      color: '#4d4d4d',
      fontWeight: 500,
    },
  },
  experience: {
    display: 'flex',
    alignItems: 'center',
    gap: 6,
    svg: {
      flexShrink: 0,
      width: 14.9,
      height: 13,
    },
  },
  introduction: {
    flex: 1,
    color: '#3c3c3c',
    paddingBottom: 12,
    svg: {
      flexShrink: 0,
      width: 14,
      height: 13,
      marginTop: 5,
    },
  },
  departureLocation: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    svg: {
      flexShrink: 0,
      width: 16,
      height: 16,
    },
  },
  departurePointLink: {
    color: '#43749A',
    textDecoration: 'underline',
  },
  menu: {
    borderBottom: '1px solid #dddddd',
    borderTop: '1px solid #dddddd',
    padding: '12px 0',
  },
  minMenu: {
    display: 'flex',
    color: '#3c3c3c',
    gap: 8,
    svg: {
      flexShrink: 0,
      marginTop: 4,
      width: 14,
      height: 14,
    },
  },
  seeMoreMenu: {
    display: 'flex',
    alignItems: 'center',
    color: '#43749A',
    flexShrink: 0,
    gap: 4,
    '&:hover': {
      textDecoration: 'underline',
    },
    svg: {
      flexShrink: 0,
      width: 15,
      height: 14.3,
    },
  },
  availableTimeGroup: {
    padding: '0 16px',
    margin: '11px -16px 0',
    cursor: 'grab',
    userSelect: 'none',
    overflow: 'auto',
    '-ms-overflow-style': 'none',
    scrollbarWidth: 'none',
    '&::-webkit-scrollbar': {
      display: 'none',
    },
  },
  availableTimeGroupItem: {
    flexShrink: 0,
    '&:not(:first-of-type)': {
      paddingLeft: 6,
      marginLeft: 6,
      borderLeft: '1px solid #dddddd',
    },
  },
  availableTime: {
    backgroundColor: '#43749a',
    color: '#ffffff',
    textAlign: 'center',
    fontWeight: 'bold',
    borderRadius: 2,
    padding: '4px 0',
    minWidth: 60,
  },
}));

export default useStyles;
