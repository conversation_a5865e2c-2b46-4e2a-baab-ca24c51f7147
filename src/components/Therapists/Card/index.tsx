import IconBackpack from '@icons/icon-backpack.svg';
import IconComment from '@icons/icon-comment.svg';
import IconNote from '@icons/icon-note.svg';
import IconPerson from '@icons/icon-person.svg';
import IconStar from '@icons/icon-star.svg';
import IconTherapistBusy from '@icons/icon-therapist-busy.svg';
import IconYen from '@icons/icon-yen.svg';
import { Badge, Box, Card, Flex, Group, Stack, Text } from '@mantine/core';
import { get, groupBy, isEmpty } from 'lodash';
import type { ITherapistItem } from 'models/therapist';
import Image from 'next/image';
import React from 'react';
import { useScrollContainer } from 'react-indiana-drag-scroll';
import dayjs from 'utils/dayjs';
import helpers from 'utils/helpers';

import useDeparturePoint from '../useDeparturePoint';
import CardSkeleton from './CardSkeleton';
import useStyles from './styles';

const DeparturePoint = ({
  departurePoints = [],
}: {
  departurePoints: {
    name: string;
    url: string;
  }[];
}) => {
  const { classes } = useStyles();

  return (
    <Group ml={-8} spacing={4}>
      {departurePoints.map((departure, index) => (
        <Text
          className={classes.departurePointLink}
          component="span"
          key={`departure-${index}`}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            window.location.href = departure.url;
          }}
        >
          {departure.name}
        </Text>
      ))}
    </Group>
  );
};

interface TherapistCardProps {
  detail?: ITherapistItem;
  searchingTherapist?: boolean;
  isLoading?: boolean;
  onClickSeeMenu?: (e: React.MouseEvent) => void;
}

const TherapistCard: React.FC<TherapistCardProps> = ({
  detail,
  searchingTherapist = false,
  onClickSeeMenu,
  isLoading = false,
}) => {
  const { classes } = useStyles();
  const scrollContainer = useScrollContainer();
  const rating =
    get(detail, 'summaryReview.sumRating', 0) /
      get(detail, 'summaryReview.sumReviewer', 0) || 0;
  const { departurePoints } = useDeparturePoint(detail?.departurePoint || []);
  const minMenu = get(detail, 'minMenu', {}) || {};

  const freeTimeslotsGroup: Record<string, string[]> = groupBy(
    detail?.freeTimeslots || [],
    (date) => {
      return dayjs(date).startOf('day').format();
    },
  );

  const getTimeslotsText = (date: string) => {
    const datePick = dayjs(date).startOf('d');
    const currentDate = dayjs().startOf('d');

    if (datePick.diff(currentDate, 'd') === 0) {
      return datePick.format('MM/DD (ddd) の受付状況');
    }

    return datePick.format('MM/DD (ddd)');
  };

  if (isLoading) {
    return <CardSkeleton isSearching={searchingTherapist} />;
  }

  return (
    <Card className={classes.therapistCardWrapper} unstyled>
      <Group align="center" noWrap spacing={14}>
        <Box
          alt="Profile picture"
          className={classes.therapistAvatar}
          component={Image}
          height={60}
          src={get(
            detail,
            'profilePicture.url',
            '/icons/icon-avatar-default-therapist.svg',
          )}
          width={60}
        />
        <Stack spacing={4}>
          <Group align="center">
            <Text lineClamp={1} size={15} weight="bold">
              {get(detail, 'nickName')}
            </Text>
          </Group>
          <Flex align="center">
            {detail?.isNew && (
              <Badge color="green" data-tag="new" mr={4} radius={2}>
                New
              </Badge>
            )}
            <Text className={classes.rating} size={12} weight="bold">
              <IconStar />
              <span>{rating.toFixed(1)}</span>
              <span>({get(detail, 'summaryReview.sumReviewer', 0)}件)</span>
            </Text>
            {detail?.therapistCurrentTreatment?.isBusy && (
              <Text className={classes.therapistBusyBadge}>
                <IconTherapistBusy />
                <span>現在施術中</span>
              </Text>
            )}
          </Flex>
        </Stack>
      </Group>
      <Text className={classes.experience} mt={10} size={14}>
        <IconBackpack />
        {get(detail, 'experience.name')}
      </Text>
      <Group
        align="start"
        className={classes.introduction}
        mt={10}
        noWrap
        spacing={8}
      >
        <IconComment />
        <Text lineClamp={2}>{get(detail, 'introduction')}</Text>
      </Group>
      {searchingTherapist ? (
        <>
          {!isEmpty(departurePoints) && (
            <Text
              className={classes.departureLocation}
              lineClamp={1}
              pb={11}
              size={14}
            >
              <IconPerson />
              出発地：
              <DeparturePoint departurePoints={departurePoints} />
            </Text>
          )}
          <Group
            align="center"
            className={classes.menu}
            noWrap
            position="apart"
            py={9}
            spacing={4}
          >
            <Text className={classes.minMenu} size={14} weight="bold">
              {!isEmpty(minMenu) && (
                <>
                  <IconYen />
                  <span>
                    {get(minMenu, 'duration')}
                    分&nbsp;&nbsp;&nbsp;
                    {helpers.numberFormat(get(minMenu, 'price'))}円〜
                  </span>
                </>
              )}
            </Text>
            <Text
              className={classes.seeMoreMenu}
              onClick={onClickSeeMenu}
              size={12}
              weight="bold"
            >
              <IconNote />
              メニュー
            </Text>
          </Group>
          <Group
            className={classes.availableTimeGroup}
            noWrap
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            ref={scrollContainer.ref}
            spacing={0}
          >
            {Object.keys(freeTimeslotsGroup).map((key) => {
              return (
                <Stack
                  className={classes.availableTimeGroupItem}
                  key={key}
                  spacing={6}
                >
                  <Text size={12}>{getTimeslotsText(key)}</Text>
                  <Group noWrap spacing={6}>
                    {freeTimeslotsGroup[key]?.map((time) => (
                      <Box className={classes.availableTime} key={time}>
                        {dayjs(time).format('HH:mm')}
                      </Box>
                    ))}
                  </Group>
                </Stack>
              );
            })}
          </Group>
        </>
      ) : (
        <Group
          noWrap
          position="apart"
          pt={11}
          spacing={16}
          sx={{
            borderTop: '1px solid #dddddd',
          }}
        >
          <Text className={classes.departureLocation} lineClamp={1} size={12}>
            {!isEmpty(departurePoints) && (
              <>
                <IconPerson />
                出発地：
                <DeparturePoint departurePoints={departurePoints} />
              </>
            )}
          </Text>
          <Text
            className={classes.seeMoreMenu}
            onClick={onClickSeeMenu}
            size={12}
            weight="bold"
          >
            <IconNote />
            メニュー
          </Text>
        </Group>
      )}
    </Card>
  );
};

export default TherapistCard;
