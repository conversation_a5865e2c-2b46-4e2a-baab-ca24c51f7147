import { Card, Flex, Skeleton } from '@mantine/core';
import React from 'react';

const CardSkeleton = ({ isSearching }: { isSearching?: boolean }) => {
  return (
    <Card unstyled>
      <Flex align="center" gap={15}>
        <Skeleton circle height={60} sx={{ flexShrink: 0 }} width={60} />
        <Flex direction="column" gap={12} w="100%">
          <Skeleton h={14} width="100%" />
          <Skeleton h={14} width="70%" />
        </Flex>
      </Flex>
      <Flex direction="column" gap={12} mt={13}>
        <Skeleton h={14} width="90%" />
        <Skeleton h={14} width="30%" />
        <Skeleton h={14} width="75%" />
      </Flex>
      <Flex
        justify="space-between"
        mt={15}
        pt={12}
        sx={{ borderTop: '1px solid #dddddd' }}
      >
        <Skeleton h={14} width="45%" />
        <Skeleton h={14} width="25%" />
      </Flex>
      {isSearching && (
        <Flex
          direction="column"
          mt={12}
          pt={12}
          sx={{ borderTop: '1px solid #dddddd' }}
        >
          <Skeleton h={14} w="25%" />
          <Flex gap={8} mt={7} sx={{ '& > *': { flexGrow: 1 } }}>
            <Skeleton h={28} />
            <Skeleton h={28} />
            <Skeleton h={28} />
            <Skeleton h={28} />
            <Skeleton h={28} />
          </Flex>
        </Flex>
      )}
    </Card>
  );
};

export default CardSkeleton;
