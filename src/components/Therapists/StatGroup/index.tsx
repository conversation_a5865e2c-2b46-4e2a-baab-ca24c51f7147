import IconCancelCount from '@icons/icon-cancel-count.svg';
import IconClock from '@icons/icon-clock.svg';
import IconPhoneNotify from '@icons/icon-phone-notify.svg';
import IconQuestion from '@icons/icon-question.svg';
import { Box, Group, Text, ThemeIcon } from '@mantine/core';

import useStyles from './styles';

interface StatGroupProps {
  approvalRate?: number;
  responseRate?: number;
  responseTime?: string;
  sumChargedBookings?: number;
  sumCancelCountBookings?: number;
  onOpenDetail?: () => void;
}

const StatGroup: React.FC<StatGroupProps> = ({
  responseRate,
  responseTime,
  sumChargedBookings = 0,
  sumCancelCountBookings = 0,
  onOpenDetail,
}) => {
  const { classes, cx } = useStyles();
  const isDisplayRating = sumChargedBookings >= 5;
  return (
    <Group className={classes.therapistStatGroup}>
      <ThemeIcon
        className={classes.statDetailIcon}
        color="transparent"
        onClick={onOpenDetail}
        size={24}
      >
        <IconQuestion />
      </ThemeIcon>
      <Box className={classes.therapistStat}>
        <Text className={classes.statTitle}>
          <IconPhoneNotify />
          返答率
        </Text>
        <Text className={classes.statContent}>
          <b>{isDisplayRating && responseRate ? `${responseRate}%` : '--'}</b>
        </Text>
      </Box>
      <Box className={classes.therapistStat}>
        <Text className={classes.statTitle}>
          <IconClock />
          返答時間
        </Text>
        <Text
          className={cx(classes.statContent, {
            'response-time-content': isDisplayRating && responseTime,
          })}
        >
          {isDisplayRating && responseTime ? (
            <>
              平均<b>{responseTime}</b>以内
            </>
          ) : (
            <b>--</b>
          )}
        </Text>
      </Box>
      <Box className={classes.therapistStat}>
        <Text className={classes.statTitle}>
          <IconCancelCount />
          キャンセル
        </Text>
        <Text
          className={cx(classes.statContent, {
            'response-time-content': isDisplayRating,
          })}
        >
          <b>{sumCancelCountBookings || 0}件</b>
        </Text>
      </Box>
    </Group>
  );
};

export default StatGroup;
