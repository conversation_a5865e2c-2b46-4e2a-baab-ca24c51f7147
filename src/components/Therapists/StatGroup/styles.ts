import { createStyles } from '@mantine/core';

const useStyles = createStyles((theme) => ({
  therapistStatGroup: {
    backgroundColor: '#f5f9fb',
    flexWrap: 'nowrap',
    gap: 0,
    height: 96,
    width: '100%',
    position: 'relative',
    [theme.fn.smallerThan('sm')]: {
      height: 76,
      alignSelf: 'stretch',
    },
  },
  therapistStat: {
    flex: '1 1 0',
    textAlign: 'center',
    padding: '0 8px',
    position: 'relative',
    [theme.fn.smallerThan('sm')]: {
      padding: '0',
      flex: '1 1 auto',
      width: 'unset',
    },
    '&:not(:nth-child(3))': {
      maxWidth: 130,
    },
    '&:not(:last-of-type):after': {
      backgroundColor: '#ffffff',
      content: '""',
      width: 2,
      position: 'absolute',
      height: '100%',
      right: 0,
      top: 0,
    },
  },
  statTitle: {
    color: '#43749a',
    fontSize: 16,
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    lineHeight: '24px',
    [theme.fn.smallerThan('sm')]: {
      fontSize: 14,
      lineHeight: '20px',
    },
    svg: {
      width: 16,
      height: 16,
      marginRight: 6,
      [theme.fn.smallerThan('sm')]: {
        width: 14,
        height: 14,
      },
    },
  },
  statContent: {
    color: theme.black,
    fontSize: 24,
    lineHeight: '34px',
    [theme.fn.smallerThan('sm')]: {
      fontSize: 18,
      lineHeight: '24px',
    },
    '&.response-time-content': {
      fontSize: 16,
      b: {
        fontSize: 20,
      },
      [theme.fn.smallerThan('sm')]: {
        fontSize: 14,
        b: {
          fontSize: 16,
        },
      },
    },
  },
  statDetailIcon: {
    position: 'absolute',
    top: 2,
    right: 2,
    cursor: 'pointer',
    transition: '200ms all linear',
    zIndex: 1,
    [theme.fn.smallerThan('sm')]: {
      top: 0,
      right: 0,
    },
    svg: {
      width: 20,
      height: 20,
      [theme.fn.smallerThan('sm')]: {
        width: 16,
        height: 16,
      },
    },
    '&:hover': {
      transform: 'scale(1.2)',
    },
  },
}));

export default useStyles;
