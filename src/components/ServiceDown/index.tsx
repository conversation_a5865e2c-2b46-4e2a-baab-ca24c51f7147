import { Portal } from '@mantine/core';
import { useScrollLock } from '@mantine/hooks';
import { useGlobalState } from 'hooks';
import React from 'react';

const ServiceDown = () => {
  const { serviceDownContent } = useGlobalState();
  useScrollLock(!!serviceDownContent);
  if (!serviceDownContent) return <></>;
  return (
    <Portal>
      <iframe
        src={`data:text/html,${encodeURIComponent(serviceDownContent)}`}
        style={{
          zIndex: 999999,
          overflow: 'hidden',
          padding: 0,
          margin: 0,
          border: 'none',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          width: '100%',
          height: '100%',
          backgroundColor: 'white',
        }}
      >
        いつもご利用いただきありがとうございます。
        <br />
        サービスを一時休止しております。
        <br />
        終了までしばらくお待ちください。
      </iframe>
    </Portal>
  );
};

export default ServiceDown;
