import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { AUTH_ROUTE, COOKIE_KEY } from 'utils/constants';
import type { UserDataCookie, UserTokenCookie } from 'utils/type';

export const middleware = async (req: NextRequest) => {
  const nextUrl = req.nextUrl.clone();

  const referer = req.headers.get('referer');
  const prevUrl = referer ? new URL(referer) : undefined;

  let userToken: UserTokenCookie | undefined;
  if (req.cookies.has(COOKIE_KEY.TOKEN)) {
    userToken = JSON.parse(
      req.cookies.get(COOKIE_KEY.TOKEN)?.value || '{}',
    ) as UserTokenCookie;
  }

  if (userToken?.token) {
    let userData: UserDataCookie | undefined;
    if (req.cookies.has(COOKIE_KEY.USER_DATA)) {
      userData = JSON.parse(
        req.cookies.get(COOKIE_KEY.USER_DATA)?.value || '{}',
      ) as UserDataCookie;
    }
    // Force revoke account
    if (
      userData?.requestingDeleteAccount &&
      nextUrl.pathname !== '/my-page/delete-account-immediately' &&
      nextUrl.pathname !== '/my-page/revoke-account'
    ) {
      nextUrl.pathname = '/my-page/revoke-account';
      const nextResponse = NextResponse.redirect(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }
    // Force complete profile
    if (!userData?.isCompletedProfile && nextUrl.pathname !== '/register') {
      nextUrl.pathname = '/register';
      const nextResponse = NextResponse.redirect(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }
    // Requesting unauth route
    // if (
    //   nextUrl.pathname === '/login' ||
    //   (nextUrl.pathname === '/register' && userData?.isCompletedProfile)
    // ) {
    //   nextUrl.pathname = '/';
    //   const nextResponse = NextResponse.redirect(nextUrl);
    //   nextResponse.headers.set('x-middleware-cache', 'no-cache');
    //   return nextResponse;
    // }
  }

  if (!userToken?.token) {
    // Requesting auth route
    if (
      nextUrl.pathname.startsWith('/my-page') ||
      nextUrl.pathname.startsWith('/booking') ||
      nextUrl.pathname === '/register/complete'
    ) {
      nextUrl.searchParams.set(
        'referer',
        `${nextUrl.pathname}${nextUrl.search}`,
      );
      nextUrl.pathname = '/login';
      const nextResponse = NextResponse.redirect(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }

    // Requesting login
    if (
      AUTH_ROUTE.includes(nextUrl.pathname) &&
      !nextUrl.searchParams.has('referer')
    ) {
      const previousPath = prevUrl?.searchParams.has('referer')
        ? prevUrl.searchParams.get('referer') || '/'
        : `${prevUrl?.pathname || '/'}${prevUrl?.search || ''}`;
      nextUrl.searchParams.set('referer', previousPath);
      const nextResponse = NextResponse.redirect(nextUrl);
      nextResponse.headers.set('x-middleware-cache', 'no-cache');
      return nextResponse;
    }
  }

  return NextResponse.next();
};

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - server (Proxy server)
     * - icons
     * - images
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|server|icons|images|.well-known|_next/static|_next/image|favicon.ico).*)',
  ],
};
