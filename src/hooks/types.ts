import type { BuildingType } from 'components/Profile/AddressHistoryForm/schema';
import type { Timestamp } from 'firebase/firestore';
import type { ICoupon } from 'models/booking';
import type { IMenuItem } from 'models/therapist';

export interface IListItem {
  _id: string;
  value: string;
}

export interface IListResult<T> {
  data: T[];
  coupons?: T[];
  total: number;
  limit: number;
  page: number;
  perPage: number;
  lastPage: number;
}

// Define data needed for booking create
export interface ICreateBooking {
  areaCodes: string[];
  menus: IMenuItem[];
  dateBooking: string;
  areaNames?: string;
  therapistId: string;
  therapistName?: string;
  therapistBusy?: boolean;
  cardId: string;
  address: string;
  buildingType?: BuildingType | '';
  nameplate?: string;
  buildingDetails?: string;
  accessMethod?: string;
  coupon?: ICoupon;
  couponError?: string;
  customerNote?: string;
  parkingNote?: string;
  isGoogle?: boolean;
  point?: number;
}

export interface IMessageItem {
  content?: string;
  created?: Timestamp;
  readByUsers?: string;
  senderID?: string;
  senderName?: string;
  device?: 'Android' | 'iOS' | 'Web';
  senderType?: 'customer' | 'therapist';
}

export interface IUnreadBooking {
  unreadCount?: number;
}

export interface IFirestoreUser {
  bookingIds?: string[];
  bookingsHaveNewMessages?: number;
  displayName?: string;
  lastBookingId?: string;
  lastSenderId?: string;
  readMessagesTime?: Timestamp;
  updatedTime?: Timestamp;
  userType?: 'customer' | 'therapist';
  userId?: string;
}
