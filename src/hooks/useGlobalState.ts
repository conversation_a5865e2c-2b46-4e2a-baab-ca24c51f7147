import type { Call, Device } from '@twilio/voice-sdk';
import { create } from 'zustand';

import type { IFirestoreUser } from './types';

export interface GlobalState {
  openedDrawer: boolean;
  setOpenDrawer: (payload: boolean) => void;
  prevAsPath: string;
  setPrevAsPath: (payload: string) => void;
  firebaseUser: IFirestoreUser;
  setFirebaseUser: (fn: (prev: IFirestoreUser) => IFirestoreUser) => void;
  call?: Call;
  setCall: (payload: Call | undefined) => void;
  setDevice: (payload: Device | undefined) => void;
  device?: Device;
  serviceDownContent?: string;
}
const useGlobalState = create<GlobalState>((set) => ({
  openedDrawer: false,
  setOpenDrawer: (payload) => set({ openedDrawer: payload }),
  prevAsPath: '/',
  setPrevAsPath: (payload) => set({ prevAsPath: payload }),
  firebaseUser: {},
  setFirebaseUser: (fn: (prev: IFirestoreUser) => IFirestoreUser) => {
    set((state) => ({ firebaseUser: fn(state.firebaseUser) }));
  },
  call: undefined,
  setCall: (payload) => set({ call: payload }),
  device: undefined,
  setDevice: (payload) => set({ device: payload }),
  serviceDownContent: undefined,
}));

export const setServiceDownContent = (content: string) => {
  useGlobalState.setState({ serviceDownContent: content });
};

export default useGlobalState;
