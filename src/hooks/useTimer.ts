/* eslint-disable react-hooks/exhaustive-deps */
import { useInterval } from '@mantine/hooks';
import { useCallback, useEffect, useState } from 'react';

const useTimer = ({
  countEnd,
  intervalMs = 1000,
}: Partial<{ countEnd: number; intervalMs?: number }>) => {
  const [seconds, setSeconds] = useState(0);
  const interval = useInterval(() => setSeconds((s) => s + 1), intervalMs);

  useEffect(() => {
    if (countEnd && seconds === countEnd) {
      interval.stop();
    }
  }, [countEnd, interval, seconds]);

  const start = useCallback(() => {
    setSeconds(0);
    interval.start();
  }, []);

  const stop = useCallback(() => {
    setSeconds(0);
    interval.stop();
  }, []);

  return {
    isDone: seconds === 0,
    count: seconds,
    start,
    stop,
  };
};

export default useTimer;
