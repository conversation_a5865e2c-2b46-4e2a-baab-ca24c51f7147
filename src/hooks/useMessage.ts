import type { Unsubscribe } from 'firebase/firestore';
import {
  collection,
  doc,
  getDocs,
  limit,
  onSnapshot,
  orderBy,
  query,
  runTransaction,
  serverTimestamp,
  startAfter,
  Timestamp,
  where,
  writeBatch,
} from 'firebase/firestore';
import { debounce, get } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import dayjs from 'utils/dayjs';
import { db } from 'utils/firebase';
import notification from 'utils/notification';

import type { IMessageItem } from './types';
import useGlobalState from './useGlobalState';

const MESSAGE_LIMIT = 20;

const useMessage = ({
  bookingId,
  bookingDate,
  senderId,
  senderName,
  receiverId,
  receiverName,
  inView,
}: {
  inView?: boolean;
  senderId?: string;
  senderName?: string;
  receiverId?: string;
  receiverName?: string;
  bookingDate?: string;
  bookingId?: string;
}) => {
  const { firebaseUser } = useGlobalState();
  const [channelDetail, setChannelDetail] = useState<Record<string, unknown>>(
    {},
  );
  const [messageResponse, setMessageResponse] = useState<{
    messageIds: string[];
    messages: Record<string, IMessageItem>;
  }>({
    messageIds: [],
    messages: {},
  });
  const [hasMoreMessages, setHasMoreMessages] = useState(false);
  const [isFirstFetch, setIsFirstFetch] = useState(true);
  const [lastMessageTime, setLastMessageTime] = useState(Timestamp.now());

  const handleCreateChannel = useCallback(async () => {
    try {
      if (!bookingId || !bookingDate || !receiverId) return;
      const channelDoc = doc(db, 'channels', bookingId);
      const receiverDoc = doc(db, 'users', receiverId);
      await runTransaction(db, async (transaction) => {
        const channelDataSnap = await transaction.get(channelDoc);
        const receiverDataSnap = await transaction.get(receiverDoc);
        if (!channelDataSnap.exists()) {
          transaction.set(channelDoc, {
            name: bookingId,
            bookingDate: Timestamp.fromDate(new Date(bookingDate)),
          });
        }
        if (!receiverDataSnap.exists()) {
          transaction.set(receiverDoc, {
            userId: receiverId,
            displayName: receiverName,
            userType: 'therapist',
          });
        }
      });
    } catch (error) {
      notification.show({
        type: 'error',
        message: get(error, 'message', ''),
      });
    }
  }, [bookingDate, bookingId, receiverId, receiverName]);

  const handleReadThread = useMemo(
    () =>
      debounce(async () => {
        try {
          if (!bookingId || !senderId || !receiverId) return;
          const channelDoc = doc(db, 'channels', bookingId);
          const threadRef = collection(channelDoc, 'thread');
          const senderDoc = doc(db, 'users', senderId);
          const senderThreadRef = collection(senderDoc, 'thread');

          await runTransaction(db, async (transaction) => {
            const therapistMessages = await getDocs(
              query(threadRef, where('readByUsers', '==', receiverId)),
            );
            if (therapistMessages.size) {
              // Update all therapist messages as read
              therapistMessages.forEach((message) => {
                transaction.update(doc(threadRef, message.id), {
                  readByUsers: `${receiverId},${senderId}`,
                });
              });
              // Update unreadCount of customer
              transaction.set(
                doc(senderThreadRef, bookingId),
                {
                  unreadCount: 0,
                  bookingId,
                },
                { merge: false },
              );
            }
            if (firebaseUser?.bookingIds?.includes(bookingId)) {
              let count = 0;
              const bookingIds: string[] = [];
              const senderThreadDocs = await getDocs(
                query(senderThreadRef, where('unreadCount', '>', 0)),
              );
              senderThreadDocs.docs.forEach(({ id }) => {
                if (id !== bookingId) {
                  count += 1;
                  bookingIds.push(id);
                }
              });
              transaction.update(senderDoc, {
                bookingsHaveNewMessages: count,
                readMessagesTime: serverTimestamp(),
                bookingIds,
              });
            }
          });
        } catch (error) {
          notification.show({
            type: 'error',
            message: get(error, 'message', ''),
          });
        }
      }, 500),
    [bookingId, firebaseUser?.bookingIds, receiverId, senderId],
  );

  const handleUpdateUnreadThread = useMemo(
    () =>
      debounce(async () => {
        try {
          if (!bookingId || !senderId || !receiverId || !senderName) return;
          const threadRef = collection(db, 'channels', bookingId, 'thread');
          const customerMessages = await getDocs(
            query(threadRef, where('readByUsers', '==', senderId)),
          );
          const receiverDoc = doc(db, 'users', receiverId);
          const receiverThreadRef = collection(receiverDoc, 'thread');

          await runTransaction(db, async (transaction) => {
            if (customerMessages.size) {
              transaction.set(
                doc(receiverThreadRef, bookingId),
                {
                  unreadCount: customerMessages.size,
                  bookingId,
                  senderId,
                  senderName,
                  updatedTime: serverTimestamp(),
                },
                { merge: false },
              );
            } else {
              transaction.set(
                doc(receiverThreadRef, bookingId),
                {
                  unreadCount: 0,
                  bookingId,
                },
                {
                  merge: false,
                },
              );
            }
            const receiverThreadDocs = await getDocs(
              query(receiverThreadRef, where('unreadCount', '>', 0)),
            );
            let count = receiverThreadDocs.size;
            const bookingIds: string[] = receiverThreadDocs.docs.map(
              ({ id }) => id,
            );
            if (!bookingIds.includes(bookingId)) {
              count += 1;
              bookingIds.push(bookingId);
            }
            transaction.update(receiverDoc, {
              lastBookingId: bookingId,
              lastSenderId: senderId,
              lastSenderName: senderName,
              bookingIds,
              bookingsHaveNewMessages: count,
              ...(!customerMessages.size
                ? { readMessagesTime: serverTimestamp() }
                : { updatedTime: serverTimestamp() }),
            });
          });
        } catch (error) {
          notification.show({
            type: 'error',
            message: get(error, 'message', ''),
          });
        }
      }, 500),
    [bookingId, receiverId, senderId, senderName],
  );

  const handleSendMessage = useCallback(
    async (data: { content: string }) => {
      if (!bookingId || !senderId || !senderName) return;
      const batch = writeBatch(db);
      const threadRef = collection(db, 'channels', bookingId, 'thread');
      const timeStamp = Timestamp.now();
      // add message
      batch.set(doc(threadRef), {
        ...data,
        senderID: senderId,
        senderName,
        readByUsers: senderId,
        senderType: 'customer',
        device: 'Web',
        created: timeStamp,
      });

      await batch.commit();
      handleUpdateUnreadThread();
    },
    [bookingId, handleUpdateUnreadThread, senderId, senderName],
  );

  useEffect(() => {
    let unsubscribeThread: Unsubscribe;
    if (bookingId) {
      const threadDoc = doc(db, 'channels', bookingId);
      unsubscribeThread = onSnapshot(threadDoc, (document) => {
        const channelData = document?.data();
        setChannelDetail(channelData || {});
      });
    }
    return () => {
      if (unsubscribeThread) {
        unsubscribeThread();
      }
    };
  }, [bookingId]);

  useEffect(() => {
    if (bookingId !== channelDetail?.name) {
      setMessageResponse(() => ({
        messageIds: [],
        messages: {},
      }));
      setHasMoreMessages(false);
      setIsFirstFetch(true);
      setLastMessageTime(Timestamp.now());
    }
  }, [bookingId, channelDetail?.name]);

  useEffect(() => {
    let unsubscribeThread: Unsubscribe;
    if (bookingId && senderId) {
      const threadRef = collection(db, 'channels', bookingId, 'thread');
      const threadQuery = query(
        threadRef,
        orderBy('created', 'desc'),
        limit(MESSAGE_LIMIT),
      );
      unsubscribeThread = onSnapshot(threadQuery, (querySnapshot) => {
        const originalMessageIds: string[] = [];
        const messages: Record<string, IMessageItem> = {};
        querySnapshot.docChanges().forEach((docChange) => {
          const data = docChange.doc.data();
          const docId = docChange.doc.id;
          if (docChange.type === 'added') {
            originalMessageIds.push(docId);
            messages[docId] = data as IMessageItem;
          }
          if (docChange.type === 'modified') {
            originalMessageIds.push(docId);
            messages[docId] = data as IMessageItem;
          }
        });
        if (originalMessageIds.length) {
          setMessageResponse(
            ({ messageIds: prevMessageIds, messages: prevMessages }) => {
              const messageIds: string[] = [];
              originalMessageIds.forEach((messageId) => {
                if (!prevMessages[messageId]) {
                  messageIds.push(messageId);
                }
              });

              let newMessageIds: string[] = prevMessageIds;
              const oldestMessageId = prevMessageIds[prevMessageIds.length - 1];
              // If blank chat list
              if (!prevMessageIds[0]) {
                newMessageIds = messageIds.concat(prevMessageIds);
              } else if (
                messageIds[0] &&
                messages[messageIds[0]] &&
                oldestMessageId
              ) {
                const commingMessageTime = dayjs(
                  messages[messageIds[0]]?.created?.toDate(),
                );
                const oldestMessageTime = dayjs(
                  prevMessages[oldestMessageId]?.created?.toDate(),
                );
                // Determine the comming message is lastest message or oldest message
                if (oldestMessageTime.diff(commingMessageTime) > 0) {
                  newMessageIds = prevMessageIds.concat(messageIds);
                } else {
                  newMessageIds = messageIds.concat(prevMessageIds);
                }
              }
              const newMessages = { ...prevMessages, ...messages };
              // firstFetch is used to show/not show loading more icon at first time
              if (isFirstFetch) {
                setHasMoreMessages(messageIds.length >= MESSAGE_LIMIT);
                setIsFirstFetch(false);
              }
              setLastMessageTime(
                newMessages[newMessageIds[newMessageIds.length - 1] || '']
                  ?.created || Timestamp.now(),
              );
              return {
                messageIds: newMessageIds,
                messages: newMessages,
              };
            },
          );
        } else {
          setIsFirstFetch(false);
          setHasMoreMessages(false);
        }
      });
    }
    return () => {
      if (unsubscribeThread) {
        unsubscribeThread();
      }
    };
  }, [bookingId, isFirstFetch, senderId]);

  useEffect(() => {
    const getMoreMessages = async () => {
      if (bookingId) {
        const threadRef = collection(db, 'channels', bookingId, 'thread');
        const queryMessages = await getDocs(
          query(
            threadRef,
            orderBy('created', 'desc'),
            startAfter(lastMessageTime),
            limit(MESSAGE_LIMIT),
          ),
        );
        const messageIds: string[] = [];
        const messages: Record<string, IMessageItem> = {};
        queryMessages.forEach((docData) => {
          messageIds.push(docData.id);
          messages[docData.id] = docData.data() as IMessageItem;
        });
        setHasMoreMessages(messageIds.length >= MESSAGE_LIMIT);
        if (messageIds.length > 0) {
          setMessageResponse(
            ({ messageIds: prevMessageIds, messages: prevMessages }) => {
              if (prevMessageIds.length) {
                const newMessages = { ...prevMessages, ...messages };
                const newMessageIds = prevMessageIds.concat(messageIds);
                setLastMessageTime(
                  newMessages[newMessageIds[newMessageIds.length - 1] || '']
                    ?.created || Timestamp.now(),
                );
                return {
                  messageIds: newMessageIds,
                  messages: newMessages,
                };
              }
              return {
                messageIds: prevMessageIds,
                messages: prevMessages,
              };
            },
          );
        }
      }
    };
    if (inView && hasMoreMessages) {
      getMoreMessages();
    }
  }, [bookingId, hasMoreMessages, inView, lastMessageTime]);

  return {
    ...messageResponse,
    hasMoreMessages,
    isFirstFetch,
    channelDetail,
    handleSendMessage,
    handleCreateChannel,
    handleReadThread,
  };
};

export default useMessage;
