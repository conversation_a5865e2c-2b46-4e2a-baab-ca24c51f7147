import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import useFetchData from 'hooks/useFetchData';
import type { ICustomer } from 'models/auth';
import { authQuery } from 'models/auth';
import Helper from 'utils/helpers';

export interface Options<TQueryFnData = unknown, TData = TQueryFnData>
  extends Omit<
    UseQueryOptions<TQueryFnData, unknown, TData, QueryKey>,
    'queryFn' | 'queryKey'
  > {
  customParams?: Record<string, unknown>;
}

const useUser = (options?: Options<ICustomer>) => {
  const { enabled = true, ...otherOptions } = options || {};
  const webCookie = Helper.getWebCookie();

  return useFetchData<ICustomer>({
    ...authQuery.currentUser,
    enabled: enabled && !!webCookie.token,
    staleTime: Infinity,
    ...otherOptions,
  });
};

export default useUser;
