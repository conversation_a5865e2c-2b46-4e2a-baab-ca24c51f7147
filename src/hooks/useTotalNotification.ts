import type { ISummaryNotification } from 'models/notification';
import { notificationQuery } from 'models/notification';
import helpers from 'utils/helpers';

import useFetchData from './useFetchData';
import useUser from './useUser';

const useTotalNotification = () => {
  const { data: currentUserData } = useUser();

  return useFetchData<ISummaryNotification>({
    ...notificationQuery.getSummaryNotification,
    enabled: !!currentUserData,
    refetchInterval: 15 * 1000,
    staleTime: 15 * 1000,
    onSuccess: (response) => {
      helpers.setUserData({
        unreadNotifications: response.total || 0,
      });
    },
  });
};

export default useTotalNotification;
