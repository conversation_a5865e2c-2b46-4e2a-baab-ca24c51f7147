import { openContextModal } from '@mantine/modals';
import { useRouter } from 'next/router';
import { LOCAL_STORAGE_KEY } from 'utils/constants';
import helpers, { eventLog, setUserId } from 'utils/helpers';
import getQueryClient from 'utils/queryClient';

const useLogout = () => {
  const router = useRouter();
  const queryClient = getQueryClient();
  return {
    logout: async (alert = true, redirect = true) => {
      eventLog('logout');
      setUserId(null);
      if (typeof window !== 'undefined') {
        localStorage.removeItem(LOCAL_STORAGE_KEY.BOOKING);
        localStorage.removeItem(LOCAL_STORAGE_KEY.BOOKING_POLICY_CHECKED);
      }
      setTimeout(() => helpers.removeWebCookie());
      queryClient
        .getQueryCache()
        .findAll(['currentUser'])
        .forEach((query) => query.reset());
      if (alert) {
        openContextModal({
          modal: 'AlertModal',
          size: 630,
          innerProps: {
            title: 'ログアウト',
            content: 'ログアウトしました。',
          },
          centered: true,
          onClose: () => {
            if (redirect) router.push(process.env.NEXT_PUBLIC_LP_DOMAIN || '/');
          },
        });
      } else if (redirect)
        router.push(process.env.NEXT_PUBLIC_LP_DOMAIN || '/');
    },
  };
};

export default useLogout;
