import { usePrevious } from '@mantine/hooks';
import type { Call } from '@twilio/voice-sdk';
import { Device } from '@twilio/voice-sdk';
import { getCookie, setCookie } from 'cookies-next';
import { useFetchData, useGlobalState, useUser } from 'hooks';
import { resourceQuery } from 'models/resource';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { COOKIE_KEY } from 'utils/constants';
import helpers from 'utils/helpers';

const useTwilioDevice = () => {
  const { setDevice, device, setCall } = useGlobalState();
  const { isReady } = useRouter();

  const { data: user } = useUser();
  const prevUserId = usePrevious(user?._id);
  const { data: token } = useFetchData<{ token: string | null }, string | null>(
    {
      placeholderData: {
        token: (getCookie(COOKIE_KEY.TWILIO_TOKEN) as string) || null,
      },
      ...resourceQuery.voiceToken,
      enabled: !!user && !getCookie(COOKIE_KEY.TWILIO_TOKEN) && isReady,
      onSuccess: (data) => {
        setCookie(COOKIE_KEY.TWILIO_TOKEN, data, {
          path: '/',
          maxAge: 86400,
        });
      },
      select: (data) => data.token,
    },
  );

  useEffect(() => {
    if (token && typeof window !== 'undefined') {
      const twilioDevice = new Device(token);
      setDevice(twilioDevice);
    }
  }, [setDevice, token]);

  useEffect(() => {
    if (prevUserId && !user?._id && device) {
      setDevice(undefined);
      device?.destroy();
      helpers.removeWebCookie();
    }
  }, [device, prevUserId, setDevice, user?._id]);

  useEffect(() => {
    if (device) {
      if (device.state === 'unregistered') {
        device.register();
      }
      device.on('error', (error) => {
        // TO DO: Improve error handling
        console.log(`${error.message} (${error.code})`);
      });
      device.on('incoming', (twilioCall: Call) => {
        setCall(twilioCall);
        twilioCall.on('reject', () => {
          setCall(undefined);
        });
        twilioCall.on('disconnect', () => {
          setCall(undefined);
        });
        twilioCall.on('cancel', () => {
          setCall(undefined);
        });
      });
    }
    return () => {
      device?.destroy();
    };
  }, [device, setCall]);
};

export default useTwilioDevice;
