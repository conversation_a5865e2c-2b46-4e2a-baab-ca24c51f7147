import { useInterval } from '@mantine/hooks';
import { useEffect, useState } from 'react';

const useCountdown = ({
  countStart = 60,
  intervalMs = 1000,
}: Partial<{ countStart: number; intervalMs: number }>) => {
  const [firstEncounter, setFirstEncounter] = useState(true);
  const [seconds, setSeconds] = useState(countStart);
  const interval = useInterval(() => setSeconds((s) => s - 1), intervalMs);

  useEffect(() => {
    if (seconds === 0) {
      if (firstEncounter) setFirstEncounter(false);
      interval.stop();
    }
  }, [interval, seconds, firstEncounter]);

  return {
    firstEncounter,
    isDone: seconds === 0,
    count: seconds,
    start: () => {
      setSeconds(countStart);
      interval.start();
    },
  };
};

export default useCountdown;
