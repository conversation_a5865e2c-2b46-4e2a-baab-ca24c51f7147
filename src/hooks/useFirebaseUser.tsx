import IconIncomingMessage from '@icons/icon-incoming-message.svg';
import { Button } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import type { Unsubscribe } from 'firebase/firestore';
import { collection, doc, onSnapshot, query, where } from 'firebase/firestore';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { db } from 'utils/firebase';
import helpers from 'utils/helpers';
import notification from 'utils/notification';

import type { IFirestoreUser } from './types';
import useGlobalState from './useGlobalState';

const useFirebaseUser = ({ userId }: { userId?: string }) => {
  const { firebaseUser, setFirebaseUser } = useGlobalState();
  const { pathname, query: routerQuery, push } = useRouter();
  const bookingId =
    pathname === '/booking/[id]' && typeof routerQuery.id === 'string'
      ? routerQuery.id
      : undefined;
  const firedRef = useRef(false);

  useEffect(() => {
    let unsubscribe: Unsubscribe;
    let unsubscribeThread: Unsubscribe;
    if (userId) {
      const userDoc = doc(db, 'users', userId);
      unsubscribe = onSnapshot(userDoc, (snapShot) => {
        const userData = snapShot.data() as IFirestoreUser;
        setFirebaseUser(() => {
          if (userData?.bookingsHaveNewMessages && !firedRef.current) {
            openContextModal({
              id: 'message-alert-modal',
              modal: 'AlertModal',
              size: 630,
              innerProps: {
                title: 'ご予約中のセラピストより\n新着メッセージがあります',
                content: '未読のチャットを\n以下よりご確認お願いいたします。',
                hasOkBtn: true,
                confirmText: '確認する',
                onConfirm: () => {
                  if (userData?.bookingIds?.length === 1) {
                    push({
                      pathname: '/booking/[id]',
                      query: {
                        id: userData?.bookingIds[0],
                        chat: true,
                      },
                    });
                  } else {
                    push('/my-page/booking-history');
                  }
                },
                icon: <IconIncomingMessage />,
              },
              centered: true,
              styles: {
                root: {
                  '.modal-icon': {
                    backgroundColor: '#43749a',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    svg: {
                      color: 'white',
                      width: 44,
                      height: 44,
                      '@media (max-width: 768px)': {
                        width: 32,
                        height: 32,
                      },
                    },
                  },
                },
              },
            });
          }
          firedRef.current = true;
          helpers.setUserData({
            unreadMessages: userData?.bookingsHaveNewMessages || 0,
          });
          return userData;
        });
      });
      const userThreadRef = collection(userDoc, 'thread');
      const userThreadQuery = query(
        userThreadRef,
        where('unreadCount', '>', 0),
      );
      unsubscribeThread = onSnapshot(userThreadQuery, (querySnapshot) => {
        querySnapshot.docChanges().forEach((docChange) => {
          if (docChange.type === 'modified' && bookingId !== docChange.doc.id) {
            notification.show({
              message: (
                <>
                  ご予約中のセラピストより
                  <br />
                  新着メッセージがあります。
                  <Button
                    component={Link}
                    href={{
                      pathname: '/booking/[id]',
                      query: {
                        id: docChange.doc.id,
                        chat: true,
                      },
                    }}
                    onClick={() => {
                      notification.hide(docChange.doc.id);
                    }}
                    sx={{
                      width: 96,
                      height: 30,
                      '@media (max-width: 768px)': {
                        width: 72,
                        padding: 0,
                      },
                    }}
                  >
                    確認する
                  </Button>
                </>
              ),
              type: 'message',
              id: docChange.doc.id,
            });
          }
        });
      });
    } else {
      setFirebaseUser(() => ({}));
    }
    return () => {
      if (unsubscribe) {
        unsubscribe();
        unsubscribeThread();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  return firebaseUser;
};

export default useFirebaseUser;
