import type { UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { IListResult } from 'hooks/types';
import { useRouter } from 'next/router';
import request from 'utils/request';
import type { ExtendOptions } from 'utils/type';

interface Options<
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
> extends Omit<
      UseQueryOptions<
        IListResult<TQueryFnData>,
        TError,
        IListResult<TData>,
        unknown[]
      >,
      'queryFn' | 'queryKey'
    >,
    ExtendOptions {
  apiUrl: string;
}

const getSortString = (orderBy: string, order: string): string => {
  if (order === 'true') {
    return `${orderBy}.desc`;
  }
  return `${orderBy}.asc`;
};

const useFetchList = <
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
>(
  options: Options<TQueryFnData, TData, TError>,
) => {
  const {
    queryKey,
    apiUrl,
    customParams,
    omitKeys,
    method = 'GET',
    ...otherOptions
  } = options;
  const router = useRouter();
  const { ...searchParams } = router.query;

  const params = {
    ...searchParams,
    page: searchParams.page ? Number(searchParams.page) : 1,
    limit: searchParams.limit ? Number(searchParams.limit) : 10,
    ...customParams, // Do not change the order. Please pass another param if you want to override the params
  };

  const formatParams = (_params: Record<string, unknown>) => {
    const formattedParams = { ..._params };
    if (formattedParams.orderBy && formattedParams.order) {
      formattedParams.sort = getSortString(
        formattedParams.orderBy as string,
        formattedParams.order as string,
      );
      delete formattedParams.order;
      delete formattedParams.orderBy;
    }
    if (omitKeys) {
      omitKeys.forEach((key) => {
        delete formattedParams[key];
      });
    }
    return formattedParams;
  };

  const formattedParams = formatParams(params);
  const { data: response, ...rest } = useQuery({
    queryKey: queryKey
      ? [
          ...queryKey,
          JSON.stringify(formattedParams, Object.keys(formattedParams).sort()),
        ]
      : [apiUrl, formattedParams],
    queryFn: async () => {
      const { data: result } = await request<IListResult<TQueryFnData>>({
        url: apiUrl,
        data: formattedParams,
        method,
      });
      return result;
    },
    suspense: false,
    keepPreviousData: true,
    enabled: router.isReady,
    ...otherOptions,
  });

  return {
    list: response?.data || response?.coupons || [],
    total: response?.total || 0,
    page: response?.page || 1,
    lastPage: response?.lastPage || 1,
    perPage: response?.perPage || 20,
    ...rest,
  };
};
export default useFetchList;
