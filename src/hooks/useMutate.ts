import type { UseMutationOptions } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import notification from 'utils/notification';
import request from 'utils/request';

interface Options<TPayload, TData>
  extends Omit<UseMutationOptions<TData, unknown, TPayload>, 'mutationFn'> {
  apiUrl: string | ((params: TPayload) => string);
  method?: string;
  defaultNotification?: boolean;
  successMessage?: string;
  omitKeys?: string[];
}

const useMutate = <TPayload = unknown, TData = unknown>(
  options: Options<TPayload, TData>,
) => {
  const {
    apiUrl,
    defaultNotification,
    method = 'POST',
    successMessage,
    omitKeys,
    ...otherOptions
  } = options;
  return useMutation({
    mutationFn: async (params: TPayload) => {
      const url =
        typeof apiUrl === 'string' ? apiUrl : apiUrl(params as TPayload);

      const formatParams = { ...params } as Record<string, unknown>;

      if (omitKeys) {
        omitKeys.forEach((key) => {
          delete formatParams[key];
        });
      }

      const { data } = await request({
        url,
        data: formatParams,
        method,
      });
      return data;
    },
    onSuccess: () => {
      if (defaultNotification || successMessage) {
        notification.show({
          type: 'success',
          message: successMessage || '完了しました。',
        });
      }
    },
    ...otherOptions,
  });
};

export default useMutate;
