import { useRouter } from 'next/router';
import { useRef } from 'react';

import useGlobalState from './useGlobalState';

const usePreviousRoute = () => {
  const router = useRouter();
  const ref = useRef<string | null>(null);
  const { setPrevAsPath } = useGlobalState();

  router.events?.on('routeChangeStart', () => {
    ref.current = router.asPath;
    setPrevAsPath(router.asPath);
  });

  return ref.current;
};

export default usePreviousRoute;
