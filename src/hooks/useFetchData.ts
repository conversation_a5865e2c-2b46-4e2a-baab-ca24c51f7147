import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import request from 'utils/request';
import type { ExtendOptions } from 'utils/type';

interface Options<
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
> extends Omit<
      UseQueryOptions<TQueryFnData, TError, TData, QueryKey>,
      'queryFn' | 'queryKey'
    >,
    ExtendOptions {
  apiUrl: string;
}

const useFetchData = <
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
>({
  queryKey,
  apiUrl,
  method = 'GET',
  customParams,
  axiosConfig,
  ...queryOptions
}: Options<TQueryFnData, TData, TError>) => {
  const { isReady } = useRouter();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const { data: result } = await request({
        url: apiUrl,
        data: customParams,
        method,
        config: axiosConfig,
      });
      return result || null;
    },
    enabled: isReady,
    ...queryOptions,
  });
};

export default useFetchData;
