import type {
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import type {
  ApplicationVerifier,
  Auth,
  AuthError,
  ConfirmationResult,
} from 'firebase/auth';
import { signInWithPhoneNumber } from 'firebase/auth';

const useAuthSignInWithPhoneNumber = (
  auth: Auth,
  useMutationOptions?: UseMutationOptions<
    ConfirmationResult,
    AuthError,
    { phoneNumber: string; appVerifier: ApplicationVerifier }
  >,
): UseMutationResult<
  ConfirmationResult,
  AuthError,
  { phoneNumber: string; appVerifier: ApplicationVerifier }
> => {
  return useMutation<
    ConfirmationResult,
    AuthError,
    { phoneNumber: string; appVerifier: ApplicationVerifier }
  >(({ phoneNumber, appVerifier }) => {
    return signInWithPhoneNumber(auth, phoneNumber, appVerifier);
  }, useMutationOptions);
};

export default useAuthSignInWithPhoneNumber;
