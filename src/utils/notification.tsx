import IconError from '@icons/icon-error.svg';
import IconMessage from '@icons/icon-incoming-message.svg';
import IconInfo from '@icons/icon-info.svg';
import IconSuccess from '@icons/icon-success.svg';
import IconWarning from '@icons/icon-warning.svg';
import type { NotificationProps } from '@mantine/notifications';
import {
  cleanNotifications,
  hideNotification,
  showNotification,
  updateNotification,
} from '@mantine/notifications';

const typeConfig: Record<string, Partial<NotificationProps>> = {
  success: {
    icon: <IconSuccess />,
    styles: {
      root: {
        border: 'solid 1px #008167',
        backgroundColor: '#cce6e0',
      },
      closeButton: {
        color: '#008167',
        backgroundColor: '#cce6e0',
      },
    },
  },
  error: {
    icon: <IconError />,
    styles: {
      root: {
        border: 'solid 1px #bf2020',
        backgroundColor: '#f2d2d2',
      },
      closeButton: {
        color: '#bf2020',
        backgroundColor: '#f2d2d2',
      },
    },
  },
  info: {
    icon: <IconInfo />,
    styles: {
      root: {
        border: 'solid 1px #43749a',
        backgroundColor: '#d9e3eb',
      },
      icon: {
        color: '#43749a',
      },
      closeButton: {
        color: '#43749a',
        backgroundColor: '#d9e3eb',
      },
    },
  },
  warning: {
    icon: <IconWarning />,
    styles: {
      root: {
        border: 'solid 1px #e39300',
        backgroundColor: '#fae9cc',
      },
      closeButton: {
        color: '#e39300',
        backgroundColor: '#fae9cc',
      },
    },
  },
  message: {
    icon: <IconMessage />,
    styles: {
      root: {
        padding: '14px 24px !important',
        border: 'solid 1px #43749a',
        backgroundColor: '#d9e3eb',
        maxWidth: 450,
        width: '100%',
        position: 'fixed',
        top: 125,
        right: 46,
        '@media (max-width: 768px)': {
          maxWidth: 343,
          top: 71,
          right: 16,
          padding: '9px 12px !important',
        },
        '@media (max-width: 400px)': {
          maxWidth: 'calc(100% - 32px)',
        },
      },
      icon: {
        color: '#43749a',
        marginRight: 14,
        '@media (max-width: 400px)': {
          marginRight: 8,
        },
        svg: {
          width: 24,
          height: 24,
          '@media (max-width: 400px)': {
            width: 20,
            height: 20,
          },
        },
      },
      description: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        lineHeight: 1.5,
        '@media (max-width: 768px)': {
          fontSize: 12,
        },
      },
      closeButton: {
        color: '#43749a',
        backgroundColor: '#d9e3eb',
      },
    },
  },
};

const setDefaultId = (options: NotificationProps) => {
  if (typeof options.title === 'string') {
    return options.title;
  }
  if (typeof options.message === 'string') {
    return options.message;
  }
  return undefined;
};

const notifications = {
  show: ({
    type = 'info',
    ...options
  }: { type?: string } & NotificationProps) =>
    showNotification({
      id: setDefaultId(options),
      ...typeConfig[type],
      ...options,
    }),
  update: (options: { id: string } & NotificationProps) =>
    updateNotification(options),
  hide: (id: string) => hideNotification(id),
  clean: cleanNotifications,
  cleanQueue: cleanNotifications,
};

export default notifications;
