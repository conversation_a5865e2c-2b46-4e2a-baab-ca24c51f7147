/* eslint-disable no-useless-escape */
/* eslint-disable no-control-regex */
import IconAvatar from '@icons/icon-avatar.svg';
import IconClock from '@icons/icon-clock.svg';
import IconLogin from '@icons/icon-login.svg';
import IconLogout from '@icons/icon-logout.svg';
import IconMail from '@icons/icon-mail.svg';
import IconNewTab from '@icons/icon-new-tab.svg';
import IconRegister from '@icons/icon-register.svg';
import Amex from '@icons/payment-amex.svg';
import DinersClub from '@icons/payment-diners.svg';
import JCB from '@icons/payment-jcb.svg';
import MasterCard from '@icons/payment-mastercard.svg';
import Visa from '@icons/payment-visa.svg';

export const REGEX = {
  URL: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/,
  PHONE: /^(\+?[0-9０-９]{9,13})$/,
  EMAIL:
    /^(?:[a-z0-9!#$%&*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/,
  EMOJI:
    /\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFF\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFE\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFD\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFC\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69])|\uD83E\uDEF1\uD83C\uDFFB\u200D\uD83E\uDEF2)(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|[\u2695\u2696\u2708]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])))|\u200D(?:\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?|\u200D(?:\uD83D\uDC8B\u200D)?)\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\u200D[\u2695\u2696\u2708])?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764(?:\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F?\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F?\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3C-\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83D\uDC41\uFE0F?\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83C\uDFF3\uFE0F?\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F?\u200D\u26A7|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDEF1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764(?:\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\u200D(?:\uD83D\uDD25|\uD83E\uDE79))|\uD83D\uDC41\uFE0F?|\uD83C\uDFF3\uFE0F?|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3C-\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#*0-9]\uFE0F?\u20E3|\uD83E\uDD3C(?:\uD83C[\uDFFB-\uDFFF])|\u2764\uFE0F?|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF6])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD3C\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF6]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDDDE\uDDDF]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\uD83C[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDD-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7C\uDE80-\uDE86\uDE90-\uDEAC\uDEB0-\uDEBA\uDEC0-\uDEC2\uDED0-\uDED9\uDEE0-\uDEE7]/g,
  UNIQUE_CHARACTER:
    /([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF]|[0-9０-９]|[&\/\\#,+()$~%.'":;*?<>{}\-\_|`@\^!])/,
  SPECIAL_CHARACTER:
    /[\u3040-\u309F\u30A0-\u30FF\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF\uFF10-\uFF19\uFF66-\uFF9D\uFF01-\uFF5E\uFFE0-\uFFE6\u3000-\u303F\uff65-\uff9f]/g,
};

export const TIMEZONE = 'Asia/Tokyo';

export const HEADER_NAVIGATOR: {
  label: React.ReactNode;
  icon?: React.ReactNode;
  href: string;
  sx?: string;
  newTab?: boolean;
}[] = [
  {
    label: 'お知らせ一覧',
    href: 'https://hogugu.com/news/',
  },
  {
    label: 'セラピストを探す',
    href: '/[[...slug]]',
  },
  {
    label: 'セラピスト一覧',
    href: '/therapist',
  },
  {
    label: 'ご利用方法',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/`,
  },
  {
    label: 'よくあるご質問',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/guide.html`,
  },
];

export const SUBMENU_NAVIGATOR: {
  label: React.ReactNode;
  icon?: React.ReactNode;
  href: string;
  newTab?: boolean;
  sx?: string;
}[] = [
  {
    label: (
      <>
        <IconMail />
        &nbsp;お問い合わせ
      </>
    ),
    href: `${process.env.NEXT_PUBLIC_LP_URL}/contact-us.html`,
  },
  {
    label: 'HOGUGU media',
    icon: <IconNewTab />,
    href: 'https://media.hogugu.com/',
    newTab: true,
    sx: 'mediaLink',
  },
  {
    label: '採用情報',
    icon: <IconNewTab />,
    href: 'https://www.wantedly.com/companies/hogugu',
    newTab: true,
    sx: 'mediaLink',
  },
];

export const LOGGED_USER_NAVIGATOR: {
  label: React.ReactNode;
  icon: React.ReactNode;
  component?: any;
  href: string;
  sx?: string;
}[] = [
  {
    label: 'マイページ',
    href: '/my-page',
    icon: <IconAvatar />,
  },
  {
    label: '予約履歴',
    href: '/my-page/booking-history',
    icon: <IconClock />,
  },
  {
    label: 'ログアウト',
    href: '/logout',
    component: 'button',
    icon: <IconLogout />,
    sx: 'btnLogout',
  },
];

export const GUEST_NAVIGATOR: {
  label: React.ReactNode;
  href: string;
  icon: React.ReactNode;
  color: 'queenBlue' | 'marigold';
  sx?: string;
}[] = [
  {
    label: '新規会員登録',
    href: '/register',
    color: 'queenBlue',
    sx: 'btnRegister',
    icon: <IconRegister />,
  },
  {
    label: 'ログイン',
    icon: <IconLogin />,
    color: 'marigold',
    sx: 'btnLogin',
    href: '/login',
  },
];

export const FOOTER_NAVIGATOR: {
  label: React.ReactNode;
  href: string;
  newTab?: boolean;
}[] = [
  {
    label: 'ご利用方法',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/`,
  },
  {
    label: 'お知らせ一覧',
    href: 'https://hogugu.com/news/',
  },
  {
    label: 'よくあるご質問',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/guide.html`,
  },
  {
    label: '利用規約',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/terms-of-use/index.html`,
  },
  {
    label: '特定商取引に基づく表記',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/act-on-specified-commercial-transaction.html`,
  },
  {
    label: 'プライバシーポリシー',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/privacy.html`,
  },
  {
    label: 'セラピスト登録希望の方はこちら',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/therapist.html`,
  },
  {
    label: '運営会社',
    href: `${process.env.NEXT_PUBLIC_LP_URL}/about.html`,
  },
  {
    label: (
      <>
        HOGUGU media&nbsp;
        <IconNewTab />
      </>
    ),
    href: 'https://media.hogugu.com/',
    newTab: true,
  },
  {
    label: (
      <>
        採用情報&nbsp;
        <IconNewTab />
      </>
    ),
    href: 'https://www.wantedly.com/companies/hogugu',
    newTab: true,
  },
];

export const SEARCH_AREA: { label: string; href: string }[] = [
  {
    label: '北海道',
    href: '/hokkaido',
  },
  {
    label: '青森',
    href: '/aomori',
  },
  {
    label: '岩手',
    href: '/iwate',
  },
  {
    label: '宮城',
    href: '/miyagi',
  },
  {
    label: '山形',
    href: '/yamagata',
  },
  {
    label: '福島',
    href: '/fukushima',
  },
  {
    label: '茨城',
    href: '/ibaraki',
  },
  {
    label: '栃木',
    href: '/tochigi',
  },
  {
    label: '群馬',
    href: '/gunma',
  },
  {
    label: '埼玉',
    href: '/saitama',
  },
  {
    label: '千葉',
    href: '/chiba',
  },
  {
    label: '東京',
    href: '/tokyo',
  },
  {
    label: '神奈川',
    href: '/kanagawa',
  },
  {
    label: '新潟',
    href: '/niigata',
  },
  {
    label: '富山',
    href: '/toyama',
  },
  {
    label: '石川',
    href: '/ishikawa',
  },
  {
    label: '福井',
    href: '/fukui',
  },
  {
    label: '山梨',
    href: '/yamanashi',
  },
  {
    label: '長野',
    href: '/nagano',
  },
  {
    label: '和歌山',
    href: '/wakayama',
  },
  {
    label: '岐阜',
    href: '/gifu',
  },
  {
    label: '静岡',
    href: '/shizuoka',
  },
  {
    label: '愛知',
    href: '/aichi',
  },
  {
    label: '三重',
    href: '/mie',
  },
  {
    label: '滋賀',
    href: '/shiga',
  },
  {
    label: '京都',
    href: '/kyoto',
  },
  {
    label: '大阪',
    href: '/osaka',
  },
  {
    label: '兵庫',
    href: '/hyogo',
  },
  {
    label: '奈良',
    href: '/nara',
  },
  {
    label: '岡山',
    href: '/okayama',
  },
  {
    label: '広島',
    href: '/hiroshima',
  },
  {
    label: '山口',
    href: '/yamaguchi',
  },
  {
    label: '香川',
    href: '/kagawa',
  },
  {
    label: '愛媛',
    href: '/ehime',
  },
  {
    label: '福岡',
    href: '/fukuoka',
  },
  {
    label: '佐賀',
    href: '/saga',
  },
  {
    label: '長崎',
    href: '/nagasaki',
  },
  {
    label: '熊本',
    href: '/kumamoto',
  },
  {
    label: '大分',
    href: '/oita',
  },
  {
    label: '宮崎',
    href: '/miyazaki',
  },
  {
    label: '鹿児島',
    href: '/kagoshima',
  },
  {
    label: '沖縄',
    href: '/okinawa',
  },
];

export const SOCIAL_CONTACTS: Record<
  string,
  {
    icon: string;
    href: string;
  }
> = {
  youtube: {
    icon: '/icons/icon-youtube.svg',
    href: 'https://www.youtube.com/channel/UCYFxc5mljKVKKkCbar0faOw/featured',
  },
  twitter: {
    icon: '/icons/icon-twitter.svg',
    href: 'https://twitter.com/HOGUGU_official',
  },
  facebook: {
    icon: '/icons/icon-facebook.svg',
    href: 'https://www.facebook.com/hogugu.relaxation/',
  },
  instagram: {
    icon: '/icons/icon-instagram.webp',
    href: 'https://www.instagram.com/hogugu.official/?hl=ja',
  },
};

export const SORT_ORDER: Record<string, string> = {
  expensive: 'price_desc',
  reasonable: 'price_asc',
  reputation: 'review_desc',
  ranking: 'rating_desc',
};

export const SORT_ORDER_TEXT: Record<string, string> = {
  recommend: 'おすすめ',
  // ranking: 'ランキング',
  reputation: 'クチコミが多い',
  reasonable: '価格が安い',
  expensive: '価格が高い',
};

export const DEFAULT_MENUS: { label: string; value: string }[] = [
  { label: 'もみほぐし＋フットセット', value: 'massage-foot' },
  { label: 'タイ式', value: 'thai' },
  { label: 'もみほぐし', value: 'massage' },
  { label: 'オイル＋フットセット', value: 'oil-foot' },
  { label: 'オイルトリートメント', value: 'oil' },
  { label: 'フット（リフレクソロジー）', value: 'foot' },
  { label: 'ヘッド', value: 'head' },
  { label: 'もみほぐし＋オイルセット', value: 'massage-oil' },
  { label: 'ストレッチ', value: 'stretch' },
];

export const GENDER: Record<string, string> = {
  1: '男性',
  2: '女性',
  0: 'その他',
};

export const GENDER_SEARCHING: Record<string, string> = {
  1: '男性',
  2: '女性',
  0: '指定なし',
};

export enum BOOKING_CATEGORIES {
  REQUEST = 'REQUEST',
  CONFIRMED = 'CONFIRMED',
  CLOSED = 'CLOSED',
}

export enum BOOKING_STATUSES {
  NEW = 'NEW',
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_TRANSIT = 'IN_TRANSIT',
  ARRIVED = 'ARRIVED',
  DONE = 'DONE',
  CANCELED = 'CANCELED',
}

export enum BOOKING_REASONS {
  ASSIGN_THERAPIST = 'assignTherapist',
  NEW_BOOKING = 'newBooking',
  BOOKING_DONE = 'bookingDone',
  SET_ARRIVAL_TIME = 'setArrivalTime',
  START_MASSAGE = 'startMassage',
  FINISH_MASSAGE = 'finishMassage',
  FINISH_WITHOUT_TREATMENT = 'finishWithoutTreatment',
  EXPIRED_BOOKING = 'Expired',
  EXPIRED_APPOINTMENT_BOOKING = 'appointmentBookingExpired',
  EXPIRED_STANDARD_BOOKING = 'standardBookingExpired',
  THERAPIST_DENY_BOOKING = 'therapistDenyBooking',
  THERAPIST_CANCEL_BOOKING = 'therapistCancelBooking',
  CUSTOMER_CANCEL_BOOKING = 'customerCancelBooking',
  ADMIN_CANCEL_BOOKING = 'adminCancelBooking',
  THERAPIST_ACCEPT_BOOKING = 'therapistAcceptBooking',
}

export const SMS_COUNTDOWN = 60; // seconds

export const FOOTER_PAGES: Record<string, string[]> = {
  simple: [
    '/login',
    '/register',
    '/register/complete',
    '/terms',
    '/booking',
    '/booking/[id]',
    '/my-page',
    '/my-page/cards',
    '/my-page/cards/add',
    '/my-page/profile-edit',
    '/my-page/profile-edit/complete',
    '/my-page/booking-history',
    '/my-page/notifications',
    '/my-page/delete-account',
    '/my-page/delete-account-immediately',
    '/my-page/revoke-account',
    '/booking/[id]/review',
    '/booking/complete',
    '/my-page/coupons',
  ],
};

export const AUTH_ROUTE = ['/login', '/register'];

export const FIREBASE_AUTH_ERRORS: Record<string, string> = {
  'auth/invalid-phone-number-login':
    '電話番号またはメールアドレスの形式が間違っています。ご確認の上、再度入力してください。',
  'auth/invalid-phone-number': '電話番号をお確めの上、入力してください。',
  'auth/invalid-verification-code':
    '認証コードをお確かめの上、入力してください。',
  'auth/too-many-requests':
    '繰り返し認証が行われたため、セキュリティ対策としてこの番号は一時的にブロックされました。しばらくしてからもう一度お試しください。',
  'auth/code-expired':
    '認証コードの有効期限が切れています。再度認証コードを取得してください。',
  'auth/network-request-failed': 'No network available',
  'auth/captcha-check-failed': 'Captcha check failed',
};

export const CardIcon = {
  Visa: <Visa />,
  Mastercard: <MasterCard />,
  MasterCard: <MasterCard />,
  JCB: <JCB />,
  'Diners Club': <DinersClub />,
  DINERS: <DinersClub />,
  'American Express': <Amex />,
};

export enum ValidCardNiceType {
  'Visa' = 'Visa',
  'MasterCard' = 'MasterCard',
  'Mastercard' = 'Mastercard',
  'JCB' = 'JCB',
  'Diners Club' = 'Diners Club',
  'DINERS' = 'Diners Club',
  'American Express' = 'American Express',
}

export const GMO_TOKEN_ERRORS: Record<string, string> = {
  '000': 'トークン取得正常終了',
  '100': 'カード番号必須チェックエラー',
  '101': 'カード番号フォーマットエラー(数字以外を含む)',
  '102': 'カード番号フォーマットエラー(10-16 桁の範囲外)',
  '110': '有効期限必須チェックエラー',
  '111': '有効期限フォーマットエラー(数字以外を含む)',
  '112': '有効期限フォーマットエラー(6 又は 4 桁以外)',
  '113': '有効期限フォーマットエラー(月が 13 以上)',
  '121': 'セキュリティコードフォーマットエラー(数字以外を含む)',
  '122': 'セキュリティコード桁数エラー',
  '131': 'カード名義は半角（英字またはスペース）で入力してください。',
  '132': '名義人フォーマットエラー(51 桁以上)',
  '141': '発行数フォーマットエラー(数字以外を含む)',
  '142': '発行数フォーマットエラー(1-10 の範囲外)',
  '150': 'カード情報を暗号化した情報必須チェックエラー',
  '160': 'ショップ ID 必須チェックエラー',
  '161': 'ショップ ID フォーマットエラー(14 桁以上)',
  '162': 'ショップ ID フォーマットエラー(半角英数字以外)',
  '170': '公開鍵ハッシュ値必須チェックエラー',
  '180': 'ショップ ID または公開鍵ハッシュ値がマスターに存在しない',
  '190': 'カード情報(Encrypted)が復号できない',
  '191': 'カード情報(Encrypted)復号化後フォーマットエラー',
  '501': 'トークン用パラメータ(id)が送信されていない',
  '502': 'トークン用パラメータ(id)がマスターに存在しない',
  '511': 'トークン用パラメータ(cardInfo)が送信されていない',
  '512': 'トークン用パラメータ(cardInfo)が復号できない',
  '521': 'トークン用パラメータ(key)が送信されていない',
  '522': 'トークン用パラメータ(key)が復号できない',
  '531': 'トークン用パラメータ(callBack)が送信されていない',
  '541': 'トークン用パラメータ(hash)が存在しない',
  '551': 'トークン用 apikey が存在しない ID',
  '552': 'トークン用 apikey が有効ではない',
  '901': 'マルチペイメント内部のシステムエラー',
  '902': '処理が混み合っている',
};

export const BOOKING_PROGRESS_STATUS: Record<string, string> = {
  NEW: '予約を作成',
  PENDING: '予約を作成',
  CONFIRMED: '予約が成立しました',
  ARRIVED: '施術スタート',
  DONE: '施術完了',
  CANCELED: 'キャンセル',
};

export const MIDNIGHT_TIMES = [
  '23:30',
  '00:00',
  '00:30',
  '01:00',
  '01:30',
  '02:00',
  '02:30',
  '03:00',
  '03:30',
  '04:00',
  '04:30',
  '05:00',
  '05:30',
];
export const MIDNIGHT_FEE = 2000;

export const LOCAL_STORAGE_KEY = {
  ADDRESS_HISTORIES: 'hg-address-histories',
  BOOKING: 'hg-booking',
  THERAPIST_LATE_RESPONSE: 'hg-late-response',
  BOOKING_POLICY_CHECKED: 'hg-policy-checked',
};

export const COOKIE_KEY = {
  TOKEN: 'hogugu-web-cookie',
  USER_DATA: 'user-data',
  TWILIO_TOKEN: 'twilio-token',
};

export enum POINT_HISTORY_REASON {
  GRANT_POINT = 'GRANT_POINT',
  USE_POINT = 'USE_POINT',
  REFUND = 'REFUND',
  REVOKE = 'REVOKE',
  REWARD = 'REWARD',
  DEDUCT = 'DEDUCT',
  REFUND_DIFF = 'REFUND_DIFF',
  REVOKE_DIFF = 'REVOKE_DIFF',
  GRANT_MORE_POINT = 'GRANT_MORE_POINT',
  EXPIRED = 'EXPIRED',
}

export enum POINT_HISTORY_REASON_TEXT {
  GRANT_POINT = 'ポイント獲得',
  USE_POINT = 'ポイント利用',
  REFUND = 'ポイント返却（キャンセル）',
  REVOKE = 'ポイント取消（キャンセル）',
  REWARD = 'ポイント獲得（運営事務局）',
  DEDUCT = 'ポイント取消（運営事務局）',
  REFUND_DIFF = 'ポイント返却（決済金額変更）',
  REVOKE_DIFF = 'ポイント取消（決済金額変更）',
  GRANT_MORE_POINT = 'ポイント獲得（決済金額変更）',
  EXPIRED = 'ポイント失効',
}

export enum POINT_HISTORY_BOOKING_TYPE {
  ONDEMAND = 'ONDEMAND',
  STANDARD = 'STANDARD',
  NOW = 'NOW',
}
