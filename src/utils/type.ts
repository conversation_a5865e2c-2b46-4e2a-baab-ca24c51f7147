import type {
  FetchQueryOptions,
  QueryClient,
  QueryKey,
  UseMutationOptions,
  UseQueryOptions,
} from '@tanstack/react-query';
import type { AxiosRequestConfig, Method } from 'axios';

export type UserTokenCookie = {
  token?: string;
  firebaseToken?: string;
};

export type UserDataCookie = {
  displayName?: string;
  unreadMessages?: number;
  unreadNotifications?: number;
  requestingDeleteAccount?: boolean;
  isCompletedProfile?: boolean;
};

// TODO: Currently, there are many any types here,
// bacause each query/mutation will have different generic types
// It's difficult to cover all types for every query/mutation
// Consider to apply conditional types for useQuery or useMutation
export type ExtendOptions = {
  apiUrl: ((payload: any) => string) | string;
  customParams?: Record<string, unknown>;
  queryKey?: QueryKey;
  method?: Method;
  transform?: (params: Record<string, unknown>) => Record<string, unknown>;
  axiosConfig?: AxiosRequestConfig;
  successMessage?: string;
  defaultToast?: boolean;
  omitKeys?: string[];
};

export type QueryModel = Record<
  string,
  | ((UseQueryOptions<any> | UseMutationOptions) & ExtendOptions)
  | ((
      payload: any,
    ) => (UseQueryOptions<any> | UseMutationOptions) & ExtendOptions)
>;

export interface IError {
  error: string;
  data: unknown;
  code: number;
}

export type MetaProps = {
  notToastErrorCodes?: (number | string)[];
  noToastError?: boolean;
};

export interface ITableQuery {
  page: number;
  limit: number;
}
export type IListQuery<T> = (T | Record<string, unknown>) & ITableQuery;

export interface FetchDetailOptions
  extends FetchQueryOptions<any, unknown, any, unknown[]> {
  queryClient: QueryClient;
  queryKey: unknown[];
  apiUrl: string;
  customParams?: Record<string, unknown>;
  method?: string;
  axiosConfig?: AxiosRequestConfig;
  transform?: (params: Record<string, unknown>) => Record<string, unknown>;
}

export interface FetchListOptions
  extends FetchQueryOptions<any, unknown, any, unknown[]> {
  queryClient: QueryClient;
  queryKey: unknown[];
  apiUrl: string;
  customParams?: Record<string, unknown>;
  method?: string;
  axiosConfig?: AxiosRequestConfig;
  transform?: (params: Record<string, unknown>) => Record<string, unknown>;
  omitKeys?: string[];
}
