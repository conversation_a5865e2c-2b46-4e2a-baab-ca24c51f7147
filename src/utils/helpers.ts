import { BUILDING_TYPES } from 'components/Profile/AddressHistoryForm/constants';
import { deleteCookie, getCookie, setCookie } from 'cookies-next';
import type { OptionsType } from 'cookies-next/lib/types';
import type { ICreateBooking } from 'hooks/types';
import jwt_decode from 'jwt-decode';
import { isArray, times } from 'lodash';
import type { AddressHistoryItem } from 'models/address';
import type { IBookingDetail } from 'models/booking';
import type { NextApiRequest, NextApiResponse } from 'next';
import type { NextRouter } from 'next/router';
import dayjs from 'utils/dayjs';

import { BOOKING_CATEGORIES, COOKIE_KEY } from './constants';
import notification from './notification';
import type { UserDataCookie, UserTokenCookie } from './type';

const cookieShared =
  process.env.NEXT_PUBLIC_DEPLOY_ENV !== 'development'
    ? 'hogugu.com'
    : undefined;

const helpers = {
  getWebCookie: (req?: NextApiRequest, res?: NextApiResponse) => {
    try {
      const cookies = JSON.parse(
        (getCookie(COOKIE_KEY.TOKEN, req && res ? { req, res } : {}) ||
          '{}') as string,
      ) as UserTokenCookie;
      return cookies;
    } catch {
      return {};
    }
  },
  getUserDataCookie: (req?: NextApiRequest, res?: NextApiResponse) => {
    try {
      const cookies = JSON.parse(
        (getCookie(COOKIE_KEY.USER_DATA, req && res ? { req, res } : {}) ||
          '{}') as string,
      ) as UserDataCookie;
      return cookies;
    } catch {
      return {};
    }
  },
  setToken: (data: UserTokenCookie): void =>
    setCookie(COOKIE_KEY.TOKEN, data, {
      path: '/',
      sameSite: 'lax',
      maxAge: 31536000,
    }),
  removeWebCookie: (): void => {
    deleteCookie(COOKIE_KEY.TOKEN, { path: '/' });
    deleteCookie(COOKIE_KEY.TWILIO_TOKEN, { path: '/' });
    deleteCookie(COOKIE_KEY.USER_DATA, {
      domain: cookieShared,
    });
  },
  getTokenConfig: (req: unknown, res: unknown) => {
    const webCookie = helpers.getWebCookie(
      req as NextApiRequest,
      res as NextApiResponse,
    );
    return {
      headers: {
        Authorization: `Bearer ${webCookie?.token || process.env.GUEST_TOKEN}`,
      },
    };
  },
  setUserData: (data: Record<string, unknown>, options?: OptionsType) => {
    try {
      const userDataCookie = JSON.parse(
        (getCookie(COOKIE_KEY.USER_DATA) || '{}') as string,
      ) as UserDataCookie;
      setCookie(
        COOKIE_KEY.USER_DATA,
        {
          ...userDataCookie,
          ...data,
        },
        {
          ...options,
          domain: cookieShared,
          maxAge: 31536000,
        },
      );
    } catch {
      setCookie(
        COOKIE_KEY.USER_DATA,
        {
          ...data,
        },
        {
          ...options,
          domain: cookieShared,
          maxAge: 31536000,
        },
      );
    }
  },
  convertArrayToEntities: (array: { value: string; _id: string }[]) => {
    const ids: string[] = [];
    const entities = (array || []).reduce((acc, cur) => {
      ids.push(cur._id);
      return { ...acc, [cur._id]: cur };
    }, {});
    return {
      ids,
      entities,
    };
  },
  mapTimeOptions: (value: Date | undefined) => {
    const m = Math.abs(30 - (dayjs().get('m') % 30)) + 60;
    const currentDate = dayjs().add(m, 'm').startOf('m');
    const options: { label: string; value: string; disabled?: boolean }[] = [];
    times(24).forEach((index) => {
      const date = dayjs(value).set('h', index).startOf('h');
      const hour = index > 9 ? index : `0${index}`;
      options.push(
        {
          label: `${hour}:00`,
          value: `${hour}:00`,
          disabled: date.diff(currentDate.startOf('m'), 'm') < 0,
        },
        {
          label: `${hour}:30`,
          value: `${hour}:30`,
          disabled: date.set('m', 30).diff(currentDate.startOf('m'), 'm') < 0,
        },
      );
    });
    return options;
  },
  formatSlugPath: (slug: string | string[]): string[] | undefined => {
    if (isArray(slug)) {
      const slugJoin = slug.join('/');
      const slugFormat = slugJoin.replace('-', '/');
      return slugFormat.split('/');
    }
    return undefined;
  },
  removeUndefined: (values: Record<string, any>) => {
    const newValues = { ...values };
    Object.keys(newValues).forEach((key) =>
      newValues[key] === undefined ? delete newValues[key] : {},
    );
    return newValues;
  },
  getValidDate: (value?: string | string[]) => {
    const m = Math.abs(30 - (dayjs().get('m') % 30)) + 60;
    const currentDate = dayjs().add(m, 'm').startOf('m');
    if (typeof value === 'string' && value && dayjs(value).isValid()) {
      const date = dayjs(value);
      return currentDate.diff(date, 'm') > 0
        ? currentDate.toISOString()
        : date.startOf('m').toISOString();
    }
    return currentDate.toISOString();
  },
  numberFormat: (value: number | undefined) =>
    value ? new Intl.NumberFormat('ja-JP').format(value) : 0,
  timestampFormat: (value: string | undefined) => {
    const date = dayjs(value);
    const currentDate = dayjs();
    if (currentDate.diff(date, 's') < 60) {
      return 'たった今';
    }
    if (currentDate.diff(date, 'm') < 60) {
      return `${currentDate.diff(date, 'm')}分前`;
    }
    if (currentDate.diff(date, 'h') < 24) {
      return `${currentDate.diff(date, 'h')}時間前`;
    }
    if (currentDate.diff(date, 'd') <= 7) {
      return `${currentDate.diff(date, 'd')}日前`;
    }
    return date.format('YYYY/MM/DD \xa0\xa0 HH:mm');
  },
  transfromPhone: (value = '') => {
    const phoneHalfWidth = value.replace(/[！-～]/g, (halfwidthChar) =>
      String.fromCharCode(halfwidthChar.charCodeAt(0) - 0xfee0),
    );
    // If match with country code format cut out first 2 letter (can use +84 for team debug)
    if (phoneHalfWidth.match(/^(\+\d+)$/g)) {
      const number = phoneHalfWidth.slice(1);
      return {
        countryCode: number.slice(0, 2),
        phoneNumber: number.slice(2),
        phone: phoneHalfWidth,
      };
    }
    // If not match with country code using default phone format
    // https://www.globalcallforwarding.com/international-call-prefixes/
    const countryCode = '81';
    let phoneNumber = phoneHalfWidth;
    if (phoneNumber.slice(0, 2) === '10') {
      phoneNumber = phoneNumber.slice(2);
    }
    if (phoneNumber.slice(0, 1) === '0') {
      phoneNumber = phoneNumber.slice(1);
    }
    return {
      countryCode,
      phoneNumber,
      phone: `+${countryCode}${phoneNumber}`,
    };
  },
  backPrevPage: (router: NextRouter) => {
    if (
      window.history.length > 1 &&
      document.referrer.indexOf(window.location.host) !== -1
    ) {
      router.back();
    } else {
      router.replace('/');
    }
  },
  checkValidImage: (file: File, config?: { maxSize: number; type: string }) => {
    if (config?.type && !file.type.startsWith(config.type)) {
      notification.show({ type: 'error', message: 'Invalid image' });
      return false;
    }
    if (config?.maxSize && file.size > 1000000 * config.maxSize) {
      notification.show({ type: 'error', message: 'Invalid size' });
      return false;
    }
    return true;
  },
  removeEmpty: (obj: Record<string, unknown>, omit?: string[]) => {
    const omitKeys = omit || [];
    return Object.fromEntries(
      Object.entries(obj).filter(
        ([k, v]) =>
          v !== undefined && v !== null && v !== '' && !omitKeys.includes(k),
      ),
    );
  },
  isEnabledAudio: async () => {
    if (typeof navigator !== 'undefined' && 'mediaDevices' in navigator) {
      try {
        await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        return true;
      } catch {
        return false;
      }
    }
    return false;
  },
  detectBrowser: () => {
    if (typeof navigator === 'undefined') {
      return undefined;
    }
    const { userAgent } = navigator;
    if (userAgent.match(/edg/i)) {
      return 'Edge';
    }
    if (userAgent.match(/chrome|chromium|crios/i)) {
      return 'Chrome';
    }
    if (userAgent.match(/firefox|fxios/i)) {
      return 'Firefox';
    }
    if (userAgent.match(/iPad/i) || userAgent.match(/iPhone/i)) {
      return 'iOS';
    }
    if (userAgent.match(/safari/i)) {
      return 'Safari';
    }
    return undefined;
  },
  getSupportLink: (type?: 'Chrome' | 'Firefox' | 'Safari' | 'Edge' | 'iOS') => {
    const supportLinks = {
      Chrome: 'https://support.google.com/chrome/answer/2693767?hl=ja',
      Firefox:
        'https://support.mozilla.org/ja/kb/how-manage-your-camera-and-microphone-permissions',
      Safari: 'https://support.apple.com/ja-jp/guide/mac-help/mchla1b1e1fe/mac',
      Edge: 'https://support.microsoft.com/ja-jp/windows/windows-%E3%83%9E%E3%82%A4%E3%82%AF-%E3%83%97%E3%83%A9%E3%82%A4%E3%83%90%E3%82%B7%E3%83%BC%E3%82%92%E4%BF%9D%E8%AD%B7%E3%81%99%E3%82%8B-a83257bc-e990-d54a-d212-b5e41beba857#:~:text=Microsoft%20Edge%20%E3%81%A7%E3%80%81%5B%E8%A8%AD%E5%AE%9A%E3%81%9D%E3%81%AE%E4%BB%96,%E3%83%9E%E3%82%A4%E3%82%AF%5D%20%E3%82%92%E9%81%B8%E6%8A%9E%E3%81%97%E3%81%BE%E3%81%99%E3%80%82',
      iOS: 'https://support.apple.com/ja-jp/guide/iphone/iph168c4bbd5/ios',
    } as const;
    if (!type) {
      return undefined;
    }
    return supportLinks[type];
  },
};

export const requestLocation = ({
  successCallback,
  errorCallback,
}: {
  successCallback: PositionCallback;
  errorCallback: () => void;
}) => {
  if (typeof navigator !== 'undefined' && 'geolocation' in navigator) {
    navigator.geolocation.getCurrentPosition(successCallback, errorCallback);
  } else {
    errorCallback();
  }
};

export const setUserId = (id: string | null) => {
  if (typeof window === 'undefined') return;
  window.gtag('config', process.env.GA_MEASUREMENT_ID || '', {
    user_id: id,
  });
};

export const eventLog = (event: string, params?: Record<string, any>) => {
  if (typeof window === 'undefined') return;
  const cookies = JSON.parse(
    (getCookie(COOKIE_KEY.TOKEN) || '{}') as string,
  ) as UserTokenCookie;
  if (cookies.token) {
    const decoded = jwt_decode(cookies.token) as { uid: string };
    window.gtag('set', {
      user_id: decoded.uid,
    });
  }
  window.gtag('event', event, params);
};

export const exchangePointToPrice = (
  point: number,
  exchangeAmount: number = 1,
  exchangePoint: number = 1,
) => {
  return (point * exchangeAmount) / exchangePoint;
};

export const formatAddress = (booking: AddressHistoryItem | undefined) => {
  const addressParts = [
    booking?.address,
    booking?.buildingType && BUILDING_TYPES[booking.buildingType],
    booking?.nameplate,
    booking?.buildingDetails,
    booking?.accessMethod,
  ];
  const formattedAddress = addressParts.filter(Boolean).join(', ');
  const areaNames = (booking as ICreateBooking)?.areaNames;
  return formattedAddress || areaNames || '---';
};

export const calculateRating = (
  summaryReview: { sumRating: number; sumReviewer: number } | undefined,
) => {
  return (
    (summaryReview?.sumRating || 0) / (summaryReview?.sumReviewer || 0) || 0
  ).toFixed(1);
};

export const checkChattingExpired = (
  bookingStatusHistory: IBookingDetail['statusHistory'] = [],
) => {
  const closedStatus = bookingStatusHistory
    .filter((item) => item.category === BOOKING_CATEGORIES.CLOSED)
    .slice(-1)[0];

  if (!closedStatus) return false;
  return dayjs()
    .subtract(Number(process.env.NEXT_PUBLIC_DURATION_LIMIT_CHAT), 'h')
    .isAfter(closedStatus.timestamp);
};

export default helpers;
