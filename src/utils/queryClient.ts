import type { Mutation } from '@tanstack/react-query';
import { MutationCache, QueryCache, QueryClient } from '@tanstack/react-query';
import { setServiceDownContent } from 'hooks/useGlobalState';
import { get } from 'lodash';
import notification from 'utils/notification';

import { LOCAL_STORAGE_KEY } from './constants';
import helpers, { setUserId } from './helpers';
import request from './request';
import type { FetchDetailOptions, FetchListOptions, MetaProps } from './type';

const handleError = (
  error: unknown,
  _query: unknown,
  _context?: unknown,
  mutation?: Mutation<unknown, unknown, unknown, unknown>,
) => {
  const errorMessage: string = get(error, 'error', '');
  const errorCode = get(error, 'code');
  if (Array.isArray(get(error, 'data'))) {
    get(error, 'data', []).forEach((errorData) => {
      if (get(errorData, 'message')) {
        notification.show({
          type: 'error',
          message: get(errorData, 'message'),
        });
      }
    });
    return;
  }
  if (mutation && mutation.meta) {
    const { noToastError }: MetaProps = mutation.meta;
    if (noToastError) return;
  }
  // if (errorMessage === 'Network Error') {
  //   errorMessage = 'インターネット接続がありません。';
  // }
  if (errorCode === 401) {
    const queryClient = new QueryClient();
    queryClient
      .getQueryCache()
      .findAll(['currentUser'])
      .forEach((query) => query.reset());
    helpers.removeWebCookie();
    setUserId(null);
    if (typeof window !== 'undefined') {
      localStorage.removeItem(LOCAL_STORAGE_KEY.BOOKING);
      localStorage.removeItem(LOCAL_STORAGE_KEY.BOOKING_POLICY_CHECKED);
    }
  }
  if (typeof window !== 'undefined' && errorCode === 503) {
    setServiceDownContent(get(error, 'data', ''));
    return;
  }
  if (typeof window !== 'undefined' && errorMessage) {
    notification.show({ type: 'error', message: errorMessage });
  }
};

let queryClientSide: QueryClient | undefined;
const getQueryClient = (): QueryClient => {
  const initialQueryClient = () =>
    new QueryClient({
      defaultOptions: {
        queries: {
          structuralSharing: true,
          refetchOnWindowFocus: false,
          retry: false,
          suspense: false,
          networkMode: 'offlineFirst',
        },
        mutations: {
          networkMode: 'offlineFirst',
        },
      },
      mutationCache: new MutationCache({
        onError: (error, query, context, mutation) =>
          handleError(error, query, context, mutation),
      }),
      queryCache: new QueryCache({
        onError: (error, query) => handleError(error, query),
      }),
    });
  if (typeof window === 'undefined') {
    return initialQueryClient();
  }
  if (!queryClientSide) {
    queryClientSide = initialQueryClient();
  }
  return queryClientSide;
};

export const fetchData = ({
  queryClient,
  queryKey,
  apiUrl,
  customParams,
  method = 'GET',
  axiosConfig,
  transform,
  ...options
}: FetchDetailOptions) => {
  return queryClient.fetchQuery({
    queryKey,
    queryFn: async () => {
      const { data } = await request({
        url: apiUrl,
        data: customParams,
        method,
        config: axiosConfig,
      });
      return transform ? transform(data) : data;
    },
    ...options,
  });
};

export const fetchList = ({
  queryClient,
  queryKey,
  apiUrl,
  customParams,
  axiosConfig,
  omitKeys,
  method = 'GET',
  transform,
  ...options
}: FetchListOptions) => {
  const queryParams = { ...(customParams || {}) };
  if (!queryParams.limit) {
    queryParams.limit = 10;
  }
  if (!queryParams.page) {
    queryParams.page = 1;
  }
  if (omitKeys) {
    omitKeys.forEach((key) => {
      delete queryParams[key];
    });
  }
  return queryClient.fetchQuery({
    queryKey: [
      ...queryKey,
      JSON.stringify(queryParams, Object.keys(queryParams).sort()),
    ],
    queryFn: async () => {
      const { data } = await request({
        url: apiUrl,
        data: queryParams,
        method,
        config: axiosConfig,
      });
      return transform ? transform(data) : data;
    },
    ...options,
  });
};

export default getQueryClient;
