/* eslint-disable prefer-promise-reject-errors */
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';
import { get } from 'lodash';
import helpers from 'utils/helpers';

const api = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_DOMAIN}/server`,
});

api.interceptors.request.use((config) => {
  const requestConfig = {
    ...config,
  };
  requestConfig.headers = requestConfig.headers ?? {};
  requestConfig.headers['Accept-Language'] = 'ja';
  requestConfig.headers['X-Device-Type'] = 'web';
  requestConfig.headers.os = 'web';
  // requestConfig.headers['Accept-Timezone'] =
  //   Intl.DateTimeFormat().resolvedOptions().timeZone;
  if (!requestConfig?.headers?.Authorization) {
    requestConfig.headers.Authorization = `Bearer ${process.env.GUEST_TOKEN}`;
    const webCookie = helpers.getWebCookie();
    if (webCookie?.token) {
      requestConfig.headers.Authorization = `Bearer ${webCookie?.token}`;
    }
  }

  return requestConfig;
});

api.interceptors.response.use(
  (response) => {
    if (get(response, 'data.error')) {
      return Promise.reject({
        data: get(response, 'data.data'),
        error: get(response, 'data.error', ''),
        code: get(response, 'data.code', 400),
      });
    }
    return response.data;
  },
  ({ message, response }) => {
    // if (
    //   get(response, 'data.error.code') === 209 &&
    //   typeof window !== 'undefined'
    // ) {
    // destroyCookie(null, 'nw-cookie');
    // window.location = "/";
    // }
    return Promise.reject({
      data: get(response, 'data.data') || get(response, 'data'),
      error: get(response, 'data.error', message),
      code: get(response, 'data.code', response?.status || -1),
    });
  },
);

const request = <T = any, D = any>({
  url,
  data,
  config,
  method = 'GET',
}: {
  url: string;
  method?: string;
  data?: any;
  config?: AxiosRequestConfig;
}): Promise<AxiosResponse<T, D>> => {
  switch (method) {
    case 'POST':
      return api.post(url, data, config);
    case 'PUT':
      return api.put(url, data, config);
    case 'PATCH':
      return api.patch(url, data, config);
    case 'DELETE':
      return api.delete(url, { params: data, ...config });
    default:
      return api.get(url, { params: data, ...config });
  }
};

export default request;
