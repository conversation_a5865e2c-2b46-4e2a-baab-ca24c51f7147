import { compact } from 'lodash';
import type { IBreadcrumb } from 'models/resource';
import type { ITherapistItem } from 'models/therapist';
import { GENDER as GENDER_JP } from 'utils/constants';

import { calculateRating } from './helpers';

const GENDER: Record<string, string> = {
  1: 'Male',
  2: 'Female',
  0: '',
};

// const menuTitleDisplay: Record<string, string> = {
//   massage: '肩こり・揉みほぐし',
//   'massage-oil': '揉みほぐし＆オイルマッサージ',
//   'massage-foot': '揉みほぐし＆フットマッサージ',
//   thai: 'タイ式マッサージ',
//   oil: 'オイルマッサージ',
//   'oil-foot': 'オイル＆フットマッサージ',
//   stretch: 'ストレッチ',
// };

const menuDescriptionDisplay: Record<string, string> = {
  massage: 'の肩こり・揉みほぐし対応',
  'massage-oil': 'の揉みほぐし＆オイルマッサージ対応',
  'massage-foot': 'の揉みほぐし＆フットマッサージ対応',
  thai: 'のタイ式マッサージ対応',
  oil: 'のオイルマッサージ対応',
  'oil-foot': 'のオイル＆フットマッサージ対応',
  stretch: 'のストレッチマッサージ対応',
};

const sortTitleDisplay: Record<string, string> = {
  recommend: '人気',
  ranking: 'ランキング',
  reputation: '口コミが多い順',
  reasonable: '料金が安い順',
  expensive: '料金が高い順',
};

const sortDescriptionDisplay: Record<string, string> = {
  recommend: 'でおすすめ',
  ranking: 'の評価が高いランキング',
  reputation: 'の口コミ多いランキング',
  reasonable: 'で施術料が安いの格安コスパランキング',
  expensive: 'で施術料が高いの高級ランキング',
};

export const suffixTitle =
  '【予約20万件・口コミ14万件】実績No.1の出張マッサージ・リラクゼーションサロンならHOGUGU（ホググ）';

export const searchMetaContent = ({
  area,
  menu,
  gender,
  sort,
  total,
  isCity = false,
}: {
  area: string;
  menu: string;
  gender: number;
  sort: string;
  total: number;
  isCity?: boolean;
}) => {
  const displaySortAndFilter = `${sortTitleDisplay[sort || 'recommend']}の${
    gender ? GENDER_JP[gender] : ''
  }セラピスト一覧`;
  const displayMenuDescription = menuDescriptionDisplay[menu] || menu || '';
  const displaySortDescription = sortDescriptionDisplay[sort] || '';
  const title = compact([
    `${area}の出張マッサージ・リラクゼーションサロン`,
    displaySortAndFilter,
    suffixTitle,
  ]).join(' | ');
  let description = `日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、${area}${
    displaySortDescription || 'で人気'
  }${displayMenuDescription}セラピスト ${total}人を掲載中！HOGUGUでは全てのセラピストが厳しい施術チェック・審査をクリアし、ネット予約・カード決済に対応しています｜自宅やホテルで出張マッサージを受けるならHOGUGU`;
  if (!isCity && menu && sort) {
    description = `日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、${area}${displayMenuDescription}セラピスト ${total}人${displaySortDescription}を掲載中！HOGUGUでは全てのセラピストが厳しい施術チェック・審査をクリアし、ネット予約・カード決済に対応しています｜自宅やホテルで出張マッサージを受けるならHOGUGU`;
  }
  const openGraphDescription = `日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、${area}${
    displaySortDescription || 'で人気'
  }${displayMenuDescription}セラピスト ${total}人を掲載中！`;
  return {
    title,
    description,
    openGraphDescription,
    type: menu ? 'article' : 'website',
  };
};

const seoConfig = {
  default: {
    title:
      'HOGUGU（ホググ）- 好きな時間にお家・ホテルでリラックス、出張リラクゼーションサービス',
    description:
      'HOGUGU（ホググ）はセラピストを選べる出張リラクゼーションサービスです。好きな時間好きな場所でアプリから予約してリラクゼーションを受けることが出来ます。面談・施術チェックをクリアした厳選されたセラピストに直接オファーできます。',
    facebook: {
      appId: '1109570993279865',
      pageId: '105239250835643',
    },
    twitter: {
      site: '@HOGUGU_official',
      cardType: 'summary',
    },
  },
  breadcrumbList: (breadcrumbList: IBreadcrumb[]) => ({
    '@context': 'http://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbList.map((item, index) => {
      let { href } = item;
      if (href) {
        href = href.startsWith('http')
          ? href
          : `${process.env.NEXT_PUBLIC_DOMAIN}${href}`;
      }
      return {
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: href,
      };
    }),
  }),
  searchingTherapist: ({
    title,
    description,
    openGraphDescription,
    type,
    prefecture,
    city,
    path,
    noindex = false,
  }: {
    title: string;
    description: string;
    openGraphDescription: string;
    type: string;
    prefecture: string;
    city: string;
    path: string;
    noindex?: boolean;
  }) => {
    const pathSplit = path.split('?');

    return {
      title,
      description,
      noindex,
      canonical: `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}`,
      openGraph: {
        type,
        siteName: 'HOGUGU（ホググ）',
        title,
        description: openGraphDescription,
        url: `${process.env.NEXT_PUBLIC_DOMAIN}${path}`,
        images: [
          {
            url: `${process.env.NEXT_PUBLIC_DOMAIN}/images/og/${prefecture}.png`,
            width: 200,
            height: 200,
            alt: prefecture,
          },
          {
            url: `${process.env.NEXT_PUBLIC_DOMAIN}/images/og/${city}.png`,
            width: 200,
            height: 200,
            alt: city,
          },
        ],
      },
    };
  },
  therapistList: {
    title: `セラピスト一覧 | ${suffixTitle}`,
    description:
      '日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、ご予約可能なセラピストを随時掲載しています。',
    openGraph: {
      type: 'article',
      siteName: 'HOGUGU（ホググ）',
      title:
        '日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】のセラピスト一覧 | HOGUGU（ホググ）',
      description:
        '日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、ご予約可能なセラピストを随時掲載しています。',
      url: `${process.env.NEXT_PUBLIC_DOMAIN}/therapist`,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_DOMAIN}/images/og/zenkoku.png`,
          width: 200,
          height: 200,
          alt: 'zenkoku',
        },
      ],
    },
  },
  therapistDetail: (therapist?: ITherapistItem) => {
    const rating = calculateRating(therapist?.summaryReview);
    const title = compact([
      `${therapist?.nickName}の個別ページ`,
      `施術歴${therapist?.experience.name}、口コミ数${
        therapist?.summaryReview?.sumReviewer || 0
      }件、評価${rating}の${GENDER_JP[therapist?.gender || 0]}セラピスト`,
      suffixTitle,
    ]).join(' | ');
    const description = `日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、${therapist?.nickName}のご予約を受付しています。`;
    return {
      title,
      description,
      canonical: `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist?._id}`,
      openGraph: {
        type: 'article',
        siteName: 'HOGUGU（ホググ）',
        title,
        description,
        url: `https://booking.hogugu.com/therapist/${therapist?._id}`,
        images: [
          {
            url: therapist?.profilePicture?.url || '',
            alt: therapist?.nickName,
            width: 300,
            height: 300,
          },
        ],
      },
    };
  },
  therapistReview: (therapist?: ITherapistItem) => {
    const rating = calculateRating(therapist?.summaryReview);
    const title = compact([
      `${therapist?.nickName}の口コミ一覧`,
      `施術歴${therapist?.experience.name}、口コミ数${
        therapist?.summaryReview?.sumReviewer || 0
      }件、評価${rating}の${GENDER_JP[therapist?.gender || 0]}セラピスト`,
      suffixTitle,
    ]).join(' | ');
    const description = `日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、${therapist?.nickName}の利用者からの口コミを掲載しています。`;
    return {
      title,
      description,
      openGraph: {
        type: 'article',
        siteName: 'HOGUGU（ホググ）',
        title,
        description,
        url: `https://booking.hogugu.com/therapist/${therapist?._id}/review`,
        images: [
          {
            url: therapist?.profilePicture?.url || '',
            alt: therapist?.nickName,
            width: 300,
            height: 300,
          },
        ],
      },
    };
  },
  searchingTherapistSchema: ({
    title,
    description,
    path,
    prefecture,
    city,
    therapists,
  }: {
    title: string;
    description: string;
    path: string;
    prefecture: string;
    city: string;
    therapists: ITherapistItem[];
  }) => {
    const pathSplit = path.split('?');
    return {
      webPage: {
        '@context': 'http://schema.org',
        '@type': 'WebPage',
        '@id': `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}#webpage`,
        url: `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}`,
        inLanguage: 'ja',
        name: title,
        isPartOf: {
          '@id': 'https://hogugu.com#website',
        },
        description,
        mainEntity: [
          {
            '@id': `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}#service`,
          },
          {
            '@id': `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}#itemlist`,
          },
        ],
      },
      service: {
        '@context': 'http://schema.org',
        '@type': 'Service',
        '@id': `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}#service`,
        url: `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}`,
        provider: {
          '@id': 'https://hogugu.com/about.html#organization',
        },
        name: title,
        description,
        areaServed: {
          '@type': 'Place',
          address: {
            '@type': 'PostalAddress',
            addressCountry: 'JP',
            addressRegion: prefecture,
            addressLocality: city,
          },
        },
      },
      itemList: {
        '@context': 'http://schema.org',
        '@type': 'ItemList',
        '@id': `${process.env.NEXT_PUBLIC_DOMAIN}${pathSplit[0]}#itemlist`,
        itemListElement: therapists.map((therapist, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          url: `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist._id}`,
        })),
      },
    };
  },
  therapistListSchema: {
    webPage: {
      '@context': 'http://schema.org',
      '@type': 'WebPage',
      '@id': `${process.env.NEXT_PUBLIC_DOMAIN}/therapist#webpage`,
      url: `${process.env.NEXT_PUBLIC_DOMAIN}/therapist`,
      inLanguage: 'ja',
      name: '日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】のセラピスト一覧',
      isPartOf: {
        '@id': 'https://hogugu.com#website',
      },
      description:
        '日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、ご予約可能なセラピストを随時掲載しています。',
    },
  },
  therapistDetailSchema: (therapist?: ITherapistItem) => {
    const title = `${therapist?.nickName} | HOGUGU（ホググ）`;
    const description = `日本最大級の出張リラクゼーションサービス【HOGUGU（ホググ）】では、${therapist?.nickName}のご予約を受付しています。`;
    return {
      webPageList: {
        '@context': 'http://schema.org',
        '@type': 'WebPage',
        '@id': 'https://booking.hogugu.com/therapist#webpage',
        url: 'https://booking.hogugu.com/therapist',
        inLanguage: 'ja',
        name: 'セラピスト一覧',
        isPartOf: {
          '@id': 'https://hogugu.com#website',
        },
        description,
      },
      webPage: {
        '@context': 'http://schema.org',
        '@type': 'WebPage',
        '@id': `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist?._id}#webpage`,
        url: `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist?._id}`,
        name: title,
        isPartOf: {
          '@id': 'https://booking.hogugu.com/therapist#webpage',
        },
        description,
        mainEntity: {
          '@id': `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist?._id}#person`,
        },
      },
      person: {
        '@context': 'http://schema.org',
        '@type': 'Person',
        '@id': `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist?._id}#person`,
        url: `${process.env.NEXT_PUBLIC_DOMAIN}/therapist/${therapist?._id}`,
        name: title,
        description,
        jobTitle: {
          '@type': 'DefinedTerm',
          name: 'Therapist',
        },
        gender: GENDER[therapist?.gender || ''],
        workLocation: {
          '@type': 'PostalAddress',
          addressRegion: therapist?.departurePoint
            ? therapist?.departurePoint[0]?.name || ''
            : '',
        },
        image: {
          '@type': 'ImageObject',
          url: therapist?.profilePicture?.url || '',
        },
      },
    };
  },
  therapistReviewListSchema: (therapist?: ITherapistItem) => {
    const rating = calculateRating(therapist?.summaryReview);
    return {
      webPage: {
        '@context': 'https://schema.org',
        '@type': 'LocalBusiness',
        name: '出張リラクゼーション',
        address: {
          '@type': 'PostalAddress',
          addressLocality: therapist?.departurePoint[1]?.name || '',
          addressRegion: therapist?.departurePoint[0]?.name || '',
          addressCountry: 'JP',
          employee: {
            '@type': 'Person',
            name: therapist?.nickName || '',
            jobTitle: 'セラピスト',
          },
        },
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: rating,
          reviewCount: therapist?.summaryReview?.sumReviewer || 0,
        },
      },
    };
  },
};

export default seoConfig;
