const fs = require('fs');
const path = require('path');
const dayjs = require('dayjs');
const timezone = require('dayjs/plugin/timezone');
const utc = require('dayjs/plugin/utc');
require('dotenv').config();

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
/**
 * Add XML stylesheet directive and enhance sitemap files
 */
const reformatSitemapFiles = () => {
  const publicDir = path.join(process.cwd(), 'public');

  // Find all sitemap XML files in the public directory
  const sitemapFiles = fs
    .readdirSync(publicDir)
    .filter((file) => file.startsWith('sitemap') && file.endsWith('.xml'));

  console.log(`Found ${sitemapFiles.length} sitemap files to process...`);

  // Current date for lastmod fields
  const currentDate = dayjs().toISOString();
  const now = dayjs().tz('Asia/Tokyo').format('YYYY年M月D日 h:mm A');

  // Process each sitemap file
  sitemapFiles.forEach((file) => {
    const filePath = path.join(publicDir, file);
    let content = fs.readFileSync(filePath, 'utf8');

    // Determine the sitemap type for the stylesheet query parameter
    const sitemapType = file === 'sitemap.xml' ? 'root' : 'post';

    // Add the XML stylesheet directive right after the XML declaration
    if (!content.includes('<?xml-stylesheet')) {
      content = content.replace(
        '<?xml version="1.0" encoding="UTF-8"?>',
        `<?xml version="1.0" encoding="UTF-8"?>\n<?xml-stylesheet type="text/xsl" href="/default.xsl?sitemap=${sitemapType}"?>`
      );

      // Insert meta tag right after the root opening tag
      content = content.replace(
        /(<urlset[^>]*>|<sitemapindex[^>]*>)/,
        `$1\n  <meta>
        <now><![CDATA[${now}]]></now>
        <domain><![CDATA[${process.env.NEXT_PUBLIC_DOMAIN}]]></domain>
      </meta>`
      );
    }

    // For sitemap.xml, add lastmod to each sitemap entry if not exists
    if (file === 'sitemap.xml') {
      content = content.replace(
        /<sitemap>[\s\n]*<loc>([^<]+)<\/loc>[\s\n]*(<\/sitemap>)/g,
        `<sitemap>\n    <loc><![CDATA[$1]]></loc>\n    <lastmod><![CDATA[${currentDate}]]></lastmod>\n  $2`
      );
    }
    // For regular sitemaps, add or update lastmod and wrap loc in CDATA
    else {
      // First wrap all <loc> tags with CDATA if not already
      content = content.replace(
        /<loc>([^<[]+)<\/loc>/g,
        '<loc><![CDATA[$1]]></loc>'
      );

      // Then ensure each URL has a lastmod element with CDATA
      content = content.replace(
        /<url>[\s\n]*<loc>(.+?)<\/loc>[\s\n]*(?:<lastmod>(.+?)<\/lastmod>[\s\n]*)?(?:<changefreq>(.+?)<\/changefreq>[\s\n]*)?(?:<priority>(.+?)<\/priority>[\s\n]*)?<\/url>/g,
        (match, loc, lastmod) => {
          // If lastmod exists, wrap it in CDATA if not already, otherwise add it
          let lastmodTag;
          if (lastmod) {
            if (lastmod.includes('CDATA')) {
              lastmodTag = `<lastmod>${lastmod}</lastmod>`;
            } else {
              lastmodTag = `<lastmod><![CDATA[${lastmod}]]></lastmod>`;
            }
          } else {
            lastmodTag = `<lastmod><![CDATA[${currentDate}]]></lastmod>`;
          }

          // Rebuild the URL tag with required elements
          return match.replace(
            /<url>[\s\n]*<loc>(.+?)<\/loc>[\s\n]*(?:<lastmod>(.+?)<\/lastmod>[\s\n]*)?/,
            `<url>\n    <loc>$1</loc>\n    ${lastmodTag}\n    `
          );
        }
      );
      // Ensure changefreq and priority are wrapped in CDATA if they exist
      content = content.replace(
        /<changefreq>(.+?)<\/changefreq>/g,
        '<changefreq><![CDATA[$1]]></changefreq>\n'
      );

      content = content.replace(
        /<priority>(.+?)<\/priority>/g,
        '<priority><![CDATA[$1]]></priority>'
      );
    }

    // Write the modified content back to the file
    fs.writeFileSync(filePath, content);
    console.log(`✅ Processed ${file}`);
  });

  console.log('Sitemap reformatting completed successfully!');
};

// Execute the function
reformatSitemapFiles();
