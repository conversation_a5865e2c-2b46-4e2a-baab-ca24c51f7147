definitions:
  steps:
    - step: &prepare
        name: prepare
        image: alpine
        artifacts:
          - .env.local
    - step: &build
        name: build
        image: public.ecr.aws/j9v6w7g7/auto-build-image:stable
        services:
          - docker
        caches:
          - docker
        script:
          - env > .env.local
          - auto_build aws_configure
          - auto_build docker_login
          - cat .env.local
          - auto_build
    - step: &deploy
        name: deploy
        image: public.ecr.aws/j9v6w7g7/auto-deploy-image:stable
        services:
          - docker
        script:
          - auto_deploy kubernetes
  services:
    docker:
      memory: 3072

pipelines:
  branches:
    dev:
      - step:
          <<: *build
          deployment: BuildDevelopment
          image: public.ecr.aws/j9v6w7g7/auto-build-image:latest
      - step:
          <<: *deploy
          deployment: Development
          image: public.ecr.aws/j9v6w7g7/auto-deploy-image:latest
    stg:
      - step:
          <<: *build
          deployment: BuildStaging
      - step:
          <<: *deploy
          deployment: Staging
    master:
      - step:
          <<: *build
          deployment: BuildProduction
      - step:
          <<: *deploy
          deployment: Production
          trigger: manual
